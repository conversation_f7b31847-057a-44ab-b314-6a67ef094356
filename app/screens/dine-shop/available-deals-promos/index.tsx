import React from "react"
import { FlatList, TouchableOpacity, View } from "react-native"
import DealsPromosLabel from "./deals-promos-label"
import { Text } from "app/elements/text"
import { CaretRight, StaffDiscountTag } from "ichangi-fe/assets/icons"
import { styles } from "./styles"
import { handleDate } from "app/sections/explore-staff-perk/explore-staff-perk"
import StaffPerkPromotionDetailController from "app/components/staff-perk-promotion-detail/staff-perk-promotion-detail-controller"

type DealsPromosItem = {
  id?: string
  title?: string
  campaignStartDate?: string
  campaignEndDate?: string
  [key: string]: any
}

interface AvailableDealsPromosProps {
  dealsPromos: DealsPromosItem[]
  navigation: any
  customContainerStyle?: any
  storeName?: string
}

const AvailableDealsPromos = ({ dealsPromos, navigation, customContainerStyle, storeName = "" }: AvailableDealsPromosProps) => {
  const separatorItem = () => <View style={styles.separatorItemStyle} />
  return (
    <View style={{ ...styles.container, ...customContainerStyle }}>
      <View style={styles.wrapLabel}>
        <DealsPromosLabel />
      </View>
      <FlatList
        data={dealsPromos}
        renderItem={({ item }) => (
          <AvailableDealsPromosItem item={item} navigation={navigation} storeName={storeName} />
        )}
        keyExtractor={(_, index) => index.toString()}
        contentContainerStyle={styles.contentContainerStyle}
        ItemSeparatorComponent={separatorItem}
      />
    </View>
  )
}

const AvailableDealsPromosItem = ({ item, navigation, storeName }: { item: DealsPromosItem; navigation: any; storeName?: string }) => {
  const { title, campaignStartDate, campaignEndDate } = item || ({} as DealsPromosItem)
  const handlePress = () => {
    StaffPerkPromotionDetailController.showModal(navigation, { item }, true)
  }
  return (
    <TouchableOpacity activeOpacity={0.5} onPress={handlePress}>
      <View style={styles.contentItemStyle}>
        <View style={styles.leftContentItemStyle}>
          <StaffDiscountTag />
        </View>
        <View style={styles.midContentItemStyle}>
          <Text text={title} numberOfLines={2} style={styles.titleStyle} />
          <Text text={handleDate(campaignStartDate, campaignEndDate)} style={styles.dateStyle} />
        </View>
        <View style={styles.rightContentItemStyle}>
          <CaretRight />
        </View>
      </View>
    </TouchableOpacity>
  )
}

export default AvailableDealsPromos


