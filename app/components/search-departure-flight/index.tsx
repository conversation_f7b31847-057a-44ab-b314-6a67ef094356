import React, { useCallback, useEffect, useRef, useState } from "react"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { Keyboard, Pressable, View } from "react-native"
import { useFocusEffect, useNavigation } from "@react-navigation/native"
import { Text } from "app/elements/text"
import { Cross } from "assets/icons"
import SearchBar, { SearchInputHandle } from "app/sections/search-bar/search-bar"
import { translate } from "app/i18n"
import { useDispatch, useSelector } from "react-redux"
import { SearchIndex } from "app/screens/search/tabs/searchIndex"
import { REMOTE_FLAG_VALUE } from "app/services/firebase/remote-config"
import AutocompleteSearch from "app/screens/search-v2/tabs/search-tab-flights/components/autocomplete-search"
import { FlightDirection } from "app/utils/constants"
import { useModal } from "app/hooks/useModal"
import SearchActions, { SearchSelectors } from "app/redux/searchRedux"
import { debounce } from "lodash"
import { NavigationConstants } from "app/utils/constants"
import moment from "moment"
import styles from './styles'

const COMPONENT_NAME = "SearchDepartureFlight_"

const SearchDepartureFlight = (props) => {
  const dispatch = useDispatch()
  const navigation = useNavigation()
  const lastSearchKeyword = useSelector(SearchSelectors.lastSearchKeyword)
  const [keySearch, setKeySearch] = useState("")
  const searchRef = useRef<SearchInputHandle>(null)
  const shouldOpenModalRef = useRef(false)
  const { isModalVisible, closeModal, openModal } = useModal("searchDepartureFlight")

  useEffect(() => {
    if (isModalVisible) {
      searchRef.current?.setInputValue(lastSearchKeyword)
    }
  }, [isModalVisible])

  useFocusEffect(
    useCallback(() => {
      if (shouldOpenModalRef.current) {
        openModal()
        shouldOpenModalRef.current = false
      }
    }, [openModal]),
  )

  const navigateToGlobalSearch = (keyword: string) => {
    const params = {
      screen: SearchIndex.flights,
      keyword,
      direction: FlightDirection.Departure,
      date: moment(props?.displayTimestamp),
    }

    if (props.isPushNavigation && "push" in navigation && typeof navigation.push === "function") {
      ;(navigation as any).push(NavigationConstants.search, params)
    } else {
      ;(navigation as any).navigate(NavigationConstants.search, params)
    }
  }

  const onClosedSheet = () => {
    props?.handleOnClose?.()
    handleClearSearch()
    closeModal()
  }

  const onGetAutoCompleteKeyword = useCallback(
    (newKeyword: string) => {
      dispatch(SearchActions.getAutoCompleteFlightRequest(newKeyword.trim()))
    },
    [dispatch],
  )

  const onDebounceKeySearch = useCallback(debounce(onGetAutoCompleteKeyword, 200), [
    onGetAutoCompleteKeyword,
  ])

  const handleSearchKeywordChange = useCallback(
    (newKeyword: string) => {
      if (newKeyword.trim().length > 1) {
        onDebounceKeySearch(newKeyword)
      }
      setKeySearch(newKeyword)
    },
    [onDebounceKeySearch],
  )

  const handleClearSearch = () => {
    dispatch(SearchActions.resetAutoCompleteFlight())
  }

  const onPressAutoCompleteItem = (item) => {
    shouldOpenModalRef.current = true
    setKeySearch(item.name)
    dispatch(SearchActions.setLastSearchKeyword(item.name))
    props?.setRestoreSearchFlightsResult?.()
    closeModal()
    navigateToGlobalSearch(item.name)
  }

  const onSubmitKeyword = () => {
    Keyboard.dismiss()
    shouldOpenModalRef.current = true
    dispatch(SearchActions.setLastSearchKeyword(keySearch))
    props?.setRestoreSearchFlightsResult?.()
    closeModal()
    navigateToGlobalSearch(keySearch)
  }

  return (
    <BottomSheet
      isModalVisible={isModalVisible}
      onClosedSheet={onClosedSheet}
      stopDragCollapse
      onBackPressHandle={onClosedSheet}
      containerStyle={styles.container}
      animationInTiming={300}
      animationOutTiming={300}
      openPendingModal
    >
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle} tx="searchV2.flightsTab.DEP.title" />
        <View style={styles.headerIcon}>
          <Cross width={24} height={24} onPress={onClosedSheet} />
        </View>
      </View>

      <Pressable style={styles.contentContainer} onPress={Keyboard.dismiss}>
        <SearchBar
          useInputFieldV2
          searchAutoCompleteFlag={REMOTE_FLAG_VALUE.ON}
          containerStyle={styles.searchBar}
          inputContainerStyle={styles.searchInput}
          inputProps={{
            placeholder: translate("searchV2.placeHolder.flights1"),
            placeholderList: [
              translate("searchV2.placeHolder.flights1"),
              translate("searchV2.placeHolder.flights2"),
            ],
          }}
          isShowBack={false}
          tab={SearchIndex.flights}
          keyword={keySearch}
          onChangeKeyword={handleSearchKeywordChange}
          onSearchClear={handleClearSearch}
          onSubmitLocal={onSubmitKeyword}
          testID={`${COMPONENT_NAME}SearchBar`}
          accessibilityLabel={`${COMPONENT_NAME}SearchBar`}
          returnKeyType="previous"
          ref={searchRef}
        />
        <AutocompleteSearch
          containerStyle={styles.autocompleteContainerStyle}
          keySearch={keySearch.trim()}
          flightType={FlightDirection.Departure}
          handleItemOnPress={onPressAutoCompleteItem}
          selector={SearchSelectors.autoCompleteFlightList}
        />
      </Pressable>
    </BottomSheet>
  )
}

export default SearchDepartureFlight
