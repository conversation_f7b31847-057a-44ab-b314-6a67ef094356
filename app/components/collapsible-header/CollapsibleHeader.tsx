import React, { ReactNode, useCallback, useMemo } from "react"
import {
  View,
  Text,
  ListRenderItem,
  FlatListProps,
  FlatList,
  Image,
  TouchableOpacity,
  Platform,
  StatusBar,
} from "react-native"
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  interpolateColor,
  useAnimatedProps,
  runOnJS,
  useAnimatedReaction,
} from "react-native-reanimated"
import { useFocusEffect } from "@react-navigation/native"
import Svg, { Path } from "react-native-svg"
import { FILTER_BORDER_RADIUS, styles } from "./styles"
import { color } from "app/theme"

import { ALPHABETICAL_INDEX_LIST } from "app/sections/deals-promos-category-listing/deals-promos-category-listing.constants"
import { SortBy } from "app/sections/deals-promos/filter-bottom-sheet/filter-bottom-sheet.constants"
import { AlphabeticalIndex } from "app/components/alphabetical-index"

const AnimatedFlatList = Animated.createAnimatedComponent(FlatList)
const AnimatedPath = Animated.createAnimatedComponent(Path)
const AnimatedView = Animated.createAnimatedComponent(View)
const AnimatedText = Animated.createAnimatedComponent(Text)

const AnimatedBackArrow = ({ animatedProps }) => {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24">
      <AnimatedPath
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.29289 11.2929C4.10536 11.4804 4 11.7348 4 12C4 12.2652 4.10536 12.5196 4.29289 12.7071L9.29289 17.7071C9.68342 18.0976 10.3166 18.0976 10.7071 17.7071C11.0976 17.3166 11.0976 16.6834 10.7071 16.2929L7.41421 13L18 13C18.5523 13 19 12.5523 19 12C19 11.4477 18.5523 11 18 11L7.41421 11L10.7071 7.70711C11.0976 7.31658 11.0976 6.68342 10.7071 6.29289C10.3166 5.90237 9.68342 5.90237 9.29289 6.29289L4.29289 11.2929Z"
        animatedProps={animatedProps}
      />
    </Svg>
  )
}

import { DealsPromosLoading } from "app/sections/deals-promos-category-listing/deals-promos-loading/deals-promos-loading-content"

export const BACKGROUND_IMAGE_HEIGHT = 115

interface CollapsibleHeaderProps<T> extends Omit<FlatListProps<T>, "renderItem"> {
  headerImageSource: any
  headerTitle: string
  renderItem: ListRenderItem<T>
  renderFilter: () => ReactNode
  navigation: any
  filterHeight?: number
  hasError?: boolean
  renderError?: () => ReactNode
  isLoading?: boolean
  sortBy?: SortBy
  listData?: any[]
  perkItemOffsetListRef?: any
  rootItemOffsetRef?: any
  rootListRef?: any
  customComponentLoading?: any
  nameField?: string
  isScrollToIndex?: boolean
}

const CollapsibleHeader = <T extends unknown>({
  headerImageSource,
  headerTitle,
  renderItem,
  renderFilter,
  navigation,
  filterHeight = 60,
  sortBy,
  listData,
  perkItemOffsetListRef,
  rootItemOffsetRef,
  rootListRef,
  customComponentLoading,
  nameField,
  isScrollToIndex = false,
  ...rest
}: CollapsibleHeaderProps<T>) => {
  const scrollY = useSharedValue(0)
  const prevScrollY = useSharedValue(0)
  const isScrollingUp = useSharedValue(false)
  const { hasError, renderError, isLoading } = rest

  const ANIMATION_SCROLL_DISTANCE = 45
  const headerMaxHeight = BACKGROUND_IMAGE_HEIGHT + FILTER_BORDER_RADIUS
  const headerMinHeight = 100

  const perkItemAccumulateHeights = useMemo(() => {
    if (!listData?.length || !perkItemOffsetListRef?.current) return []

    return listData.reduce((result, _item, index) => {
      let totalHeights = 0
      for (let j = 0; j < index; j++) {
        totalHeights += perkItemOffsetListRef.current[j] || 0
      }
      return result.concat(totalHeights)
    }, [])
  }, [JSON.stringify(listData), perkItemOffsetListRef?.current, Object.keys(perkItemOffsetListRef?.current || {}).length])

  const handleAlphabetIndexPress = useCallback(
    (letter: string, indexToScroll: number) => {
      if (indexToScroll === 0) {
        rootListRef?.current?.scrollToOffset({ animated: false, offset: 0 })
        return
      }
      if(isScrollToIndex) {
        const index = indexToScroll > 1 ? indexToScroll - 1 : 0
        rootListRef?.current?.scrollToIndex({index: index, animated: false})
      } else {
        const totalPerkItemHeights = perkItemAccumulateHeights[indexToScroll] || 0

        // Calculate the correct offset considering the contentContainerStyle padding
        const contentPaddingTop = headerMaxHeight + filterHeight - 4

        // Account for the filter padding to ensure item is not covered by sticky filter bar
        // Filter bar has padding (24 top + 8 bottom = 32) when in sticky mode
        const dynamicFilterPadding = 32

        // headerMinHeight is the height of the header when scrolled
        // filterHeight is the base height of the filter bar
        // dynamicFilterPadding accounts for the extra padding added when scrolled
        const stickyHeaderHeight = headerMinHeight + filterHeight + dynamicFilterPadding
        // Add extra padding to ensure the item is fully visible below the sticky header
        const extraPadding = 16
        const offset = totalPerkItemHeights + contentPaddingTop - stickyHeaderHeight + extraPadding

        rootListRef?.current?.scrollToOffset({ animated: false, offset, viewPosition: 0 })
      }
    },
    [perkItemAccumulateHeights, rootListRef, headerMaxHeight, filterHeight, headerMinHeight],
  )

  const handleAlphabetTracking = useCallback((letter: string) => {
    // TODO: Add tracking for alphabetical index
    console.log('handleAlphabetTracking', letter)
  }, [])

  const setBarStyle = useCallback((style: "light-content" | "dark-content") => {
    StatusBar.setBarStyle(style, true)
  }, [])

  useAnimatedReaction(
    () => scrollY.value > ANIMATION_SCROLL_DISTANCE,
    (result, previous) => {
      if (result !== previous) {
        runOnJS(setBarStyle)(result ? "dark-content" : "light-content")
      }
    },
    [],
  )

  useFocusEffect(
    useCallback(() => {
      setBarStyle("light-content")
      if (Platform.OS === "android") {
        StatusBar.setTranslucent(true)
        StatusBar.setBackgroundColor("transparent", true)
      }

      return () => {
        setBarStyle("dark-content")
        if (Platform.OS === "android") {
          StatusBar.setTranslucent(false)
          StatusBar.setBackgroundColor(color.background, true)
        }
      }
    }, [setBarStyle]),
  )

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      const currentScrollY = event.contentOffset.y
      isScrollingUp.value = currentScrollY < prevScrollY.value
      prevScrollY.value = currentScrollY
      scrollY.value = currentScrollY
    },
  })

  const headerImageAnimatedStyle = useAnimatedStyle(() => {
    const height = interpolate(
      scrollY.value,
      [-headerMaxHeight, 0],
      [headerMaxHeight * 2, headerMaxHeight],
      Extrapolate.CLAMP,
    )
    const translateY = interpolate(
      scrollY.value,
      [0, ANIMATION_SCROLL_DISTANCE],
      [0, -ANIMATION_SCROLL_DISTANCE],
      Extrapolate.CLAMP,
    )
    return {
      height,
      transform: [{ translateY }],
    }
  })

  const headerBarAnimatedStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      scrollY.value,
      [0, ANIMATION_SCROLL_DISTANCE],
      ["transparent", color.palette.whiteGrey],
    )
    const shadowOpacity = interpolate(
      scrollY.value,
      [0, ANIMATION_SCROLL_DISTANCE],
      [0, Platform.OS === "android" ? 0.16 : 1],
      Extrapolate.CLAMP,
    )
    const elevation = interpolate(
      scrollY.value,
      [-1, 0, ANIMATION_SCROLL_DISTANCE],
      [0, 0, 2],
      Extrapolate.CLAMP,
    )
    return {
      backgroundColor,
      elevation,
      shadowOpacity,
    }
  })

  const titleAnimatedStyle = useAnimatedStyle(() => {
    const textColor = interpolateColor(
      scrollY.value,
      [0, ANIMATION_SCROLL_DISTANCE],
      [color.palette.whiteGrey, color.palette.darkestGrey],
    )
    return {
      color: textColor,
    }
  })

  const filterContainerAnimatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scrollY.value,
      [0, ANIMATION_SCROLL_DISTANCE],
      [headerMaxHeight, headerMaxHeight - ANIMATION_SCROLL_DISTANCE],
      Extrapolate.CLAMP,
    )

    let paddingTop = 0
    let paddingBottom = 0

    if (!isScrollingUp.value) {
      // Scroll down
      paddingTop = scrollY.value >= ANIMATION_SCROLL_DISTANCE ? 24 : 0
      paddingBottom = scrollY.value >= ANIMATION_SCROLL_DISTANCE ? 8 : 0
    } else {
      // Scroll up
      paddingTop = scrollY.value < 32 ? 0 : 24
      paddingBottom = scrollY.value < 32 ? 0 : 8
    }

    return {
      height: filterHeight + paddingTop + paddingBottom,
      paddingTop,
      paddingBottom,
      transform: [{ translateY }],
    }
  })

  const backArrowProps = useAnimatedProps(() => {
    const fill = interpolateColor(
      scrollY.value,
      [0, ANIMATION_SCROLL_DISTANCE],
      [color.palette.whiteGrey, color.palette.darkestGrey],
    )
    return {
      fill,
    }
  }, [scrollY.value])

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.headerImageContainer, headerImageAnimatedStyle]}>
        <Image
          source={headerImageSource}
          style={[styles.backgroundImage, { height: headerMaxHeight }]}
          resizeMode="cover"
        />
      </Animated.View>

      {isLoading ? (
        <>
          {customComponentLoading ? customComponentLoading :<DealsPromosLoading
            containerStyle={{
              marginTop: BACKGROUND_IMAGE_HEIGHT,
              paddingTop: 80,
              borderTopLeftRadius: FILTER_BORDER_RADIUS,
              borderTopRightRadius: FILTER_BORDER_RADIUS,
              height: "100%",
              overflow: "hidden",
              backgroundColor: color.palette.whiteGrey,
            }}
          />}
        </>
      ) : hasError && renderError ? (
        renderError()
      ) : (
        <AnimatedFlatList
          {...rest}
          ref={rootListRef}
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          renderItem={renderItem}
          contentContainerStyle={{ paddingTop: headerMaxHeight + filterHeight - 4 }}
        />
      )}

      <AnimatedView
        style={[
          styles.headerBar,
          { height: headerMinHeight, paddingTop: 45 },
          headerBarAnimatedStyle,
        ]}
      >
        <TouchableOpacity onPress={() => navigation?.goBack()} style={styles.backBtn}>
          <AnimatedBackArrow animatedProps={backArrowProps} />
        </TouchableOpacity>
        <AnimatedText style={[styles.title, titleAnimatedStyle]}>{headerTitle}</AnimatedText>
        <View style={styles.placeholder} />
      </AnimatedView>

      <Animated.View style={[styles.filterContainer, filterContainerAnimatedStyle]}>
        {renderFilter()}
      </Animated.View>

      {/* Alphabetical index search bar */}
      <AlphabeticalIndex
        data={listData || []}
        nameField={nameField ? nameField : "tenantName"}
        alphabetList={ALPHABETICAL_INDEX_LIST}
        onIndexPress={handleAlphabetIndexPress}
        itemAccumulateHeights={perkItemAccumulateHeights}
        visible={sortBy === SortBy.AZ && !!listData?.length}
        onTrackingAction={handleAlphabetTracking}
      />
    </View>
  )
}

export default CollapsibleHeader
