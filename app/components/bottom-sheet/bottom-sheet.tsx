import React from "react"
import { View } from "react-native"
import Modal from "react-native-modal"
import { BottomSheetProps } from "./bottom-sheet.props"
import * as styles from "./bottom-sheet.styles"
import { useDispatch } from "react-redux"
import ModalManagerActions from "app/redux/modalManagerRedux"
import { Animation } from "react-native-animatable"

const DEFAULT_ANIMATION_TIMING = 1000

const BottomSheet = (props: BottomSheetProps) => {
  const {
    stopDragCollapse,
    isModalVisible,
    onClosedSheet,
    containerStyle,
    animationInTiming,
    animationOutTiming,
    onModalShow,
    onModalWillShow,
    animationIn = Animation.slideInUp,
    animationOut = Animation.slideOutDown,
    onModalHide,
    modalStyle,
    openPendingModal,
  } = props

  const dispatch = useDispatch()

  return (
    <Modal
      navigationBarTranslucent={true}
      isVisible={isModalVisible}
      onBackdropPress={() => onClosedSheet()}
      onSwipeComplete={() => onClosedSheet()}
      swipeDirection={stopDragCollapse ? null : "down"}
      animationInTiming={animationInTiming || DEFAULT_ANIMATION_TIMING}
      animationOutTiming={animationOutTiming || DEFAULT_ANIMATION_TIMING}
      backdropTransitionInTiming={animationInTiming || DEFAULT_ANIMATION_TIMING}
      backdropTransitionOutTiming={animationOutTiming || DEFAULT_ANIMATION_TIMING}
      style={[styles.modalStyle, modalStyle]}
      onBackButtonPress={props.onBackPressHandle ? onClosedSheet : null}
      hideModalContentWhileAnimating={true}
      useNativeDriver={true}
      onModalShow={() => onModalShow && onModalShow()}
      onModalHide={() => {
        onModalHide && onModalHide()
        if(openPendingModal){
          dispatch(ModalManagerActions.openPendingModal())
        }
      }}
      onModalWillShow={() => onModalWillShow && onModalWillShow()}
      statusBarTranslucent
      animationIn={animationIn}
      animationOut={animationOut}
    >
      <View style={containerStyle || styles.containerStyle}>{props.children}</View>
    </Modal>
  )
}

export default BottomSheet
