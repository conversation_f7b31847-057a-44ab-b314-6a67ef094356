import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect, useMemo } from "react"
import {
  View,
  TouchableOpacity,
  ScrollView,
  ViewStyle,
  StatusBar,
  Platform,
  ImageBackground,
} from "react-native"
import { Text } from "app/elements/text/text"
import { useDispatch, useSelector } from "react-redux"
import ChangiRewardsActions from "app/redux/changiRewardsRedux"
import { color } from "app/theme"
import { isEmpty, get } from "lodash"
import { styles, systemFonts, tagStyle, tagStyle2, width, tagStyle2V2 } from "./staff-perk-promotion-detail-modal.styles"
import {
  CaretRight,
  CrossPurpleWithBgr,
  IShopChangiPromoDetail,
  StaffPerkOpenWebView,
  StaffPerkOpenWebViewV2,
  StaffPerkTag,
} from "ichangi-fe/assets/icons"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { LoadingOverlay } from "app/components/loading-modal"
import { AdobeTagName, commonTrackingScreen, getExperienceCloudId, trackAction } from "app/services/adobe"
import { NavigationConstants, StateCode } from "app/utils/constants"
import NetInfo from "@react-native-community/netinfo"
import { handleCondition, joinTexts, mappingUrlAem } from "app/utils"
import StaffPerkPromotionDetailController, {
  StaffPerkPromotionDetailRef,
} from "./staff-perk-promotion-detail-controller"
import Modal from "react-native-modal"
import StaffPerkCreators, { StaffPerkSelectors } from "app/redux/staffPerkRedux"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import BaseImageBackground from "app/elements/base-image-background/base-image-background"
import BaseImage from "app/elements/base-image/base-image"
import CollapsibleComponent from "../collapsible/collapsible"
import RenderHTML from "react-native-render-html"
import { formatHtmlContent } from "app/utils/html/html-content"
import { StaffPerkScan } from "ichangi-fe/assets/backgrounds"
import { AemSelectors, AEM_PAGE_NAME } from "app/redux/aemRedux"
import { ForYouSelectors } from "app/redux/forYouRedux"
import { Tier } from "../changi-rewards-member-card"
import ChangiRewardCardInsidePromo from "./changi-reward-card-inside-promo"
import { REMOTE_CONFIG_FLAGS, getFeatureFlagInit, isFlagON } from "app/services/firebase/remote-config"
import { getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import { getISCInputParamsDeepLink, getIsISCLinkDomain } from "app/helpers/deeplink/deeplink-parameter"
import { WebViewHeaderTypes } from "app/models/enum"
import { getViewerUID } from "app/utils/screen-helper"
import { getPreviousScreen, useCurrentScreenActiveAndPreviousScreenHook } from "app/utils/screen-hook"
import { NavigationAATag, NavigationPageSource, NavigationValueDeepLink, useHandleNavigation } from "app/utils/navigation-helper"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import { checkLoginState } from "app/utils/authentication"
import Animated, { useSharedValue, useAnimatedStyle, interpolate, useAnimatedScrollHandler } from 'react-native-reanimated';
import { HeaderAnimated } from './header-animated';
import { useCPay } from "app/helpers/changipay"
import { CTALinkUrl, ModalAction, TaskCode } from "./staff-perk-promotion-detail-modal.constants"
import { LoadingV2 } from "./loadingV2"
import { ViewError } from "./view-error"
import { useRewardTier } from "app/hooks/useRewardTier"

const StaffPerkPromotionDetailModal = ({navigationRef}) => {
  const {memberIconInfo} = useRewardTier()
  const CRCardIcon = memberIconInfo?.crCardIcon
  const navigationRoute = navigationRef?.current?.getCurrentRoute?.()
  const isShopDineV2 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_V2)
  const modalRef = useRef<StaffPerkPromotionDetailRef>(null)
  const scrollY = useSharedValue(0);
  const heightBannerModal =  useSharedValue(0);
  const positionTitle =  useSharedValue(0);
  const dispatch = useDispatch()
  const [newNavigation, setNewNavigation] = useState<any>()
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const [isNoInternet, setIsNoInternet] = useState(false)
  const [isShowWithoutImage, setIsShowWithoutImage] = useState(false)
  const [showHeaderAnimated, setShowHeaderAnimated] = useState(false)

  const [modalVisible, setModalVisible] = useState(false)
  const isModalOpenRef = useRef(false)

  const [itemSelected, setItemSelected] = useState(null)
  const [action, setAction] = useState(null)

  const staffPerkPromotionDetailRequest = useSelector(
    StaffPerkSelectors.staffPerkPromotionDetailRequest,
  )
  const staffPerkPromotionDetailPayload = useSelector(
    StaffPerkSelectors.staffPerkPromotionDetailPayload,
  )

  const staffPerkPromotionDetailError = useSelector(
    StaffPerkSelectors.staffPerkPromotionDetailError,
  )

  const dataCommonAEM = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.AEM_COMMON_DATA))
  const tierIconsAEM = get(dataCommonAEM, "data.pageLanding.explore.tierIcons")
  const rewardsData = useSelector(ForYouSelectors.rewardsData)
  const changiRewardCardInsidePromo = useRef(null)
  const { handleNavigation } = useHandleNavigation("STAFF_PERK_PROMOTION_DETAIL_MODAL")
  const {openChangiPay} = useCPay()

  const isTenantIdIsAnInteger = /^(?!0)\d+$/.test(staffPerkPromotionDetailPayload?.tenantId)
  const shouldHidePromoAvaibility = !isTenantIdIsAnInteger && isEmpty(staffPerkPromotionDetailPayload?.iShopChangiUrl)
  const promoAvaibilityStyle = shouldHidePromoAvaibility && { marginTop: 24, borderBottomColor: "transparent" }
  const iShopChangiAvailabilityStyle = [styles.physicalTenantContentStyle, {paddingBottom: 16, borderBottomWidth: 0}]

  const mappingTierCode = (tierCode, arrTier) => {
    if (tierCode === Tier.StaffGold) {
      return arrTier?.find((tierIconElement) => tierIconElement.tier === Tier.Gold)
    } else if (tierCode === Tier.StaffMember) {
      return arrTier?.find((tierIconElement) => tierIconElement.tier === Tier.Member)
    } else if (tierCode === Tier.StaffPlatinum) {
      return arrTier?.find((tierIconElement) => tierIconElement.tier === Tier.Platinum)
    } else if (tierCode === Tier.StaffMonarch) {
      return arrTier?.find((tierIconElement) => tierIconElement.tier === Tier.Monarch)
    }
    return arrTier?.find((tierIconElement) => tierIconElement.tier === tierCode)
  }

  const transformTierIcon = (rewardsDataParams, isLoggedInParams, tierIconsAEMParams) => {
    const tier = isLoggedInParams
      ? rewardsDataParams?.reward?.currentTierInfo?.replace(" ", "")
      : "Non-logged-in"
    const tierAEM = mappingTierCode(tier, tierIconsAEMParams)
    return mappingUrlAem(tierAEM?.icon)
  }

  const tierIcon = useMemo(() => transformTierIcon(rewardsData, true, tierIconsAEM), [
    rewardsData,
    tierIconsAEM,
    true,
  ])

  useEffect(() => {
    StaffPerkPromotionDetailController.setModalRef(modalRef)
  }, [])

  const inset = useSafeAreaInsets()

  const modalStyle: ViewStyle = {
    marginHorizontal: 0,
    marginBottom: 0,
    marginTop: Platform.select({
      ios: 60,
      android: handleCondition(inset?.top, inset?.top + 5, 25),
    }),
    overflow: "hidden",
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: color.palette.whiteGrey,
  }

  useImperativeHandle(
    modalRef,
    () => ({
      show: (nav: any, item: any, showWithoutImage: boolean) => {
        setNewNavigation(nav)
        setItemSelected(item)
        setModalVisible(true)
        setIsShowWithoutImage(!!showWithoutImage)
        isModalOpenRef.current = true
        dispatch(StaffPerkCreators.setStaffPerkPromotionDetailModalOpenning(true))
      },
      hide: () => {
        setModalVisible(false)
        isModalOpenRef.current = false
        dispatch(StaffPerkCreators.setStaffPerkPromotionDetailModalOpenning(false))
      },
      setActionWhenCloseModal: (_nav: any, actionType: any) => {
        setAction(actionType)
      },
    }),
    [],
  )

  const checkConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    return isConnected
  }

  useCurrentScreenActiveAndPreviousScreenHook("StaffPerkPromotion_Ecard")

  useEffect(() => {
    const unsubscribeFocus = newNavigation?.addListener("focus", () => {
      commonTrackingScreen(
        "StaffPerkPromotion_Ecard",
        getPreviousScreen(),
        isLoggedIn,
      )
    })
    return unsubscribeFocus
  }, [])


  useEffect(() => {
    if (!isEmpty(itemSelected)) {
      handleFetchData()
    }
  }, [itemSelected])

  const handleFetchData = () => {
    checkConnection().then((res) => {
      if (res) {
        if (isNoInternet) {
          setIsNoInternet(false)
        }
        const params = {
          promo_id: itemSelected?.item?.id,
        }
        dispatch(StaffPerkCreators.getStaffPerkPromotionDetailRequest(params))
      } else {
        setIsNoInternet(true)
      }
    })
  }

  const closeScreen = () => {
    scrollY.value = 0
    heightBannerModal.value = 0
    positionTitle.value = 0
    dispatch(ChangiRewardsActions.setChangiECardModalOpenning(false))
    StaffPerkPromotionDetailController.hideModal()
  }

  const resetState = () => {
    setAction(null)
  }

  const TRANSITION_TIMING = 200

  const openWebView = async () => {
    const UID = await getViewerUID({ shouldReturnNull: true })
    trackAction(AdobeTagName.CAppGalaxyDetail, {
      [AdobeTagName.CAppGalaxyDetail]: `${staffPerkPromotionDetailPayload?.title} | ${staffPerkPromotionDetailPayload?.linkLabel} | ${UID}`,
    })
    setAction(ModalAction.OPEN_WEBVIEW)
    closeScreen()
  }

  const openTenantDetail = async () => {
    const UID = await getViewerUID({ shouldReturnNull: true })
    trackAction(AdobeTagName.CAppGalaxyDetail, {
      [AdobeTagName.CAppGalaxyDetail]: `${staffPerkPromotionDetailPayload?.title} | PROMO AVAILABILITY | ${staffPerkPromotionDetailPayload?.tenantName} | ${UID}`,
    })
    if (staffPerkPromotionDetailPayload?.tenantType === "dine") {
      setAction(ModalAction.OPEN_DINE_DETAIL)
    } else if (staffPerkPromotionDetailPayload?.tenantType === "shop") {
      setAction(ModalAction.OPEN_SHOP_DETAIL)
    }
    closeScreen()
  }

  const openIShopChangi = async () => {
    const UID = await getViewerUID({ shouldReturnNull: true })
    trackAction(AdobeTagName.CAppGalaxyDetail, {
      [AdobeTagName.CAppGalaxyDetail]: `${staffPerkPromotionDetailPayload?.title} | PROMO AVAILABILITY | iShopChangi | ${UID}`,
    })
    setAction(ModalAction.OPEN_ISHOPCHANGI)
    closeScreen()
  }

  const handleTag = () => {
    const firstItem = (
      <TouchableOpacity key={`tagPromo-`} activeOpacity={1}>
        <StaffPerkTag />
      </TouchableOpacity>
    )
    const content = [firstItem]
    if (!isEmpty(staffPerkPromotionDetailPayload?.tags)) {
      staffPerkPromotionDetailPayload?.tags.forEach((e, index) => {
        if (e?.toLowerCase() !== "staff perks") {
          content.push(
            <TouchableOpacity key={`tagPromo-${index}`} style={styles.tagPromo} activeOpacity={1}>
              <Text text={e} style={styles.textTagPromo} />
            </TouchableOpacity>,
          )
        }
      })
    }
    return content
  }
  const processChangiPay = () => {
    if (Platform.OS === "ios") {
      setTimeout(() => {
        openChangiPay()
      }, 200)
    } else {
      openChangiPay()
    }
  }

  const handleNavigateCSMIShopchangi = async(url: string) => {
    const ecid = await getExperienceCloudId()
    const target = getISCInputParamsDeepLink(url)
    const payload = {
      stateCode: StateCode.ISHOPCHANGI_PAGELINK,
      input: {
        ...target,
        ecid,
      },
    }
    try {
      const response = await getDeepLinkV2(payload, true)
      if (response?.redirectUri) {
        newNavigation.navigate(NavigationConstants.playpassWebview, {
          uri: response?.redirectUri,
          needBackButton: true,
          needCloseButton: true,
          headerType: WebViewHeaderTypes.default,
          basicAuthCredential: response?.basicAuth,
        })
      } else {
        newNavigation.navigate(NavigationConstants.webview, {
          uri: url,
        })
      }
    } catch (error) {
      newNavigation.navigate(NavigationConstants.webview, {
        uri: url,
      })
    }
  }

  const callbackWhenClose = () => {
    switch (action) {
      case ModalAction.OPEN_WEBVIEW:
        const isIscDomain = getIsISCLinkDomain(staffPerkPromotionDetailPayload?.linkUrl)
        if (staffPerkPromotionDetailPayload?.linkUrl === CTALinkUrl.MiffyBirthdayBash) {
          handleNavigation(NavigationTypeEnum.deepLink, NavigationValueDeepLink.missionpass, {
            aaTag: joinTexts([NavigationAATag.offerDetails, staffPerkPromotionDetailPayload?.title]),
            isLoggedInAtTriggerTime: checkLoginState(),
            pageSource: NavigationPageSource.staffPerkPromotionDetailModal,
            taskCode: TaskCode.StaffPerks,
            utmCampaign: "miffybirthdaybash25",
          })
        } else if (staffPerkPromotionDetailPayload?.linkUrl === CTALinkUrl.SpaceAppxplorer) {
          handleNavigation(NavigationTypeEnum.deepLink, NavigationValueDeepLink.missionpass, {
            aaTag: joinTexts([NavigationAATag.offerDetails, staffPerkPromotionDetailPayload?.title]),
            isLoggedInAtTriggerTime: checkLoginState(),
            pageSource: NavigationPageSource.staffPerkPromotionDetailModal,
            taskCode: TaskCode.StaffPerks,
          })
        } else if (isIscDomain) {
          handleNavigateCSMIShopchangi(staffPerkPromotionDetailPayload?.linkUrl)
        } else {
          newNavigation.navigate(NavigationConstants.webview, {
            uri: staffPerkPromotionDetailPayload?.linkUrl,
          })
        }
        break
      case ModalAction.OPEN_DINE_DETAIL:
        newNavigation.navigate(NavigationConstants.restaurantDetailScreen, {
          tenantId: staffPerkPromotionDetailPayload?.tenantId,
          tenantName: staffPerkPromotionDetailPayload?.tenantName,
        })
        break
      case ModalAction.OPEN_SHOP_DETAIL:
        newNavigation.navigate(NavigationConstants.shopDetailsScreen, {
          tenantId: staffPerkPromotionDetailPayload?.tenantId,
          tenantName: staffPerkPromotionDetailPayload?.tenantName,
        })
        break
      case ModalAction.OPEN_ISHOPCHANGI:
        const isIscDomainiShopChangiUrl = getIsISCLinkDomain(staffPerkPromotionDetailPayload?.iShopChangiUrl)
        if (isIscDomainiShopChangiUrl) {
          handleNavigateCSMIShopchangi(staffPerkPromotionDetailPayload?.iShopChangiUrl)
        } else {
          newNavigation.navigate(NavigationConstants.webview, {
            uri: staffPerkPromotionDetailPayload?.iShopChangiUrl,
          })
        }
        break
      case ModalAction.OPEN_CHANGIPAY:
        processChangiPay()
        break
    }
  }

  const headerModal = () => {
    if (!isEmpty(staffPerkPromotionDetailPayload?.imageUrl) && !isShowWithoutImage) {
      return (
        <BaseImageBackground
          source={{ uri: staffPerkPromotionDetailPayload?.imageUrl }}
          imageStyle={styles.backgroundImage}
          resizeMode={"stretch"}
        ></BaseImageBackground>
      )
    }
    return <View style={isShopDineV2 ? styles.headerModalWithoutImageStyleV2 : styles.headerModalWithoutImageStyle} />
  }

  const openCRCard = async () => {
    const UID = await getViewerUID({ shouldReturnNull: true })
    trackAction(AdobeTagName.CAppGalaxyDetail, {
      [AdobeTagName.CAppGalaxyDetail]: `${staffPerkPromotionDetailPayload?.title} | ECard | ${UID}`,
    })
    changiRewardCardInsidePromo.current.showCard(newNavigation)
  }

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const animatedHeaderStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      scrollY.value,
      [0, (heightBannerModal.value + positionTitle.value - 30), heightBannerModal.value + positionTitle.value],
      [0, 0, 1],
    );

    return {
      opacity,
    };
  });

  const handleReload = () => {
    checkConnection().then((res) => {
      if (res) {
        if (isNoInternet) {
          setIsNoInternet(false)
        }
        const params = {
          promo_id: itemSelected?.item?.id,
        }
        dispatch(StaffPerkCreators.getStaffPerkPromotionDetailRequest(params))
      } else {
        setIsNoInternet(true)
      }
    })
  }

  useEffect(() => {
    let timeout = null
    if (!staffPerkPromotionDetailRequest) {
      timeout = setTimeout(() => setShowHeaderAnimated(true), 500)
    } else {
      setShowHeaderAnimated(false)
    }
    return () => {
      if (timeout) clearTimeout(timeout)
    }
  }, [staffPerkPromotionDetailRequest])

  const renderHeaderAnimated = () => (
    showHeaderAnimated ? (
      <HeaderAnimated animatedHeaderStyle={animatedHeaderStyle} title={staffPerkPromotionDetailPayload?.title} />
    ) : null
  )

  const renderContentModal = () => {
    if (staffPerkPromotionDetailRequest && isShopDineV2) {
      return (<LoadingV2 navigationRoute={navigationRoute} onClose={closeScreen} />)
    } else if (isNoInternet || staffPerkPromotionDetailError) {
      return (
        <ViewError isNoInternet={isNoInternet} handlePressReload={handleReload} onCloseModal={closeScreen} />
      )
    } else {
      return (
        <>
          <Animated.ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}
            onScroll={scrollHandler}
          >
            <StatusBar barStyle="light-content" />
            <View style={styles.contentModalStyle}>
              <View onLayout={event => {
                const layout = event.nativeEvent.layout;
                heightBannerModal.value = layout.height
              }}>
                {headerModal()}
              </View>
              <View style={isShopDineV2 ? styles.contentPromoV2 : styles.contentPromo}>
                {!isShopDineV2 && <View>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {handleTag()}
                  </ScrollView>
                </View>}
                <TouchableOpacity activeOpacity={1} onLayout={event => {
                  const layout = event.nativeEvent.layout;
                  positionTitle.value = layout.y + 16
                }}>
                  <Text text={staffPerkPromotionDetailPayload?.title} style={isShopDineV2 ? styles.offerTitleV2 : styles.offerTitle} />
                  <RenderHTML
                    source={{
                      html: formatHtmlContent(staffPerkPromotionDetailPayload?.description),
                    }}
                    tagsStyles={isShopDineV2 ? tagStyle2V2 : tagStyle2}
                    contentWidth={width}
                    systemFonts={systemFonts}
                  />
                </TouchableOpacity>
                {!isEmpty(staffPerkPromotionDetailPayload?.linkLabel) && (
                  <View style={styles.openWebViewSection}>
                    {isShopDineV2 ? <StaffPerkOpenWebViewV2/> : <StaffPerkOpenWebView />}
                    <TouchableOpacity onPress={openWebView}>
                      <Text
                        text={staffPerkPromotionDetailPayload?.linkLabel}
                        style={isShopDineV2 ? styles.textLinkWebViewV2 : styles.textLinkWebView}
                      />
                    </TouchableOpacity>
                  </View>
                )}
                {/* Physical Tenant */}
                {!shouldHidePromoAvaibility && <TouchableOpacity activeOpacity={1} style={isShopDineV2 ? styles.physicalTenantStyleV2 : styles.physicalTenantStyle}/>}
                <View style={promoAvaibilityStyle}>
                  {handleCondition(
                    shouldHidePromoAvaibility,
                    null,
                    <TouchableOpacity activeOpacity={1}>
                      <Text text={isShopDineV2 ? "Promo Availability" : "PROMO AVAILABILITY"} style={isShopDineV2 ? styles.promoAvailabilityTextV2 : styles.promoAvailabilityText} />
                    </TouchableOpacity>,
                  )}
                  {/* Tenant Availability */}
                  {isTenantIdIsAnInteger && (
                    <TouchableOpacity onPress={openTenantDetail} activeOpacity={0.5}>
                      <View style={[styles.physicalTenantContentStyle, { borderBottomWidth: !isEmpty(staffPerkPromotionDetailPayload?.iShopChangiUrl) ? 1 : 0 }]}>
                        <BaseImage
                          source={{ uri: staffPerkPromotionDetailPayload?.tenantIcon }}
                          style={styles.tenantIconStyle}
                          resizeMode={"stretch"}
                        />
                        <View style={styles.promoRightContentStyle}>
                          <View style={styles.headerPromoAvailableStyle}>
                            <Text
                              text={staffPerkPromotionDetailPayload?.tenantName}
                              style={styles.tenantNameTextStyle}
                            />
                            <View style={styles.caretRightStyle}>
                              <CaretRight />
                            </View>
                          </View>
                          <View style={styles.wrapLocationComponentStyle}>
                            <Text
                              text={staffPerkPromotionDetailPayload?.tenantOutlets}
                              style={styles.caption1RegOutletStyle}
                            />
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  )}

                  {/* iShopChangi Availability */}
                  {!isEmpty(staffPerkPromotionDetailPayload?.iShopChangiUrl) && (
                    <TouchableOpacity onPress={openIShopChangi} activeOpacity={0.5}>
                      <View style={iShopChangiAvailabilityStyle}>
                        <IShopChangiPromoDetail width={40} height={40} style={styles.tenantIconStyle} />
                        <View style={styles.promoRightContentStyle}>
                          <View style={styles.headerPromoAvailableStyle}>
                            <Text text={"iShopChangi"} style={styles.tenantNameTextStyle} />
                            <View style={styles.caretRightStyle}>
                              <CaretRight />
                            </View>
                          </View>
                          {!isEmpty(staffPerkPromotionDetailPayload?.iShopChangiAvailability) && (
                            <Text
                              text={staffPerkPromotionDetailPayload?.iShopChangiAvailability}
                              style={styles.caption1RegLocationStyle}
                            />
                          )}
                        </View>
                      </View>
                    </TouchableOpacity>
                  )}
                </View>

                {/* How to use */}
                {!isEmpty(staffPerkPromotionDetailPayload?.howToUse) && (
                  <View
                    style={handleCondition(
                      !isEmpty(staffPerkPromotionDetailPayload?.tnc),
                      styles.marginContentHowToUse,
                      styles.marginContentHowToUseWithOutBreakline,
                    )}
                  >
                    <CollapsibleComponent
                      customStyleTitle={isShopDineV2 && styles.collapsibleTitleStyle}
                      title={"HOW TO USE"}
                      content={
                        <TouchableOpacity activeOpacity={1}>
                          <RenderHTML
                            source={{
                              html: formatHtmlContent(staffPerkPromotionDetailPayload?.howToUse),
                            }}
                            tagsStyles={isShopDineV2 ? tagStyle2V2 : tagStyle2}
                            contentWidth={width}
                            systemFonts={systemFonts}
                          />
                        </TouchableOpacity>
                      }
                    />
                  </View>
                )}
                {!isEmpty(staffPerkPromotionDetailPayload?.tnc) && (
                  <View style={styles.marginContentTerm}>
                    <CollapsibleComponent
                      customStyleTitle={isShopDineV2 && styles.collapsibleTitleStyle}
                      title={"TERMS & CONDITIONS"}
                      content={
                        <TouchableOpacity activeOpacity={1}>
                          <RenderHTML
                            source={{
                              html: formatHtmlContent(staffPerkPromotionDetailPayload?.tnc),
                            }}
                            tagsStyles={isShopDineV2 ? tagStyle2V2 : tagStyle2}
                            contentWidth={width}
                            systemFonts={systemFonts}
                          />
                        </TouchableOpacity>
                      }
                    />
                  </View>
                )}
              </View>
            </View>
          </Animated.ScrollView>
          <LoadingOverlay visible={staffPerkPromotionDetailRequest && !isShopDineV2} isTransparent />
          {isLoggedIn && <TouchableOpacity
            style={styles.staffPerkScanBtnStyle}
            onPress={openCRCard}
            activeOpacity={0.5}
          >
            {isShopDineV2 ? <CRCardIcon width={52} height={52}/> : <ImageBackground source={StaffPerkScan} style={styles.imageBackgroundStaffPerkScan}>
              <BaseImage
                source={{ uri: tierIcon }}
                style={styles.imageTierHeaderStyle}
                resizeMode="contain"
              />
            </ImageBackground>}
          </TouchableOpacity>}
          <ChangiRewardCardInsidePromo ref={changiRewardCardInsidePromo} />
          {renderHeaderAnimated()}
          {!staffPerkPromotionDetailRequest && (
            <TouchableOpacity style={styles.crossPurple} onPress={closeScreen}>
              <CrossPurpleWithBgr />
            </TouchableOpacity>
          )}
        </>
      )
    }
  }

  return (
    <Modal
      isVisible={modalVisible}
      style={modalStyle}
      backdropColor={color.palette.black}
      backdropOpacity={0.5}
      swipeDirection={"down"}
      animationInTiming={TRANSITION_TIMING}
      animationOutTiming={TRANSITION_TIMING}
      backdropTransitionInTiming={TRANSITION_TIMING}
      backdropTransitionOutTiming={TRANSITION_TIMING}
      onSwipeComplete={closeScreen}
      propagateSwipe={true}
      onBackdropPress={closeScreen}
      onModalHide={callbackWhenClose}
      onModalShow={resetState}
    >
      {renderContentModal()}
    </Modal>
  )
}

export default forwardRef(StaffPerkPromotionDetailModal)
