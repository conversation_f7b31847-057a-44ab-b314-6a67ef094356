import { presets } from "app/elements/text"
import { color, shadow, typography } from "app/theme"
import { Dimensions, Platform, StyleSheet } from "react-native"
import { defaultSystemFonts } from "react-native-render-html"

export const { width, height } = Dimensions.get("screen")

const rightContentWidth = width - 100
export const tagStyle = {
  ul: {
    ...presets.bodyTextRegular,
    margin: 0,
    lineHeight: 20,
    fontSize: 16,
    paddingLeft: 11,
    color: color.palette.darkestGrey,
    textAlignVertical: "center",
  },
  ol: {
    ...presets.bodyTextRegular,
    margin: 0,
    lineHeight: 20,
    fontSize: 16,
    color: color.palette.darkestGrey,
    textAlignVertical: "center",
  },
  li: {
    ...presets.bodyTextRegular,
    lineHeight: 20,
    fontSize: 16,
    padding: 0,
    margin: 0,
    marginBottom: 5,
    color: color.palette.darkestGrey,
    textAlignVertical: "center",
  },
  p: {
    ...presets.bodyTextRegular,
    margin: 0,
    lineHeight: 20,
    fontSize: 16,
    marginBottom: 10,
    color: color.palette.darkestGrey,
    textAlignVertical: "center",
  },
}
export const tagStyle2 = {
  ul: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 15,
    lineHeight: 20,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
  ol: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 15,
    lineHeight: 20,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
  li: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 15,
    lineHeight: 20,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
  p: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 15,
    lineHeight: 20,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
}

export const tagStyle2V2 = {
  ul: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
  ol: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
  li: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
  p: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    padding: 0,
    marginTop: 16,
    textAlignVertical: "center",
  },
}

export const tagStyleForAvailability = {
  ul: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    marginBottom: 4,
  },
  ol: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    marginBottom: 4,
  },
  li: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    marginBottom: 4,
  },
  p: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    lineHeight: 18,
    margin: 0,
    marginBottom: 4,
  },
}
export const styles = StyleSheet.create({
  collapsibleTitleStyle: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 22,
    textAlignVertical: "center",
    letterSpacing: 0.06
  },
  contentModalStyle: {
    flex: 1,
    paddingBottom: 150
  },
  backgroundImage: {
    height: 250,
    width: width,
  },
  caption1LocationStyle: {
    ...presets.caption1Bold,
    color: color.palette.almostBlackGrey,
    marginBottom: 4,
    textAlign: "left",
  },
  caption1RegOutletStyle: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    lineHeight: 18,
  },
  caption1RegLocationStyle: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 4,
  },
  caretRightStyle: {
    alignItems: "center",
    height: 24,
    justifyContent: "center",
  },
  contentPromo: {
    padding: 24,
  },
  contentPromoV2: {
    paddingHorizontal: 24,
    paddingBottom: 24
  },
  crossPurple: {
    position: "absolute",
    right: 14,
    top: 18,
  },
  headerModalWithoutImageStyle: {
    height: 40,
  },
  headerModalWithoutImageStyleV2: {
    height: 50
  },
  headerPromoAvailableStyle: {
    alignItems: "flex-start",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
    width: rightContentWidth,
  },
  imageBackgroundStaffPerkScan: {
    alignItems: "center",
    height: 44,
    justifyContent: "center",
    width: 44,
  },
  imageTierHeaderStyle: {
    height: 20,
    width: 20,
  },
  marginContentHowToUse: {
    borderBottomColor: color.palette.lighterGrey,
    borderColor: color.transparent,
    borderWidth: 1,
    marginTop: 24,
  },
  marginContentHowToUseWithOutBreakline: {
    marginTop: 24,
  },
  marginContentTerm: {
    marginTop: 16,
  },
  offerDescription: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 15,
    lineHeight: 20,
    marginTop: 16,
    textAlignVertical: "center",
  },
  offerTitle: {
    ...presets.h1,
    color: color.palette.almostBlackGrey,
    fontSize: 28,
    lineHeight: 36,
    marginTop: 16,
    textAlignVertical: "center",
  },
  offerTitleV2: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 24,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 32,
    marginTop: 16,
    textAlignVertical: "center",
  },
  openWebViewSection: {
    alignItems: "center",
    flexDirection: "row",
    marginTop: 17,
  },
  physicalTenantContentStyle: {
    alignItems: "flex-start",
    flexDirection: "row",
    marginTop: 16,
    width: "100%",
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
  },
  physicalTenantContentStyleV2: {
    alignItems: "flex-start",
    flexDirection: "row",
    marginTop: 12,
    width: "100%",
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
  },
  physicalTenantStyle: {
    height: 50,
    width: "100%",
  },
  physicalTenantStyleV2: {
    height: 94,
    width: "100%",
  },
  promoAvailabilityText: {
    ...presets.h4,
    color: color.palette.almostBlackGrey,
    fontSize: 18,
    letterSpacing: 0.06,
    lineHeight: 22,
  },
  promoAvailabilityTextV2: {
    fontFamily: typography.bold,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
  },
  promoRightContentStyle: {
    width: rightContentWidth,
  },
  staffPerkScanBtnStyle: {
    borderRadius: 22,
    bottom: 40,
    height: 44,
    position: "absolute",
    right: 12,
    width: 44,
    ...shadow.secondaryShadow,
    backgroundColor: color.palette.whiteGrey,
    justifyContent: 'center',
    alignItems: 'center'
  },
  tagPromo: {
    alignItems: "center",
    backgroundColor: color.palette.lightestPurple,
    borderRadius: 99,
    justifyContent: "center",
    marginLeft: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  tenantIconStyle: {
    height: 40,
    width: 40,
    marginRight: 12,
  },
  tenantNameTextStyle: {
    ...presets.bodyTextBold,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    lineHeight: 20,
    textAlignVertical: "center",
    width: "90%",
  },
  textLinkWebView: {
    ...presets.textLink,
    color: color.palette.lightPurple,
    marginLeft: 8,
  },
  textLinkWebViewV2: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 20,
    marginLeft: 8,
  },
  textTagPromo: {
    ...presets.caption2Bold,
    color: color.palette.lightPurple,
    letterSpacing: 0.04,
  },
  wrapHandleTenantAvailabilityTextStyle: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    textAlignVertical: "center",
  },
  wrapLocationComponentStyle: {
    marginBottom: 16,
  },
})

export const systemFonts = [...defaultSystemFonts, "Lato-Regular", "Lato-Bold"]
