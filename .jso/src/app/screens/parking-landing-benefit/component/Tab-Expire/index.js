  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TabExpire = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _parkingLandingBenefit = _$$_REQUIRE(_dependencyMap[9]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _loadingSkeleton = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _errorOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _expiredCoupons = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _native = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TabExpire = exports.TabExpire = _react.default.memo(function (props) {
    var _aemCommonData$data, _aemCommonData$data2;
    var navigation = props.navigation,
      pressedTabLabel = props.pressedTabLabel;
    var scrollRef = _react.default.useRef(null);
    var _useParkingBenefit = (0, _parkingLandingBenefit.useParkingBenefit)({
        payloadKey: "expiredCoupons",
        scrollRef: scrollRef
      }),
      data = _useParkingBenefit.data,
      isError = _useParkingBenefit.isError,
      isFetching = _useParkingBenefit.isFetching,
      dataParked = _useParkingBenefit.dataParked,
      isRefresh = _useParkingBenefit.isRefresh,
      setIsRefresh = _useParkingBenefit.setIsRefresh;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setIsConnected = _useState2[1];
    var aemCommonData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var inf28 = aemCommonData == null || (_aemCommonData$data = aemCommonData.data) == null || (_aemCommonData$data = _aemCommonData$data.informatives) == null ? undefined : _aemCommonData$data.find(function (e) {
      return (e == null ? undefined : e.code) === "INF28";
    });
    var inf29 = aemCommonData == null || (_aemCommonData$data2 = aemCommonData.data) == null || (_aemCommonData$data2 = _aemCommonData$data2.informatives) == null ? undefined : _aemCommonData$data2.find(function (e) {
      return (e == null ? undefined : e.code) === "INF29";
    });
    var hasCoupon = (data == null ? undefined : data.length) > 0;
    var isParked = (dataParked == null ? undefined : dataParked.status) === "PARKED";
    var parkingStatus = isParked ? "Parked" : "Not Parked";
    var message = (0, _react.useMemo)(function () {
      if (hasCoupon || isFetching) {
        return (inf28 == null ? undefined : inf28.informativeText) || (0, _i18n.translate)("parkingLandingBenefit.expiredUsed.hasCouponMsg");
      }
      return (inf29 == null ? undefined : inf29.informativeText) || (0, _i18n.translate)("parkingLandingBenefit.expiredUsed.noCouponMsg");
    }, [hasCoupon, inf28 == null ? undefined : inf28.informativeText, inf29 == null ? undefined : inf29.informativeText, isFetching]);
    var checkConnection = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        setIsConnected(isConnected);
      });
      return function checkConnection() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkConnection();
    }, [navigation]);
    var renderTabContent = (0, _react.useMemo)(function () {
      if (!isFetching && (isError || !isConnected)) {
        return (0, _jsxRuntime.jsx)(_errorOverlay.default, {});
      }
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          text: message
        }), isFetching && (0, _jsxRuntime.jsx)(_loadingSkeleton.default, {}), data == null || data.map == null ? undefined : data.map(function (item, index, list) {
          return (0, _jsxRuntime.jsx)(_expiredCoupons.default, {
            isLastItem: index === (list == null ? undefined : list.length) - 1,
            item: item
          }, `${index}_${item == null ? undefined : item.title}`);
        })]
      });
    }, [JSON.stringify(data), isConnected, isError, isFetching, message]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if ((pressedTabLabel == null ? undefined : pressedTabLabel.current) === (0, _i18n.translate)("parkingLandingBenefit.tabFilter.expired") && !!(dataParked != null && dataParked.status)) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppBenefitsSummary, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppBenefitsSummary, `${parkingStatus} | Tab | ${(0, _i18n.translate)("parkingLandingBenefit.tabFilter.expired")}`));
      }
    }, [dataParked == null ? undefined : dataParked.status, parkingStatus, pressedTabLabel]));
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.wrapperStyle,
      children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        ref: scrollRef,
        style: styles.containerStyle,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          colors: [_theme.color.palette.lightGrey],
          onRefresh: function onRefresh() {
            return setIsRefresh == null ? undefined : setIsRefresh(true);
          },
          progressBackgroundColor: "transparent",
          progressViewOffset: 16,
          refreshing: isRefresh,
          tintColor: _theme.color.palette.lightGrey
        }),
        children: renderTabContent
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    wrapperStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      flex: 1
    },
    containerStyle: {
      paddingHorizontal: 16
    },
    messageTextStyle: Object.assign({}, _text.newPresets.caption2Regular, {
      marginVertical: 24,
      textAlign: "center"
    })
  });
