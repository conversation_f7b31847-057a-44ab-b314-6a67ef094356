  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TabInActive = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _titleDescription = _$$_REQUIRE(_dependencyMap[8]);
  var _viewItem = _$$_REQUIRE(_dependencyMap[9]);
  var _viewLoading = _$$_REQUIRE(_dependencyMap[10]);
  var _viewEmpty = _$$_REQUIRE(_dependencyMap[11]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[12]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _systemRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[15]);
  var _constants = _$$_REQUIRE(_dependencyMap[16]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[17]);
  var _native = _$$_REQUIRE(_dependencyMap[18]);
  var _errorOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _parkingLandingBenefit = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _translate = _$$_REQUIRE(_dependencyMap[23]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[24]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TabInActive = exports.TabInActive = _react.default.memo(function (props) {
    var pressedTabLabel = props.pressedTabLabel;
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loadingModal = _useState2[0],
      setLoadingModal = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isConnected = _useState4[0],
      setIsConnected = _useState4[1];
    var scrollRef = (0, _react.useRef)(null);
    var _useParkingBenefit = (0, _parkingLandingBenefit.useParkingBenefit)({
        payloadKey: "inactiveCoupons",
        scrollRef: scrollRef
      }),
      data = _useParkingBenefit.data,
      isError = _useParkingBenefit.isError,
      isFetching = _useParkingBenefit.isFetching,
      isRefresh = _useParkingBenefit.isRefresh,
      dataParked = _useParkingBenefit.dataParked,
      setIsRefresh = _useParkingBenefit.setIsRefresh;
    var isParked = (dataParked == null ? undefined : dataParked.status) === "PARKED";
    var parkingStatus = isParked ? "Parked" : "Not Parked";
    var trackingValue = `${parkingStatus} | Tile | ${(0, _translate.translate)("parkingLandingBenefit.tabFilter.in-active")}`;
    var handleItem = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (item) {
        var _item$title;
        var titleTrackingValue = (_item$title = item == null ? undefined : item.title) != null ? _item$title : "null";
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppBenefitsSummary, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppBenefitsSummary, `${trackingValue} | ${titleTrackingValue} | ${(0, _translate.translate)("parkingLandingBenefit.inactive.button")}`));
        setLoadingModal(true);
        try {
          var enableFlightCSM_CarParss = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.CSM_CARPARSS);
          var payload = {
            stateCode: enableFlightCSM_CarParss ? _constants.StateCode == null ? undefined : _constants.StateCode.CARPASS_DRIVE : _constants.StateCode == null ? undefined : _constants.StateCode.CARPASS,
            input: {
              "stateCode": "coupon",
              "bookingId": item == null ? undefined : item.booking_id
            }
          };
          var getLink = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          navigation.navigate(_constants.NavigationConstants.webview, {
            uri: getLink == null ? undefined : getLink.redirectUri,
            basicAuthCredential: getLink == null ? undefined : getLink.basicAuth
          });
          setLoadingModal(false);
        } catch (error) {
          setLoadingModal(false);
          setTimeout(function () {
            dispatch(_systemRedux.default.setBottomSheetErrorData({
              visible: true
            }));
          }, 600);
        }
      });
      return function handleItem(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var checkConnection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        setIsConnected(isConnected);
      });
      return function checkConnection() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkConnection();
    }, [navigation]);
    var renderItem = function renderItem(_ref3) {
      var item = _ref3.item,
        index = _ref3.index;
      return (0, _jsxRuntime.jsx)(_viewItem.ViewItem, {
        item: item,
        index: index,
        onActive: function onActive() {
          return handleItem(item);
        }
      });
    };
    var renderTabContent = (0, _react.useMemo)(function () {
      // API error / no internet case
      if (!isFetching && (isError || !isConnected)) {
        return (0, _jsxRuntime.jsx)(_errorOverlay.default, {});
      }
      // Empty case
      if ((data == null ? undefined : data.length) === 0 && !isFetching) {
        return (0, _jsxRuntime.jsx)(_viewEmpty.EmptyInActive, {
          trackingValue: trackingValue
        });
      }
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_titleDescription.TitleDescription, {}), isFetching ? (0, _jsxRuntime.jsx)(_viewLoading.LoadingInActive, {}) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewContent,
          children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: data,
            renderItem: renderItem,
            keyExtractor: function keyExtractor(index) {
              return index == null ? undefined : index.toString();
            },
            showsVerticalScrollIndicator: false,
            ListFooterComponent: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.viewBottom
            }),
            ref: scrollRef,
            nestedScrollEnabled: true
          })
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
          visible: loadingModal
        })]
      });
    }, [JSON.stringify(data), isConnected, isError, isFetching, loadingModal, renderItem]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if ((pressedTabLabel == null ? undefined : pressedTabLabel.current) === (0, _translate.translate)("parkingLandingBenefit.tabFilter.in-active") && !!(dataParked != null && dataParked.status)) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppBenefitsSummary, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppBenefitsSummary, `${parkingStatus} | Tab | ${(0, _translate.translate)("parkingLandingBenefit.tabFilter.in-active")}`));
      }
    }, [dataParked == null ? undefined : dataParked.status, parkingStatus, pressedTabLabel]));
    return (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
      refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
        colors: [_theme.color.palette.lightGrey],
        onRefresh: function onRefresh() {
          return setIsRefresh == null ? undefined : setIsRefresh(true);
        },
        progressBackgroundColor: "transparent",
        progressViewOffset: 16,
        refreshing: isRefresh,
        tintColor: _theme.color.palette.lightGrey
      }),
      showsVerticalScrollIndicator: false,
      style: styles.container,
      children: renderTabContent
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    viewContent: {
      flex: 1,
      paddingHorizontal: 16,
      gap: 12
    },
    viewBottom: {
      height: 40
    }
  });
