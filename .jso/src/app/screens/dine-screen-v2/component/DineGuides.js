  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineGuides = undefined;
  var _native = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[6]);
  var _color = _$$_REQUIRE(_dependencyMap[7]);
  var _typography = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var DineGuidesLoading = function DineGuidesLoading() {
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.viewLoading,
      children: [1, 2, 3].map(function (item) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewItemLoading,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.viewContentLoading,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.viewTxt,
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
                shimmerStyle: styles.contentTitleLoading
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
                shimmerStyle: styles.contentTitleLoading
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
                shimmerStyle: styles.contentLoading
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
                shimmerStyle: styles.contentTitleLoading
              })]
            })
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.imageLoading
          })]
        }, item);
      })
    });
  };
  var DineGuides = exports.DineGuides = _react.default.memo(function (props) {
    var navigation = (0, _native.useNavigation)();
    var dataDineGuides = props.dataDineGuides,
      isLoadingDineGuides = props.isLoadingDineGuides,
      isErrorDineGuides = props.isErrorDineGuides;
    if (isErrorDineGuides || !isLoadingDineGuides && !(dataDineGuides != null && dataDineGuides.length)) return null;
    var onClickItemGuide = function onClickItemGuide(item) {
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: item == null ? undefined : item.link
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [((dataDineGuides == null ? undefined : dataDineGuides.length) > 0 || isLoadingDineGuides) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.viewTitle,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "dineScreenV2.dineGuides.title",
          preset: "caption1BoldSmall",
          style: styles.txtTitle
        })
      }), isLoadingDineGuides ? (0, _jsxRuntime.jsx)(DineGuidesLoading, {}) : (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewScroll,
          children: dataDineGuides == null ? undefined : dataDineGuides.map(function (item, index) {
            return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              style: [styles.viewItemLoading, {
                marginLeft: index === 0 ? 20 : 0
              }],
              onPress: function onPress() {
                onClickItemGuide(item);
              },
              activeOpacity: 0.8,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: [styles.viewContentLoading, {
                  paddingRight: 12
                }],
                children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: styles.viewTxtContent,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.txtTitleItem,
                    numberOfLines: 3,
                    children: item == null ? undefined : item.title
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.txtSubTitle,
                    numberOfLines: 1,
                    children: item == null ? undefined : item.source
                  })]
                })
              }), (0, _jsxRuntime.jsx)(_baseImage.default, {
                source: {
                  uri: item == null ? undefined : item.image
                },
                style: styles.imageLoading
              })]
            }, `${item == null ? undefined : item.title}-${index}`);
          })
        })
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      marginTop: 50
    },
    viewScroll: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 12,
      marginBottom: 30
    },
    viewLoading: {
      width: '100%',
      marginTop: 12,
      flexDirection: 'row',
      gap: 12,
      paddingLeft: 20
    },
    viewItemLoading: {
      width: 200,
      height: 213,
      justifyContent: 'flex-end'
    },
    viewContentLoading: {
      height: 184,
      width: '100%',
      borderRadius: 16,
      backgroundColor: _color.color.palette.whiteGrey,
      shadowColor: "#121212",
      shadowOffset: {
        width: 0,
        height: 6
      },
      shadowOpacity: 0.08,
      shadowRadius: 20,
      elevation: 8,
      justifyContent: 'flex-end',
      paddingBottom: 12,
      paddingLeft: 12,
      paddingRight: 37
    },
    imageLoading: {
      width: 176,
      height: 97,
      marginHorizontal: 12,
      borderRadius: 12,
      position: 'absolute',
      top: 0
    },
    viewTitle: {
      paddingHorizontal: 20,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center"
    },
    viewRow: {
      flexDirection: "row",
      alignItems: "center"
    },
    txtTitle: {
      color: _color.color.palette.darkestGrey
    },
    contentTitleLoading: {
      width: '100%',
      height: 13,
      borderRadius: 4
    },
    viewTxt: {
      gap: 12
    },
    contentLoading: {
      width: 57,
      height: 13,
      borderRadius: 4
    },
    viewTxtContent: {
      width: '100%',
      height: 94,
      justifyContent: 'space-between'
    },
    txtTitleItem: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 20
    },
    txtSubTitle: {
      fontFamily: _typography.typography.regular,
      color: _color.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18
    }
  });
