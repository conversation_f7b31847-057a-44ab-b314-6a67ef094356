  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.setPPCookiesExploreChangi = exports.PlayPassWebView = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[7]);
  var _qs = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _extendedWebview = _$$_REQUIRE(_dependencyMap[10]);
  var _envParams = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _i18n = _$$_REQUIRE(_dependencyMap[14]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _takeUploadPhoto = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[19]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _defaultHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _changiMillionaireHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _uploadReceiptCm = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _scanQrCm = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _utils = _$$_REQUIRE(_dependencyMap[25]);
  var _enum = _$$_REQUIRE(_dependencyMap[26]);
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[28]));
  var _notificationRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _storageKey = _$$_REQUIRE(_dependencyMap[30]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[31]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[33]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _native = _$$_REQUIRE(_dependencyMap[35]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[36]);
  var _appscapadeHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[37]));
  var _cookies = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[38]));
  var _envSettings = _$$_REQUIRE(_dependencyMap[39]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[40]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[41]);
  var _account = _$$_REQUIRE(_dependencyMap[42]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[43]);
  var _airportLandingRedux = _$$_REQUIRE(_dependencyMap[44]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[45]);
  var _deeplinkParameter = _$$_REQUIRE(_dependencyMap[46]);
  var _useRetroClaims = _$$_REQUIRE(_dependencyMap[47]);
  var _urlParse2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[48]));
  var _useL3BaggagePrediction = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[49]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[50]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CameraType = /*#__PURE__*/function (CameraType) {
    CameraType["default"] = "default";
    CameraType["uploadCM"] = "uploadCM";
    CameraType["scanCM"] = "scanCM";
    CameraType["scanBarCode"] = "scanBarCode";
    return CameraType;
  }(CameraType || {});
  var PlayPassDeeplinks = /*#__PURE__*/function (PlayPassDeeplinks) {
    PlayPassDeeplinks["openCamera"] = "cagichangi://launchimagepicker/";
    PlayPassDeeplinks["openImageGallery"] = "cagichangi://launchimagepicker/?ui=PBE&entrypoint=upload";
    PlayPassDeeplinks["scanQR"] = "cagichangi://launchqrcode";
    PlayPassDeeplinks["scanBarCode"] = "cagichangi://launchbarcode";
    PlayPassDeeplinks["eventDetails"] = "cagichangi://ppevent/";
    PlayPassDeeplinks["wallet"] = "cagichangi://wallet/";
    PlayPassDeeplinks["eCard"] = "cagichangi://cr_ecard";
    PlayPassDeeplinks["paymentConfirmation"] = "cagichangi://ppconfirmation/";
    PlayPassDeeplinks["exploreHomePage"] = "cagichangi://explore/";
    PlayPassDeeplinks["login"] = "cagichangi://login";
    PlayPassDeeplinks["exitCM"] = "cagichangi://exitCM";
    PlayPassDeeplinks["scanBoardingPass"] = "cagichangi://scanboardingpass";
    PlayPassDeeplinks["perkspage"] = "cagichangi://perkspage";
    PlayPassDeeplinks["searchflights"] = "cagichangi://searchflights";
    PlayPassDeeplinks["loginappscapade"] = "cagichangi://loginappscapade";
    return PlayPassDeeplinks;
  }(PlayPassDeeplinks || {});
  var container = {
    flex: 1
  };
  var bottomSheetStyle = {
    flex: 1
  };
  var WebViewContainer = _reactNative.Platform.OS === 'android' ? _reactNative.View : _reactNativeSafeAreaContext.SafeAreaView;
  var removeSpecialChars = function removeSpecialChars(text) {
    if (typeof text !== 'string') {
      throw new TypeError('Header must be a string');
    }
    var specialCharsRegex = /[^a-zA-Z0-9!#$%&'()*+\-./:<=>?@[\]^_`{|}~ ]/g;
    return text.replace(specialCharsRegex, '');
  };
  var getMessageCode = function getMessageCode(messageCommon, code) {
    if (Array.isArray(messageCommon) && messageCommon.length > 0) {
      return messageCommon.find(function (e) {
        return (e == null ? undefined : e.code) === code;
      }) || {};
    }
    return {};
  };
  var runFunctionWithCondition = function runFunctionWithCondition(condition, callback1, callback2) {
    if (condition) {
      callback1 == null || callback1();
    } else {
      callback2 == null || callback2();
    }
  };
  var setPPCookiesExploreChangi = exports.setPPCookiesExploreChangi = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (data, callback) {
      var ecid = yield (0, _adobe.getExperienceCloudId)();
      var dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ";
      var epxireDate = (0, _moment.default)().add(30, "days").format(dateFormat).toString();
      var Settings = (0, _envSettings.getEnvSetting)();
      var hostURL = Settings.URL_HOST_PP_COOKES;
      var promises = [_cookies.default.set(hostURL, {
        name: "url_host",
        value: data.urlHost,
        version: "1",
        expires: epxireDate
      }), _cookies.default.set(hostURL, {
        name: "ecid_changiapp_playpass",
        value: ecid,
        version: "1",
        expires: epxireDate
      }), _cookies.default.set(hostURL, {
        name: "main_entry_page",
        value: data.entryPoint,
        version: "1",
        expires: epxireDate
      })];
      if (data.uid_changiapp_playpass) {
        _cookies.default.set(hostURL, {
          name: "uid_changiapp_playpass",
          value: data.uid_changiapp_playpass,
          version: "1",
          expires: epxireDate
        });
      }
      if (data.eventName) {
        promises.push(_cookies.default.set(hostURL, {
          name: "event_name",
          value: removeSpecialChars(data.eventName),
          version: "1",
          expires: epxireDate
        }));
      }
      if (data.perkTitle) {
        promises.push(_cookies.default.set(hostURL, {
          name: "perk_tile",
          value: removeSpecialChars(data.perkTitle),
          version: "1",
          expires: epxireDate
        }));
      }
      if (data.cardID) {
        promises.push(_cookies.default.set(hostURL, {
          name: "cart_id",
          value: data.cardID,
          version: "1",
          expires: epxireDate
        }));
      }
      if (data.tokenType) {
        promises.push(_cookies.default.set(hostURL, {
          name: "token_type",
          value: data.tokenType,
          version: "1",
          expires: epxireDate
        }));
      }
      yield Promise.all(promises);
      callback();
    });
    return function setPPCookiesExploreChangi(_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }();
  var clearCookiesPP = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      var Settings = (0, _envSettings.getEnvSetting)();
      var hostURL = Settings.URL_HOST_PP_COOKES;
      if (_reactNative.Platform.OS === "ios") {
        _cookies.default.clearByName(hostURL, "url_host");
        _cookies.default.clearByName(hostURL, "uid_changiapp_playpass");
        _cookies.default.clearByName(hostURL, "ecid_changiapp_playpass");
        _cookies.default.clearByName(hostURL, "main_entry_page");
        _cookies.default.clearByName(hostURL, "event_name");
        _cookies.default.clearByName(hostURL, "perk_tile");
        _cookies.default.clearByName(hostURL, "cart_id");
        _cookies.default.clearByName(hostURL, "token_type");
      } else {
        var dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ";
        var epxireDate = (0, _moment.default)().subtract(1, "days").format(dateFormat).toString();
        _cookies.default.set(hostURL, {
          name: "url_host",
          value: "",
          version: "1",
          expires: epxireDate
        });
        _cookies.default.set(hostURL, {
          name: "uid_changiapp_playpass",
          value: "",
          version: "1",
          expires: epxireDate
        });
        _cookies.default.set(hostURL, {
          name: "ecid_changiapp_playpass",
          value: "",
          version: "1",
          expires: epxireDate
        });
        _cookies.default.set(hostURL, {
          name: "main_entry_page",
          value: "",
          version: "1",
          expires: epxireDate
        });
        _cookies.default.set(hostURL, {
          name: "event_name",
          value: "",
          version: "1",
          expires: epxireDate
        });
        _cookies.default.set(hostURL, {
          name: "perk_tile",
          value: "",
          version: "1",
          expires: epxireDate
        });
        _cookies.default.set(hostURL, {
          name: "cart_id",
          value: "",
          version: "1",
          expires: epxireDate
        });
        _cookies.default.set(hostURL, {
          name: "token_type",
          value: "",
          version: "1",
          expires: epxireDate
        });
      }
    });
    return function clearCookiesPP() {
      return _ref2.apply(this, arguments);
    };
  }();
  var canGoBackForPhysicalBack = false;
  var PlayPassWebView = exports.PlayPassWebView = function PlayPassWebView(props) {
    var _env2, _env3;
    var webviewRef = (0, _react.useRef)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = props.navigation;
    var _props$route$params = props.route.params,
      uri = _props$route$params.uri,
      title = _props$route$params.title,
      needBackButton = _props$route$params.needBackButton,
      needCloseButton = _props$route$params.needCloseButton,
      basicAuthCredential = _props$route$params.basicAuthCredential,
      onBackBtnPress = _props$route$params.onBackBtnPress,
      onCloseBtnPress = _props$route$params.onCloseBtnPress,
      shouldShowLoadingCookies = _props$route$params.shouldShowLoadingCookies,
      loadingCookiesStyles = _props$route$params.loadingCookiesStyles,
      loadingCookiesSection = _props$route$params.loadingCookiesSection,
      _props$route$params$h = _props$route$params.headerType,
      headerType = _props$route$params$h === undefined ? _enum.WebViewHeaderTypes.default : _props$route$params$h,
      isIShopChangi = _props$route$params.isIShopChangi;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isLogIn = _useState2[0],
      setLogIn = _useState2[1];
    var statePlayPass = (0, _react.useRef)(null);
    var shouldOpenGallery = (0, _react.useRef)(false);
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("playPassWebView", true),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl,
      loading = _useGeneratePlayPassU.loading;
    var _useGeneratePlayPassU2 = (0, _screenHook.useGeneratePlayPassUrl)("FAQupload", true),
      getPlayPassUrlFAQ = _useGeneratePlayPassU2.getPlayPassUrl,
      loadingFAQ = _useGeneratePlayPassU2.loading;
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isBottomSheetVisible = _useState4[0],
      setIsBottomSheetVisible = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isBottomSheetCMVisible = _useState6[0],
      setIsBottomSheetCMVisible = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isBottomSheetQRScanisible = _useState8[0],
      setIsBottomSheetQRScanVisible = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isBottomSheetBarCodeScanVisible = _useState0[0],
      setIsBottomSheetBarCodeScanVisible = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      canGoBack = _useState10[0],
      setCanGoBack = _useState10[1];
    var _useState11 = (0, _react.useState)(""),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      webViewTitle = _useState12[0],
      setWebViewTitle = _useState12[1];
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg61 = getMessageCode(messageCommon, "MSG61");
    var msg62 = getMessageCode(messageCommon, "MSG62");
    var _useState13 = (0, _react.useState)({
        path: "",
        params: ""
      }),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      deepLink = _useState14[0],
      setDeepLink = _useState14[1];
    var isIOS = _reactNative.Platform.OS === "ios";
    var isPlaypassItemClicked = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.isPlaypassItemClicked);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var currentUrl = (0, _react.useRef)(uri);
    var isFocused = (0, _native.useIsFocused)();
    var navigationState = (0, _native.useNavigationState)(function (state) {
      return state;
    });
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      cm24Flag = _useContext.cm24Flag;
    var playPassUrlPayloadFAQupload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getPlayPassUrlPayload("FAQupload"));
    var isCM24 = (0, _remoteConfig.isFlagOnCondition)(cm24Flag);
    var isCM24Page = headerType === _enum.WebViewHeaderTypes.changiMillionaire && isCM24;
    var _useL3BaggagePredicti = (0, _useL3BaggagePrediction.default)(),
      handleOpenL3BaggagePrediction = _useL3BaggagePredicti.handleOpenL3BaggagePrediction;
    (0, _react.useEffect)(function () {
      if (playPassUrlPayloadFAQupload != null && playPassUrlPayloadFAQupload.error) {
        navigation.dispatch(_native.StackActions.popToTop());
      }
    }, [playPassUrlPayloadFAQupload == null ? undefined : playPassUrlPayloadFAQupload.error]);
    var Rationale = {
      title: (0, _utils.simpleCondition)({
        condition: msg61 == null ? undefined : msg61.title,
        ifValue: msg61 == null ? undefined : msg61.title,
        elseValue: (0, _i18n.translate)("requestPermission.camera.title")
      }),
      message: (0, _utils.simpleCondition)({
        condition: msg61 == null ? undefined : msg61.message,
        ifValue: msg61 == null ? undefined : msg61.message,
        elseValue: (0, _i18n.translate)("requestPermission.camera.message")
      }),
      buttonPositive: (0, _utils.simpleCondition)({
        condition: msg61 == null ? undefined : msg61.secondButton,
        ifValue: msg61 == null ? undefined : msg61.secondButton,
        elseValue: (0, _i18n.translate)("requestPermission.camera.buttonPositive")
      }),
      buttonNegative: (0, _utils.simpleCondition)({
        condition: msg61 == null ? undefined : msg61.firstButton,
        ifValue: msg61 == null ? undefined : msg61.firstButton,
        elseValue: (0, _i18n.translate)("requestPermission.camera.buttonNegative")
      })
    };
    var caculateNavigation = function caculateNavigation() {
      var _navigationState$rout, _history, _navigationState$rout2, _navigationState$rout3, _navigationState$rout4, _navigationState$rout5;
      var history = (_navigationState$rout = navigationState == null ? undefined : navigationState.routes) != null ? _navigationState$rout : [];
      var screenPosition = history.length - 1;
      var isFromSearchScreen = [_constants.NavigationConstants.search, _constants.NavigationConstants.searchResult].includes((_history = history[screenPosition - 1]) == null ? undefined : _history.name);
      if (isFromSearchScreen) {
        return 1;
      }
      var entryPointBottomNavigation = navigationState == null || (_navigationState$rout2 = navigationState.routes) == null ? undefined : _navigationState$rout2.findIndex(function (e) {
        return (e == null ? undefined : e.name) === "bottomNavigation";
      });
      var entryPointFlightDetails = navigationState == null || (_navigationState$rout3 = navigationState.routes) == null ? undefined : _navigationState$rout3.findIndex(function (e) {
        return (e == null ? undefined : e.name) === "flightDetails";
      });
      var entryPointPlayPassBookingDetail = navigationState == null || (_navigationState$rout4 = navigationState.routes) == null ? undefined : _navigationState$rout4.findIndex(function (e) {
        return (e == null ? undefined : e.name) === _constants.NavigationConstants.playPassBookingDetail;
      });
      var isFlightDetailNearInstantWin = screenPosition - entryPointFlightDetails < screenPosition - entryPointBottomNavigation;
      if (isFlightDetailNearInstantWin) {
        return screenPosition - entryPointFlightDetails;
      }
      if ((navigationState == null || (_navigationState$rout5 = navigationState.routes) == null ? undefined : _navigationState$rout5.length) - 2 === entryPointPlayPassBookingDetail) {
        return 1;
      }
      return screenPosition - entryPointBottomNavigation;
    };
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        setDeepLink({
          path: "",
          params: ""
        });
      }
    }, []);
    var fetchMytravelData = function fetchMytravelData() {
      if (isLoggedIn && profilePayload != null && profilePayload.email && isIShopChangi) {
        dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsRequest(profilePayload.email));
      }
    };
    var onBackButtonPressed = function onBackButtonPressed() {
      if (onBackBtnPress) {
        onBackBtnPress(navigation);
      } else {
        navigation.goBack();
      }
      fetchMytravelData();
      dispatch(dispatch(_exploreRedux.default.setIsPlaypassItemClicked(false)));
    };
    var onCloseButtonPressed = function onCloseButtonPressed() {
      if (onCloseBtnPress) {
        onCloseBtnPress();
      } else {
        navigation.goBack();
      }
      fetchMytravelData();
      dispatch(dispatch(_exploreRedux.default.setIsPlaypassItemClicked(false)));
    };
    var onBack = function onBack() {
      if (isPlaypassItemClicked) {
        fetchMytravelData();
        navigation.goBack();
        dispatch(dispatch(_exploreRedux.default.setIsPlaypassItemClicked(false)));
      } else {
        var _webviewRef$current;
        (_webviewRef$current = webviewRef.current) == null || _webviewRef$current.goBack();
      }
    };
    (0, _react.useEffect)(function () {
      dispatch(_notificationRedux.default.forceClosePopupUserNotifications(true));
      if (headerType === _enum.WebViewHeaderTypes.changiMillionaire) {
        (0, _screenHelper.setTriggerForShowRatingPopup)(_storageKey.StorageKey.isParticipatedCmWebview);
      }
      return function () {
        (0, _screenHook.setPreviousScreen)(_constants.TrackingScreenName.PlayPassWebView);
      };
    }, []);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var onBackPress = function onBackPress() {
        onCloseButtonPressed();
        return true;
      };
      if (onCloseBtnPress) {
        navigation.setOptions({
          gestureEnabled: false
        });
      }
      var subscription = _reactNative.BackHandler.addEventListener("hardwareBackPress", onBackPress);
      return function () {
        return subscription.remove();
      };
    }, []));
    var onAndroidBackPress = function onAndroidBackPress() {
      var shouldReturn = false;
      if (headerType === _enum.WebViewHeaderTypes.appscapadeLP || headerType === _enum.WebViewHeaderTypes.appscapadeInstantWinLP) {
        shouldReturn = true;
      } else {
        if (canGoBackForPhysicalBack) {
          onBack();
        } else {
          onBackButtonPressed();
        }
        return true;
      }
      return shouldReturn;
    };
    (0, _react.useEffect)(function () {
      var backHandler = _reactNative.BackHandler.addEventListener("hardwareBackPress", onAndroidBackPress);
      return function () {
        return backHandler.remove();
      };
    }, []);
    (0, _react.useEffect)(function () {
      return function () {
        clearCookiesPP();
      };
    }, []);
    var onNavigationStateChange = function onNavigationStateChange(navState) {
      canGoBackForPhysicalBack = navState == null ? undefined : navState.canGoBack;
      setCanGoBack(navState == null ? undefined : navState.canGoBack);
      if (navState != null && navState.url.startsWith("cagichangi://")) {
        var _webviewRef$current2;
        (_webviewRef$current2 = webviewRef.current) == null || _webviewRef$current2.injectJavaScript(`
      window.location.href = '${currentUrl.current}';
    `);
        return false;
      }
      currentUrl.current = navState == null ? undefined : navState.url;
    };
    var onUploadReceiptScreen = function onUploadReceiptScreen(cameraType) {
      (0, _reactNativePermissions.request)((0, _utils.handleCondition)(_reactNative.Platform.OS === "ios", _reactNativePermissions.PERMISSIONS.IOS.CAMERA, _reactNativePermissions.PERMISSIONS.ANDROID.CAMERA), Rationale).then(function (result) {
        if (result === _reactNativePermissions.RESULTS.BLOCKED) {
          _reactNative.Alert.alert(msg62 == null ? undefined : msg62.title, msg62 == null ? undefined : msg62.message, [{
            text: (0, _utils.simpleCondition)({
              condition: msg62 == null ? undefined : msg62.firstButton,
              ifValue: msg62 == null ? undefined : msg62.firstButton,
              elseValue: (0, _i18n.translate)("scanCode.noBoardingPassDetected.firstButton")
            }),
            style: "cancel",
            onPress: _reactNativePermissions.openSettings
          }, {
            text: (0, _utils.simpleCondition)({
              condition: msg62 == null ? undefined : msg62.secondButton,
              ifValue: msg62 == null ? undefined : msg62.secondButton,
              elseValue: (0, _i18n.translate)("scanCode.noBoardingPassDetected.secondButton")
            }),
            onPress: function onPress() {
              return null;
            }
          }]);
        } else if (result === _reactNativePermissions.RESULTS.GRANTED) {
          if ((0, _utils.ifOneTrue)([!cameraType, cameraType === CameraType.default])) {
            setIsBottomSheetVisible(true);
          } else if (cameraType === CameraType.uploadCM) {
            setIsBottomSheetCMVisible(true);
          } else if (cameraType === CameraType.scanCM) {
            setIsBottomSheetQRScanVisible(true);
          } else if (cameraType === CameraType.scanBarCode) {
            setIsBottomSheetBarCodeScanVisible(true);
          }
        }
      });
    };
    var injectBase64ToWebView = function injectBase64ToWebView(base64Img) {
      var _webviewRef$current3;
      (_webviewRef$current3 = webviewRef.current) == null || _webviewRef$current3.injectJavaScript("setImage('" + base64Img + "')");
    };
    var handleTakePicture = function handleTakePicture(base64Img) {
      injectBase64ToWebView(base64Img);
      onClosedSheet();
    };
    var handleTakePictureCM = function handleTakePictureCM(base64) {
      injectBase64ToWebView(base64);
      onClosedSheetCM();
    };
    var handleUploadCM = function handleUploadCM(base64) {
      injectBase64ToWebView(base64);
      onClosedSheetQRScan();
    };
    var handleQRScan = function handleQRScan(codeValue) {
      if (isBottomSheetBarCodeScanVisible) {
        var _webviewRef$current4;
        (_webviewRef$current4 = webviewRef.current) == null || _webviewRef$current4.injectJavaScript(`setBarcode("${codeValue}")`);
      } else {
        var _webviewRef$current5;
        (_webviewRef$current5 = webviewRef.current) == null || _webviewRef$current5.injectJavaScript(`setQRCode("${codeValue}")`);
      }
      onClosedSheetQRScan();
    };
    (0, _react.useEffect)(function () {
      var stateCode = (0, _lodash.get)(statePlayPass.current, "stateCode", "");
      var params = (0, _lodash.get)(statePlayPass.current, "packageCode", "");
      var tokenType = (0, _lodash.get)(statePlayPass.current, "tokenType", "");
      var openPlaypass = function openPlaypass() {
        var finalParams = tokenType ? `${tokenType}==${params}` : params;
        if ((0, _utils.ifAllTrue)([isLogIn, stateCode, params])) {
          if (stateCode.includes(_constants.StateCode.PPRECEIPT)) {
            getPlayPassUrlFAQ(stateCode, finalParams);
          } else {
            getPlayPassUrl(stateCode, finalParams);
          }
          statePlayPass.current = null;
        }
      };
      openPlaypass();
    }, [isLogIn, getPlayPassUrl]);
    var getParams = function getParams(url) {
      try {
        var _get;
        var params = _qs.default.parse(url);
        //@ts-ignore
        var _get$split = (_get = (0, _lodash.get)(Object.values(params), "0", "")) == null ? undefined : _get.split("__"),
          _get$split2 = (0, _slicedToArray2.default)(_get$split, 3),
          stateCode = _get$split2[0],
          packageCode = _get$split2[1],
          tokenType = _get$split2[2];
        return {
          stateCode: stateCode,
          packageCode: packageCode,
          tokenType: tokenType
        };
      } catch (error) {
        return {
          stateCode: null,
          packageCode: null,
          tokenType: null
        };
      }
    };
    (0, _react.useEffect)(function () {
      if ((0, _utils.ifAllTrue)([!(deepLink != null && deepLink.path), !isIOS])) return;
      handleOpenApp();
    }, [deepLink == null ? undefined : deepLink.path]);
    var handleOpenApp = function handleOpenApp() {
      switch (deepLink == null ? undefined : deepLink.path) {
        case PlayPassDeeplinks.eCard:
          goToECard();
          break;
        case PlayPassDeeplinks.wallet:
          goToWallet();
          break;
        case PlayPassDeeplinks.exploreHomePage:
          goToExploreChangi();
          break;
        case PlayPassDeeplinks.scanBoardingPass:
          openScanBoardingPass();
          break;
        case PlayPassDeeplinks.perkspage:
          openPerkPage();
          break;
        case PlayPassDeeplinks.searchflights:
          openSearchFlight();
          break;
        case PlayPassDeeplinks.loginappscapade:
          handleLoginAppscapade();
          break;
      }
    };
    var goToECard = function goToECard() {
      var _webviewRef$current6;
      (_webviewRef$current6 = webviewRef.current) == null || _webviewRef$current6.stopLoading();
      _changiEcardControler.default.showModal(navigation);
    };
    var goToWallet = function goToWallet() {
      var _webviewRef$current7;
      (_webviewRef$current7 = webviewRef.current) == null || _webviewRef$current7.stopLoading();
      navigation.navigate(_constants.NavigationConstants.bookingsOrdersScreen);
    };
    var openPerkPage = function openPerkPage() {
      var _webviewRef$current8;
      (_webviewRef$current8 = webviewRef.current) == null || _webviewRef$current8.stopLoading();
      navigation.navigate(_constants.NavigationConstants.redemptionCatalogueScreen, {
        screen: _constants.NavigationConstants.perksTab
      });
    };
    var openSearchFlight = function openSearchFlight() {
      var _webviewRef$current9;
      (_webviewRef$current9 = webviewRef.current) == null || _webviewRef$current9.stopLoading();
      var currentDate = (0, _dateTime.getCurrentTimeSingapore)();
      var nextDateTime = (0, _moment.default)(currentDate).add(1, "day").format("YYYY-MM-DD HH:mm").split(" ");
      var nextDate = nextDateTime[0];
      dispatch(_flyRedux.FlyCreators.setFilterDateDeparture(new Date(nextDate)));
      dispatch(_flyRedux.FlyCreators.setFilterDateArrival(new Date(nextDate)));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightListFilters, (0, _moment.default)(nextDate).format("YYYY-MM-DD")));
      dispatch(_flyRedux.FlyCreators.setFlightSearchDate(nextDate));
      navigation.navigate("flightResultLandingScreen", {
        screen: "DEP",
        isLoadFlightAfter24h: true,
        sourcePage: _adobe.AdobeTagName.CAppFlightLanding,
        selectedDate: nextDate
      });
    };
    var handleLoginAppscapade = function handleLoginAppscapade() {
      navigation.navigate(_constants.NavigationConstants.authScreen, {
        sourceSystem: _constants.SOURCE_SYSTEM.APPSCAPADE,
        callBackAfterLoginSuccess: function () {
          var _callBackAfterLoginSuccess = (0, _asyncToGenerator2.default)(function* () {
            _globalLoadingController.default.showLoading(true);
            var response = yield (0, _pageConfigSaga.getDeepLink)({
              stateCode: _constants.StateCode.APPSCAPADE_LP,
              input: {
                uid: profilePayload == null ? undefined : profilePayload.id
              }
            });
            _globalLoadingController.default.hideLoading();
            if (response != null && response.redirectUri) {
              navigation.replace(_constants.NavigationConstants.playpassWebview, {
                uri: response == null ? undefined : response.redirectUri,
                needBackButton: true,
                needCloseButton: true,
                headerType: _enum.WebViewHeaderTypes.appscapadeLP,
                basicAuthCredential: response == null ? undefined : response.basicAuth
              });
            }
          });
          function callBackAfterLoginSuccess() {
            return _callBackAfterLoginSuccess.apply(this, arguments);
          }
          return callBackAfterLoginSuccess;
        }()
      });
    };
    var openScanBoardingPass = function openScanBoardingPass() {
      var _webviewRef$current0;
      (_webviewRef$current0 = webviewRef.current) == null || _webviewRef$current0.stopLoading();
      navigation.navigate(_constants.NavigationConstants.appscapadeScanBoadingPass);
    };
    var goToExploreChangi = function goToExploreChangi() {
      navigation == null || navigation.goBack();
      navigation == null || navigation.navigate(_constants.NavigationConstants.explore, {
        isOpenApp: false,
        isScrollToExploreChangiSection: true
      });
    };
    var handleForAA = function handleForAA(dataReq) {
      var adobeAnalytics = (0, _lodash.get)(dataReq, "adobeAnalytics");
      if (!(0, _lodash.isEmpty)(adobeAnalytics)) {
        if (adobeAnalytics.aaTag === _adobe.AdobeTagName.CAppL3FAQPage) {
          var _aaValue$split;
          var _ref3 = adobeAnalytics || {},
            aaValue = _ref3.aaValue;
          var questionContent = aaValue == null || aaValue.split == null || (_aaValue$split = aaValue.split(" | ")) == null ? undefined : _aaValue$split[0];
          if (questionContent) {
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppL3FAQPage, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppL3FAQPage, `Parking | ${questionContent} | Link`));
          }
          return;
        }
        (0, _adobe.trackAction)(adobeAnalytics.aaTag, (0, _defineProperty2.default)({}, adobeAnalytics.aaTag, adobeAnalytics.aaValue));
      }
      var pageL3Info = (0, _lodash.get)(dataReq, "pageL3Info");
      if (!(0, _lodash.isEmpty)(pageL3Info)) {
        var _env;
        var pageName = `${pageL3Info.parentPageName}_${pageL3Info.pageName}`;
        var pagePath = ((_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL) + pageL3Info.pagePath;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.common, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.cagPagePath, pagePath));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.cagPagePath, (0, _defineProperty2.default)({}, pageName, pagePath));
        (0, _adobe.trackAction)(_adobe.AdobeTagName.cagPagePath, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.cagPagePath, (0, _defineProperty2.default)({}, pageName, pagePath)));
        (0, _adobe.commonTrackingScreen)(pageName, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      }
    };
    var goToNewUri = function goToNewUri(newUri) {
      var script = `window.location.href = "${newUri}";`;
      webviewRef.current.injectJavaScript(script);
    };
    var deepLinkHandler = function deepLinkHandler(newRequest) {
      var _newRequest = Object.assign({}, newRequest),
        _newRequest$url = _newRequest.url,
        url = _newRequest$url === undefined ? "" : _newRequest$url;
      if (newRequest != null && newRequest.url.startsWith("http://")) {
        var _webviewRef$current1;
        var newUri = newRequest == null ? undefined : newRequest.url.replace(/^http:\/\//i, "https://");
        var redirectTo = 'window.location = "' + newUri + '"';
        (_webviewRef$current1 = webviewRef.current) == null || _webviewRef$current1.injectJavaScript(redirectTo);
        return false;
      }
      canGoBackForPhysicalBack = newRequest == null ? undefined : newRequest.canGoBack;
      setCanGoBack(newRequest == null ? undefined : newRequest.canGoBack);
      setDeepLink({
        path: "",
        params: ""
      });
      if ((0, _utils.handleCondition)((url == null ? undefined : url.includes("openInDeviceBrowser")) && (headerType === _enum.WebViewHeaderTypes.appscapadeLP || headerType === _enum.WebViewHeaderTypes.appscapadeInstantWinLP), true, false)) {
        _reactNative.Linking.openURL(url);
        return false;
      }
      if (url.includes(PlayPassDeeplinks.perkspage)) {
        runFunctionWithCondition(isIOS, function () {
          return openPerkPage();
        }, function () {
          return setDeepLink({
            path: PlayPassDeeplinks.perkspage,
            params: ""
          });
        });
        return false;
      }
      if (url.includes(PlayPassDeeplinks.scanBoardingPass)) {
        runFunctionWithCondition(isIOS, function () {
          return openScanBoardingPass();
        }, function () {
          return setDeepLink({
            path: PlayPassDeeplinks.scanBoardingPass,
            params: ""
          });
        });
        return false;
      }
      if (url.includes(PlayPassDeeplinks.searchflights)) {
        runFunctionWithCondition(isIOS, function () {
          return openSearchFlight();
        }, function () {
          return setDeepLink({
            path: PlayPassDeeplinks.searchflights,
            params: ""
          });
        });
        return false;
      }
      handleForAA(newRequest);
      var isGamiDeepLinkItem = _constants.GAMI_DEEPLINKS.find(function (item) {
        return url == null ? undefined : url.startsWith(item);
      });
      if (!!isGamiDeepLinkItem) {
        var _webviewRef$current10;
        webviewRef == null || (_webviewRef$current10 = webviewRef.current) == null || _webviewRef$current10.stopLoading == null || _webviewRef$current10.stopLoading();
        _reactNative.Linking.openURL(url);
        return false;
      }
      if (url.includes(PlayPassDeeplinks.exitCM)) {
        navigation == null || navigation.pop();
        return false;
      }
      if (url.includes(PlayPassDeeplinks.loginappscapade)) {
        runFunctionWithCondition(isIOS, function () {
          return handleLoginAppscapade();
        }, function () {
          return setDeepLink({
            path: PlayPassDeeplinks.loginappscapade,
            params: ""
          });
        });
        return false;
      }
      if (url.includes(PlayPassDeeplinks.login)) {
        var params = getParams(url);
        statePlayPass.current = params;
        var onLoggedIn = /*#__PURE__*/function () {
          var _ref4 = (0, _asyncToGenerator2.default)(function* () {
            setLogIn(true);
            if (!isCM24Page) return;
            var query = url.replace(PlayPassDeeplinks.login, "");
            query = /^\?/.test(query) ? query.slice(1) : query;
            try {
              var getLink = yield (0, _pageConfigSaga.getDeepLinkV2)({
                stateCode: _constants.StateCode.CM24,
                input: {
                  receivedFromLink: query
                }
              }, true);
              if (getLink != null && getLink.redirectUri) {
                navigation.navigate(_constants.NavigationConstants.playpassWebview, {
                  uri: getLink == null ? undefined : getLink.redirectUri,
                  basicAuthCredential: getLink == null ? undefined : getLink.basicAuth,
                  headerType: headerType
                });
              }
            } catch (_unused) {
              navigation.navigate(_constants.NavigationConstants.explore, {
                bottomSheetErrorHash: new Date().getTime(),
                isOpenApp: false
              });
            }
          });
          return function onLoggedIn() {
            return _ref4.apply(this, arguments);
          };
        }();
        runFunctionWithCondition(isLoggedIn, onLoggedIn, function () {
          navigation.navigate(_constants.NavigationConstants.authScreen, {
            sourceSystem: isCM24Page ? _constants.SOURCE_SYSTEM.CHANGI_MILLIONAIRE : _constants.SOURCE_SYSTEM.PLAY_PASS,
            callBackAfterLoginSuccess: onLoggedIn
          });
        });
        return false;
      }
      if (url.includes(PlayPassDeeplinks.openCamera)) {
        shouldOpenGallery.current = url === PlayPassDeeplinks.openImageGallery;
        onUploadReceiptScreen(CameraType.default);
        return false;
      }
      if ((0, _utils.ifAllTrue)([url.includes(PlayPassDeeplinks.openCamera), headerType === _enum.WebViewHeaderTypes.changiMillionaire])) {
        onUploadReceiptScreen(CameraType.uploadCM);
        return false;
      }
      if (url.includes(PlayPassDeeplinks.scanQR)) {
        onUploadReceiptScreen(CameraType.scanCM);
        return false;
      }
      if (url.includes(PlayPassDeeplinks.scanBarCode)) {
        onUploadReceiptScreen(CameraType.scanBarCode);
        return false;
      }
      if (url.includes(PlayPassDeeplinks.exploreHomePage)) {
        runFunctionWithCondition(isIOS, goToExploreChangi, setDeepLink({
          path: PlayPassDeeplinks.exploreHomePage,
          params: ""
        }));
        return false;
      }
      if (url.startsWith(PlayPassDeeplinks.wallet)) {
        runFunctionWithCondition(isIOS, goToWallet, setDeepLink({
          path: PlayPassDeeplinks.wallet,
          params: ""
        }));
        return false;
      }
      if (url.startsWith(PlayPassDeeplinks.eCard)) {
        runFunctionWithCondition(isIOS, goToECard, setDeepLink({
          path: PlayPassDeeplinks.eCard,
          params: ""
        }));
        return false;
      }

      // Handle dynamic link
      var decodedUrl = decodeURIComponent(url);
      var dynamicLinkPrefix = (0, _envSettings.getEnvSetting)().DYNAMIC_LINK_PREFIX;
      var isDynamicLink = decodedUrl == null ? undefined : decodedUrl.startsWith(dynamicLinkPrefix);
      if (isDynamicLink) {
        var _urlParse, _parsedUrl$query;
        var parsedUrl = (0, _urlParse2.default)(decodedUrl, true);
        var modeValue = (_urlParse = (0, _urlParse2.default)(parsedUrl == null || (_parsedUrl$query = parsedUrl.query) == null ? undefined : _parsedUrl$query.link, true)) == null || (_urlParse = _urlParse.query) == null ? undefined : _urlParse.mode;
        if (modeValue === _deeplinkParameter.MODE_CODE.baggage_prediction) {
          var _ref5 = (parsedUrl == null ? undefined : parsedUrl.query) || {},
            terminal = _ref5.terminal;
          handleOpenL3BaggagePrediction({
            queryData: {
              terminal: terminal
            },
            goToExternalLink: goToNewUri
          });
          return false;
        }
      }
      if (url) {
        var urlObj = new URL(url);
        var searchParams = urlObj == null ? undefined : urlObj.searchParams;
        var link = searchParams != null && searchParams.get != null && searchParams.get("link") ? new URL(decodeURIComponent(searchParams == null || searchParams.get == null ? undefined : searchParams.get("link"))) : null;
        var linkParams = link == null ? undefined : link.searchParams;
        var mode = linkParams == null || linkParams.get == null ? undefined : linkParams.get("mode");
        if (mode === _deeplinkParameter.MODE_CODE.retro_claims) {
          (0, _useRetroClaims.goToRetroClaims)({
            navigation: navigation
          });
          return false;
        }
      }
      return true;
    };

    /**
     * Function to render the dummy view in the header
     * if we don't need CTA button in the header, to make the title alignment center a dummy view is used.
     */

    var onClosedSheet = (0, _react.useCallback)(function () {
      shouldOpenGallery.current = false;
      setIsBottomSheetVisible(false);
    }, []);
    var onClosedSheetCM = (0, _react.useCallback)(function () {
      setIsBottomSheetCMVisible(false);
    }, []);
    var onClosedSheetQRScan = (0, _react.useCallback)(function () {
      setIsBottomSheetQRScanVisible(false);
      setIsBottomSheetBarCodeScanVisible(false);
    }, []);
    var playpassOnLoadEnd = function playpassOnLoadEnd(syntheticEvent) {
      var nativeEvent = syntheticEvent.nativeEvent;
      if (!(0, _lodash.isEmpty)(nativeEvent)) {
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.PlayPassWebView, (0, _screenHook.getPreviousScreen)(), isLogIn);
      }
    };
    var reloadScreen = function reloadScreen() {
      var _webviewRef$current11;
      (_webviewRef$current11 = webviewRef.current) == null || _webviewRef$current11.reload();
    };
    var handlePopScreen = function handlePopScreen() {
      var caculateValue = caculateNavigation();
      var step = (0, _utils.handleCondition)(caculateValue, caculateValue, 0);
      navigation.pop(step);
    };
    var onLoadEnd = function onLoadEnd(event) {
      var _event$nativeEvent;
      setCanGoBack(event == null || (_event$nativeEvent = event.nativeEvent) == null ? undefined : _event$nativeEvent.canGoBack);
    };
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.PlayPassWebView);
    var handleHeader = function handleHeader() {
      var HeaderComponent = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _enum.WebViewHeaderTypes.default, (0, _jsxRuntime.jsx)(_defaultHeader.default, {
        title: title,
        needBackButton: needBackButton,
        needCloseButton: needCloseButton,
        canGoBack: canGoBack,
        onBack: onBack,
        onBackButtonPressed: onBackButtonPressed,
        onCloseButtonPressed: onCloseButtonPressed,
        webViewTitle: webViewTitle
      })), _enum.WebViewHeaderTypes.changiMillionaire, (0, _jsxRuntime.jsx)(_changiMillionaireHeader.default, {
        onBackButtonPressed: onBackButtonPressed,
        reloadScreen: reloadScreen
      })), _enum.WebViewHeaderTypes.appscapadeLP, (0, _jsxRuntime.jsx)(_appscapadeHeader.default, {
        title: title,
        onReload: reloadScreen,
        webViewTitle: webViewTitle,
        onCloseScreen: handlePopScreen,
        isDisable: false
      })), _enum.WebViewHeaderTypes.appscapadeInstantWinLP, (0, _jsxRuntime.jsx)(_appscapadeHeader.default, {
        title: title,
        onReload: reloadScreen,
        webViewTitle: webViewTitle,
        onCloseScreen: onCloseButtonPressed || handlePopScreen,
        isDisable: false
      }));
      return HeaderComponent == null ? undefined : HeaderComponent[headerType];
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(WebViewContainer, {
        style: container,
        children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
          translucent: true,
          backgroundColor: "transparent",
          barStyle: "dark-content"
        }), handleHeader(), (0, _jsxRuntime.jsx)(_extendedWebview.ExtendedWebView, {
          isPlayPassView: true,
          uri: uri,
          webviewRef: webviewRef,
          username: (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.PLAYPASS_USERNAME,
          password: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.PLAYPASS_PASSWORD,
          navigationHandler: deepLinkHandler,
          needCredentialDecoding: true,
          onTitleChange: setWebViewTitle,
          onNavigationStateChange: onNavigationStateChange,
          loading: loading || loadingFAQ,
          basicAuthCredential: basicAuthCredential,
          playpassOnLoadEnd: playpassOnLoadEnd,
          incognito: !isLoggedIn,
          onLoad: onLoadEnd,
          shouldShowLoadingCookies: shouldShowLoadingCookies,
          loadingCookiesStyles: loadingCookiesStyles,
          loadingCookiesSection: loadingCookiesSection,
          handleForAA: handleForAA
        })]
      }), (0, _jsxRuntime.jsx)(_bottomSheet.default, {
        isModalVisible: isBottomSheetVisible,
        onClosedSheet: onClosedSheet,
        containerStyle: bottomSheetStyle,
        stopDragCollapse: true,
        onBackPressHandle: onClosedSheet,
        animationInTiming: 200,
        animationOutTiming: 200,
        children: (0, _jsxRuntime.jsx)(_takeUploadPhoto.default, {
          isCameraGranted: true,
          onClosedSheet: onClosedSheet,
          handleTakePicture: handleTakePicture,
          shouldOpenGallery: shouldOpenGallery.current
        })
      }), (0, _jsxRuntime.jsx)(_bottomSheet.default, {
        isModalVisible: isBottomSheetCMVisible,
        onClosedSheet: onClosedSheetCM,
        containerStyle: bottomSheetStyle,
        stopDragCollapse: true,
        onBackPressHandle: onClosedSheetCM,
        animationInTiming: 200,
        animationOutTiming: 200,
        children: (0, _jsxRuntime.jsx)(_uploadReceiptCm.default, {
          onClosedSheet: onClosedSheetCM,
          handleTakePicture: handleTakePictureCM
        })
      }), (0, _jsxRuntime.jsx)(_bottomSheet.default, {
        isModalVisible: (0, _utils.ifOneTrue)([isBottomSheetQRScanisible, isBottomSheetBarCodeScanVisible]),
        onClosedSheet: onClosedSheetQRScan,
        containerStyle: bottomSheetStyle,
        stopDragCollapse: true,
        onBackPressHandle: onClosedSheetQRScan,
        animationInTiming: 200,
        animationOutTiming: 200,
        children: (0, _jsxRuntime.jsx)(_scanQrCm.default, {
          onClosedSheet: onClosedSheetQRScan,
          handleQRScan: handleQRScan,
          handleUploadCM: handleUploadCM,
          isBottomSheetQRScanisible: isBottomSheetQRScanisible,
          isBottomSheetBarCodeScanVisible: isBottomSheetBarCodeScanVisible
        })
      })]
    });
  };
