  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Chip = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var React = _react;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "Chip";
  var iconStyle = {
    width: 16,
    height: 16,
    color: _theme.color.palette.darkestGrey
  };
  var iconActiveStyle = {
    color: _theme.color.palette.lightPurple
  };
  var Chip = exports.Chip = function Chip(props) {
    var _stateMapping$state, _stateMapping$state2;
    var title = props.title,
      onPress = props.onPress,
      iconLeft = props.iconLeft,
      iconRight = props.iconRight,
      state = props.state,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel,
      disabled = props.disabled;
    var iconProps = Object.assign({}, iconStyle, state ? iconActiveStyle : {});
    var stateMapping = {
      applied: {
        container: styles.itemActive,
        text: styles.textActive
      },
      active: {
        container: styles.itemApplied,
        text: styles.textActive
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      accessibilityLabel: `${accessibilityLabel || ""}${COMPONENT_NAME}__Btn__${title}`,
      testID: `${testID || ""}${COMPONENT_NAME}__Btn__${title}`,
      style: [styles.item, (_stateMapping$state = stateMapping[state]) == null ? undefined : _stateMapping$state.container],
      onPress: onPress,
      disabled: disabled,
      children: [iconLeft && (0, _react.cloneElement)(iconLeft, iconProps), (0, _jsxRuntime.jsx)(_text.Text, {
        accessibilityLabel: `${accessibilityLabel || ""}${COMPONENT_NAME}__Name__${title}`,
        testID: `${testID || ""}${COMPONENT_NAME}__Name__${title}`,
        preset: "caption1Bold",
        style: [styles.text, (_stateMapping$state2 = stateMapping[state]) == null ? undefined : _stateMapping$state2.text],
        children: title
      }), iconRight && (0, _react.cloneElement)(iconRight, iconProps)]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    item: {
      flexDirection: "row",
      alignItems: "center",
      gap: 2,
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 99,
      borderWidth: 1,
      height: 30,
      borderColor: _theme.color.palette.lighterGrey
    },
    itemActive: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderColor: _theme.color.palette.purpleD5BBEA,
      paddingRight: 6
    },
    text: {
      color: _theme.color.palette.darkestGrey,
      marginTop: -2,
      marginHorizontal: 2
    },
    textActive: {
      color: _theme.color.palette.lightPurple
    },
    itemApplied: {
      borderColor: _theme.color.palette.lightPurple,
      backgroundColor: _theme.color.palette.whiteGrey
    }
  });
