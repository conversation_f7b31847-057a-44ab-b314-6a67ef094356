  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Empty = undefined;
  var _icons = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _styles = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var Empty = exports.Empty = function Empty(props) {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _styles.noContentStyles.container,
      children: [(0, _jsxRuntime.jsx)(_icons.SearchNoResultsIconSmall, {
        width: 24,
        height: 24
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "bodyTextBlack",
        tx: "searchV2.flightsResult.noFlightResults",
        style: _styles.noContentStyles.noFlightResults
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        style: _styles.noContentStyles.textCenter,
        children: (0, _i18n.translate)("searchV2.flightsResult.noMatching", {
          date: (0, _moment.default)(props.date).format(_dateTime.DateFormats.DayDateMonthYear)
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        style: [_styles.noContentStyles.textCenter, _styles.noContentStyles.suggestionTryAgain],
        tx: "searchV2.flightsResult.suggestionTryAgain"
      })]
    });
  };
