  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExpandablePanel = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _utils = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_HEIGHT = _reactNative2.Dimensions.get("window").height;
  var ExpandablePanel = exports.ExpandablePanel = function ExpandablePanel(_ref) {
    var anchor = _ref.anchor,
      isExpanded = _ref.isExpanded,
      children = _ref.children,
      containerStyle = _ref.containerStyle,
      onDismiss = _ref.onDismiss,
      isPaneAbsoluteToRoot = _ref.isPaneAbsoluteToRoot;
    var animatedHeight = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      buttonLayout = _useState2[0],
      setButtonLayout = _useState2[1];
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      panelMaxHeight = _useState4[0],
      setPanelMaxHeight = _useState4[1];
    (0, _react.useEffect)(function () {
      if (!anchor.current) return;
      anchor.current.measure(function () {
        var x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        var width = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
        var height = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;
        var pageX = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;
        var pageY = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;
        var panelHeight = SCREEN_HEIGHT - (y + height); // space below the button
        setButtonLayout({
          x: pageX,
          y: pageY,
          width: width,
          height: height
        });
        setPanelMaxHeight(panelHeight);
      });
    }, [anchor.current]);
    (0, _react.useEffect)(function () {
      if (!anchor.current) return;
      _reactNative2.Animated.timing(animatedHeight, {
        toValue: isExpanded ? panelMaxHeight : 0,
        duration: 250,
        useNativeDriver: false
      }).start();
    }, [isExpanded, panelMaxHeight, anchor.current]);
    if (!buttonLayout) return null;
    return (0, _jsxRuntime.jsxs)(_reactNative2.Animated.View, {
      style: [styles.panel, {
        top: (0, _utils.simpleCondition)({
          condition: isPaneAbsoluteToRoot,
          ifValue: buttonLayout.y + buttonLayout.height,
          elseValue: buttonLayout.height
        }),
        left: buttonLayout.x,
        width: buttonLayout.width,
        height: animatedHeight
      }, containerStyle],
      children: [(0, _jsxRuntime.jsx)(_reactNative.Pressable, {
        style: styles.backdrop,
        onPress: onDismiss
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.contentStyle,
        children: children
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    panel: {
      position: "absolute",
      backgroundColor: `${_theme.color.palette.darkestGrey}99`,
      overflow: "hidden",
      zIndex: 999,
      bottom: 0
    },
    backdrop: Object.assign({}, _reactNative2.StyleSheet.absoluteFillObject, {
      backgroundColor: `${_theme.color.palette.black}66`
    }),
    contentStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    }
  });
