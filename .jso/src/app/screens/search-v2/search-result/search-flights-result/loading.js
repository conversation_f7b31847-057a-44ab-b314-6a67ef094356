  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LoadingView = undefined;
  var _constants = _$$_REQUIRE(_dependencyMap[1]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var skeletonLayout = [{
    width: 48,
    height: 48,
    borderRadius: 8
  }, {
    width: 271,
    height: 12,
    marginLeft: _theme.spacing[4],
    marginBottom: _theme.spacing[3],
    borderRadius: 4
  }, {
    width: 148,
    height: 12,
    marginLeft: _theme.spacing[4],
    marginBottom: _theme.spacing[3],
    borderRadius: 4
  }, {
    width: 80,
    height: 12,
    marginLeft: _theme.spacing[4],
    borderRadius: 4
  }];
  var LoadingView = exports.LoadingView = function LoadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [_styles.flightItemStyle.container, _styles.flightItemStyle.itemMarginTop],
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayout[0]
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _styles.flightItemStyle.flightDetailContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayout[1]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayout[2]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayout[3]
          })]
        })
      })]
    });
  };
