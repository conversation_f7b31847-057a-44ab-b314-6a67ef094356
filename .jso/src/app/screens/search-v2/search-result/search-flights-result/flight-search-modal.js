  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightSearchModal = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _searchTabFlights = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var FlightSearchModal = exports.FlightSearchModal = function FlightSearchModal(props) {
    var _props$route;
    var _props$route$params = props == null || (_props$route = props.route) == null ? undefined : _props$route.params,
      keyword = _props$route$params.keyword,
      _props$route$params$d = _props$route$params.date,
      date = _props$route$params$d === undefined ? (0, _moment.default)() : _props$route$params$d,
      _props$route$params$d2 = _props$route$params.direction,
      direction = _props$route$params$d2 === undefined ? _constants.FlightDirection.Arrival : _props$route$params$d2,
      onGoBack = _props$route$params.onGoBack;
    return (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
      activeOpacity: 1,
      style: styles.backdrop,
      onPress: function onPress() {
        var _props$navigation;
        return (_props$navigation = props.navigation) == null ? undefined : _props$navigation.goBack();
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.container,
        onStartShouldSetResponder: function onStartShouldSetResponder() {
          return true;
        },
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.header,
          preset: "bodyTextBold",
          tx: "searchV2.flightsTab.searchFlights"
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.closeButton,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {
            width: 24,
            height: 24,
            onPress: function onPress() {
              return props.navigation.goBack();
            }
          })
        }), (0, _jsxRuntime.jsx)(_searchTabFlights.default, {
          keyword: keyword,
          date: date,
          direction: direction,
          onSearch: function onSearch(data) {
            onGoBack(data);
            props.navigation.goBack();
          }
        })]
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    backdrop: {
      flex: 1,
      backgroundColor: "rgba(18, 18, 18, 0.80)",
      flexDirection: "column-reverse",
      display: "flex"
    },
    container: {
      backgroundColor: _theme.color.palette.whiteGrey,
      flexDirection: "column",
      borderTopStartRadius: 16,
      borderTopEndRadius: 16,
      height: 749
    },
    header: {
      paddingVertical: 20,
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    },
    closeButton: {
      width: 20,
      height: 20,
      top: 20,
      right: _theme.spacing[4],
      position: "absolute"
    }
  });
