  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _chip = _$$_REQUIRE(_dependencyMap[7]);
  var _ExpandablePanel = _$$_REQUIRE(_dependencyMap[8]);
  var _direction = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _location = _$$_REQUIRE(_dependencyMap[11]);
  var _flightFilter = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[13]);
  var _airline = _$$_REQUIRE(_dependencyMap[14]);
  var _airportCity = _$$_REQUIRE(_dependencyMap[15]);
  var _consts = _$$_REQUIRE(_dependencyMap[16]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  var COMPONENT_NAME = "SearchV2_FlightFilter_Facets";
  var iconStyle = {
    width: 16,
    height: 16,
    color: _theme.color.palette.darkestGrey
  };
  var iconAppliedStyle = {
    color: _theme.color.palette.lightPurple
  };
  var FacetID = /*#__PURE__*/function (FacetID) {
    FacetID["Direction"] = "Direction";
    FacetID["Terminal"] = "Terminal";
    FacetID["Airline"] = "Airline";
    FacetID["AirportCity"] = "AirportCity";
    return FacetID;
  }(FacetID || {});
  var Category = function Category(props) {
    var filter = props.filter,
      _props$isShowDirectio = props.isShowDirection,
      isShowDirection = _props$isShowDirectio === undefined ? true : _props$isShowDirectio,
      _props$isPaneAbsolute = props.isPaneAbsoluteToRoot,
      isPaneAbsoluteToRoot = _props$isPaneAbsolute === undefined ? true : _props$isPaneAbsolute,
      _props$componentName = props.componentName,
      componentName = _props$componentName === undefined ? COMPONENT_NAME : _props$componentName,
      containerStyle = props.containerStyle,
      onSearchPress = props.onSearchPress,
      onFacetStateChange = props.onFacetStateChange,
      disabledCategory = props.disabledCategory;
    var flightFilterOptions = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptions);
    var _useModal = (0, _useModal2.useModal)("flightResultCategoryFilter"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var _useState = (0, _react.useState)(undefined),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      selectedTab = _useState2[0],
      setSelectedTab = _useState2[1];
    var locations = (0, _react.useMemo)(function () {
      return !filter.terminal || filter.terminal.length === _consts.defaultListLocation.length - 1 ? _consts.defaultListLocation.map(function (item) {
        return Object.assign({}, item, {
          selected: true
        });
      }) : _consts.defaultListLocation.map(function (item) {
        var _filter$terminal;
        return Object.assign({}, item, {
          selected: (_filter$terminal = filter.terminal) == null ? undefined : _filter$terminal.some(function (v) {
            return v === item.tagName;
          })
        });
      });
    }, [filter.terminal]);
    var anchorRef = (0, _react.useRef)(null);
    var facetList = (0, _react.useMemo)(function () {
      var _flightFilterOptions$, _flightFilterOptions$2;
      return [isShowDirection && {
        id: FacetID.Direction,
        title: "Direction",
        iconLeft: (filter == null ? undefined : filter.direction) === _constants.FlightDirection.Arrival ? (0, _jsxRuntime.jsx)(_icons.PlaneOutlineArrival, {}) : (0, _jsxRuntime.jsx)(_icons.PlaneOutlineDeparture, {}),
        values: [(filter == null ? undefined : filter.direction) === _constants.FlightDirection.Arrival ? "Arrival" : "Departure"]
      }, {
        id: FacetID.Terminal,
        title: "Terminal",
        iconLeft: (0, _jsxRuntime.jsx)(_icons.LocationOutline, {}),
        values: locations[0].selected ? [] : locations.filter(function (item) {
          return item.selected;
        }).map(function (item) {
          return item.tagCode;
        })
      }, {
        id: FacetID.Airline,
        title: "Airline",
        iconLeft: (0, _jsxRuntime.jsx)(_icons.PlaneOutlineIcon, {}),
        values: (filter == null ? undefined : filter.airline) && [flightFilterOptions == null || (_flightFilterOptions$ = flightFilterOptions.airline) == null ? undefined : _flightFilterOptions$.find(function (airlineElement) {
          return (airlineElement == null ? undefined : airlineElement.value) === filter.airline;
        }).name]
      }, {
        id: FacetID.AirportCity,
        title: "Airport/City",
        iconLeft: (0, _jsxRuntime.jsx)(_icons.PublicAreaIcon, {}),
        values: (filter == null ? undefined : filter.cityAirport) && [flightFilterOptions == null || (_flightFilterOptions$2 = flightFilterOptions.cityAirport) == null ? undefined : _flightFilterOptions$2.find(function (cityAirportElement) {
          return (cityAirportElement == null ? undefined : cityAirportElement.value) === filter.cityAirport;
        }).name]
      }].filter(Boolean);
    }, [filter == null ? undefined : filter.direction, locations, filter == null ? undefined : filter.airline, filter.cityAirport]);
    var onRemoveSelectedTab = function onRemoveSelectedTab() {
      setSelectedTab(undefined);
      if (onFacetStateChange && typeof onFacetStateChange === 'function') {
        onFacetStateChange(false);
      }
    };
    var renderHeader = function renderHeader() {
      var hasFilterTerminals = !locations[0].selected && (filter == null ? undefined : filter.terminal);
      var isFilterActive = !!hasFilterTerminals || !!(filter != null && filter.airline) || !!filter.cityAirport;
      var iconProps = Object.assign({}, iconStyle, isFilterActive ? iconAppliedStyle : {});
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: {
          flexDirection: "row",
          gap: 4
        },
        children: [!!onSearchPress && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          disabled: disabledCategory,
          onPress: onSearchPress,
          testID: `${componentName}__Search`,
          accessibilityLabel: `${componentName}__Search`,
          style: [styles.item, {
            height: 30
          }],
          children: (0, _jsxRuntime.jsx)(_icons.SearchIconV2, Object.assign({}, iconStyle))
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          disabled: disabledCategory,
          onPress: onPressFilter,
          testID: `${componentName}__FilterAll`,
          accessibilityLabel: `${componentName}__FilterAll`,
          style: [styles.item, isFilterActive && Object.assign({}, styles.itemActive, styles.paddingRight10), {
            height: 30
          }],
          children: [(0, _jsxRuntime.jsx)(_icons.FilterV2, Object.assign({}, iconProps)), isFilterActive && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.dotActive
          })]
        })]
      });
    };
    var handleFilterFlight = function handleFilterFlight(newFilter) {
      props.onFilterFlight({
        terminal: newFilter.terminal,
        direction: newFilter.direction,
        airline: newFilter.airline === _consts.FILTER_ALL.toLowerCase() ? undefined : newFilter.airline,
        cityAirport: newFilter.cityAirport === _consts.FILTER_ALL.toLowerCase() ? undefined : newFilter.cityAirport
      });
      onRemoveSelectedTab();
    };
    var onPressFilter = function onPressFilter() {
      openModal();
    };
    var onPressItem = function onPressItem(item) {
      if (selectedTab === item.id) {
        onRemoveSelectedTab();
        return;
      }
      setSelectedTab(item.id);
      if (onFacetStateChange && typeof onFacetStateChange === "function") {
        onFacetStateChange(true);
      }
    };
    var renderItem = function renderItem(_ref) {
      var _item$values, _item$values2, _item$values3;
      var item = _ref.item,
        index = _ref.index;
      if (!item) return null;
      var isActive = !!(item != null && (_item$values = item.values) != null && _item$values.length);
      var state = undefined;
      if (!!(item != null && (_item$values2 = item.values) != null && _item$values2.length)) state = "applied";
      if (selectedTab && selectedTab == item.id) state = "active";
      var iconProps = Object.assign({}, iconStyle, isActive ? iconAppliedStyle : {});
      var icon = (0, _jsxRuntime.jsx)(_icons.ArrowDown, Object.assign({}, iconProps));
      if (state === "active") {
        icon = (0, _jsxRuntime.jsx)(_icons.AccordionUp, Object.assign({}, iconProps));
      }
      return (0, _jsxRuntime.jsx)(_chip.Chip, {
        title: (item == null || (_item$values3 = item.values) == null ? undefined : _item$values3.join(", ")) || item.title,
        state: state,
        onPress: function onPress() {
          return onPressItem(item);
        },
        iconLeft: item.iconLeft,
        iconRight: icon,
        disabled: disabledCategory
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        onLayout: function onLayout() {},
        ref: anchorRef,
        style: [styles.container, containerStyle],
        children: [(0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          horizontal: true,
          contentContainerStyle: styles.contentContainer,
          data: facetList,
          renderItem: renderItem,
          showsHorizontalScrollIndicator: false,
          ListHeaderComponent: renderHeader,
          keyExtractor: function keyExtractor(item, index) {
            return `${item.title}-${index}`;
          }
        }), (0, _jsxRuntime.jsx)(_flightFilter.default, {
          visible: isModalVisible,
          direction: filter.direction,
          airline: filter.airline,
          airport: filter.cityAirport,
          terminal: filter.terminal,
          navigationType: _flightProps.NavigationType.FlightsResult,
          onClosedSheet: closeModal,
          handleApplyFilter: function handleApplyFilter(filterOption) {
            handleFilterFlight({
              terminal: filterOption.terminal,
              direction: filterOption.direction,
              airline: filterOption.airline,
              cityAirport: filterOption.cityAirport
            });
            closeModal();
          },
          testID: `${componentName}__FlightFilterModal`,
          accessibilityLabel: `${componentName}__FlightFilterModal`
        })]
      }), !!selectedTab && (0, _jsxRuntime.jsx)(_ExpandablePanel.ExpandablePanel, {
        anchor: anchorRef,
        isExpanded: !!selectedTab,
        onDismiss: onRemoveSelectedTab,
        isPaneAbsoluteToRoot: isPaneAbsoluteToRoot,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.expandableContent,
          children: [selectedTab === FacetID.Direction && (0, _jsxRuntime.jsx)(_direction.Direction, {
            onSelect: function onSelect(direction) {
              handleFilterFlight({
                terminal: filter.terminal,
                direction: direction,
                airline: filter.airline,
                cityAirport: filter.cityAirport
              });
            },
            direction: filter.direction,
            onClosedSheet: onRemoveSelectedTab
          }), selectedTab === FacetID.Terminal && (0, _jsxRuntime.jsx)(_location.Location, {
            items: locations,
            onApplyFilter: function onApplyFilter(data) {
              handleFilterFlight({
                terminal: data.slice(1).filter(function (t) {
                  return t.selected;
                }).map(function (t) {
                  return t.tagName;
                }),
                direction: filter.direction,
                airline: filter.airline,
                cityAirport: filter.cityAirport
              });
            },
            onClosedSheet: onRemoveSelectedTab
          }), selectedTab === FacetID.Airline && (0, _jsxRuntime.jsx)(_airline.AirLine, {
            airline: filter.airline,
            onClosedSheet: onRemoveSelectedTab,
            onSelect: function onSelect(airline) {
              return handleFilterFlight({
                terminal: filter.terminal,
                direction: filter.direction,
                airline: airline,
                cityAirport: filter.cityAirport
              });
            }
          }), selectedTab === FacetID.AirportCity && (0, _jsxRuntime.jsx)(_airportCity.AirportCity, {
            airport: filter.cityAirport,
            onClosedSheet: onRemoveSelectedTab,
            onSelect: function onSelect(airport) {
              return handleFilterFlight({
                terminal: filter.terminal,
                direction: filter.direction,
                airline: filter.airline,
                cityAirport: airport
              });
            }
          })]
        })
      })]
    });
  };
  var _default = exports.default = Category;
  var styles = _reactNative2.StyleSheet.create({
    container: {
      paddingTop: 10
    },
    contentContainer: {
      gap: 4,
      paddingHorizontal: 16
    },
    item: {
      flexDirection: "row",
      alignItems: "center",
      gap: 2,
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 99,
      borderWidth: 1,
      height: 28,
      borderColor: _theme.color.palette.lighterGrey
    },
    itemActive: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderColor: _theme.color.palette.purpleD5BBEA,
      paddingRight: 6
    },
    paddingRight10: {
      paddingRight: 10
    },
    text: {
      color: _theme.color.palette.darkestGrey
    },
    textActive: {
      color: _theme.color.palette.lightPurple
    },
    dotActive: {
      width: 7,
      height: 7,
      borderWidth: 1,
      borderColor: _theme.color.palette.whiteGrey,
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 10,
      position: "absolute",
      top: 0,
      right: 0
    },
    expandableContent: {
      marginTop: 10,
      borderTopWidth: 1,
      borderStyle: "solid",
      borderColor: _theme.color.palette.lighterGrey
    }
  });
