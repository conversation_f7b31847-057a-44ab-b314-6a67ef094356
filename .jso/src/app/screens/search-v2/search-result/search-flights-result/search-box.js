  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[7]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var COMPONENT = "SearchBoxOnFlightResult_";
  var SearchBottomSheet = function SearchBottomSheet(props) {
    var _props$date = props.date,
      date = _props$date === undefined ? (0, _moment.default)() : _props$date,
      keyword = props.keyword,
      onPress = props.onPress,
      onPressBack = props.onPressBack;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
        onPress: _reactNative2.Keyboard.dismiss,
        hitSlop: {
          top: 30,
          bottom: 10
        },
        style: styles.container,
        testID: `${COMPONENT}PressableContainer`,
        accessibilityLabel: `${COMPONENT}PressableContainer`,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.iconContainer,
          onPress: onPressBack,
          testID: `${COMPONENT}TouchableBack`,
          accessibilityLabel: `${COMPONENT}TouchableBack`,
          children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
            color: _theme.color.palette.darkestGrey
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
          style: styles.inputContainer,
          onPress: onPress,
          children: [(0, _jsxRuntime.jsx)(_icons.SearchBox, {
            width: 20,
            height: 20,
            style: styles.searchIconStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.inputTextWrapper,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.keyword,
              preset: "bodyTextRegular",
              ellipsizeMode: "tail",
              numberOfLines: 1,
              children: keyword
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption2Regular",
              children: date == null ? undefined : date.format(_dateTime.DateFormats.DayDateMonthYear)
            })]
          })]
        })]
      })
    });
  };
  var _default = exports.default = (0, _react.memo)(SearchBottomSheet);
  var styles = _reactNative2.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      paddingLeft: _theme.spacing[4],
      paddingRight: 20,
      flexDirection: "row",
      alignItems: "center",
      marginTop: _reactNative2.Platform.OS === "ios" ? _theme.spacing[4] : _reactNative2.StatusBar.currentHeight + _theme.spacing[4],
      marginBottom: 10
    },
    iconContainer: {
      marginRight: _theme.spacing[3]
    },
    inputContainer: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderWidth: 1,
      borderStyle: "solid",
      borderColor: _theme.color.palette.greyCCCCCC,
      height: 36,
      borderRadius: 8,
      alignItems: "center",
      flexDirection: "row"
    },
    inputTextWrapper: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      gap: _theme.spacing[1],
      paddingRight: 12
    },
    keyword: {
      overflow: "hidden",
      color: _theme.color.palette.almostBlackGrey,
      textAlignVertical: "center",
      flex: 1
    },
    searchIconStyle: {
      marginLeft: _theme.spacing[3],
      marginRight: _theme.spacing[2],
      color: _theme.color.palette.darkGrey999
    }
  });
