  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SectionImageComponent = SectionImageComponent;
  exports.SectionImageComponentType = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _photo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var containerStyle = {
    height: 176.5,
    width: width,
    backgroundColor: _theme.color.palette.lightGrey,
    overflow: "hidden",
    marginBottom: 25
  };
  var backgroundImageStyle = {
    flex: 1,
    justifyContent: "center"
  };
  var textStyle = {
    color: _theme.color.palette.whiteGrey,
    marginLeft: 24,
    width: 232
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var loadingViewStyle = {
    position: "absolute",
    alignSelf: "center",
    top: 80
  };
  var SectionImageComponentType = exports.SectionImageComponentType = /*#__PURE__*/function (SectionImageComponentType) {
    SectionImageComponentType["defaultDarkGradient"] = "defaultDarkGradient";
    SectionImageComponentType["defaultLightGradient"] = "defaultLightGradient";
    SectionImageComponentType["loading"] = "loading";
    return SectionImageComponentType;
  }({});
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: containerStyle
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: loadingViewStyle,
        children: (0, _jsxRuntime.jsx)(_photo.default, {})
      })]
    });
  };
  var defaultView = function defaultView(title, sectionImgUrl) {
    if (!sectionImgUrl) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
      imageStyle: backgroundImageStyle,
      source: {
        uri: sectionImgUrl
      },
      children: (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        style: textStyle,
        numberOfLines: 2,
        children: title
      })
    });
  };
  function SectionImageComponent(props) {
    var sectionImgUrl = props.sectionImgUrl,
      type = props.type,
      title = props.title;
    var isLoading = type === SectionImageComponentType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: containerStyle,
      children: isLoading ? loadingView() : defaultView(title, sectionImgUrl)
    });
  }
