  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MonarchCRCard = MonarchCRCard;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var React = _react;
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _monarchCrCard = _$$_REQUIRE(_dependencyMap[11]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[12]);
  var _getConfigurationPermission = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _lodash = _$$_REQUIRE(_dependencyMap[16]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[18]);
  var _monarchCrCard2 = _$$_REQUIRE(_dependencyMap[19]);
  var _icons = _$$_REQUIRE(_dependencyMap[20]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[21]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var monarchGradientColor = ["rgba(0, 0, 0, 1)", "rgba(205, 212, 217, 0.96)"];
  var gradientDisabledTheme = ["#E5E5E5", "#E5E5E5"];
  var monarchMemberTextColor = _theme.color.palette.almostBlackGrey;
  var midGreyLoadingColors = [_theme.color.palette.midGrey, _theme.color.background, _theme.color.palette.midGrey];
  var lightGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayoutImage = [{
    height: 18,
    width: 126,
    borderRadius: 4,
    marginLeft: 16
  }, {
    height: 18,
    width: 82,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 18
  }, {
    height: 5,
    borderRadius: 12,
    marginLeft: 16,
    marginTop: 18,
    width: "90%"
  }, {
    height: 10,
    width: 161,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 20,
    marginBottom: 31
  }];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _monarchCrCard2.styles.skeletonContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _monarchCrCard2.styles.skeletonHeaderStyle,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: midGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[0]
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _monarchCrCard2.styles.skeletonImageStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[3]
        })]
      })]
    });
  };
  var handleGradientTheme = function handleGradientTheme() {
    var theme = monarchGradientColor;
    var textColor = monarchMemberTextColor;
    return {
      theme: theme,
      textColor: textColor
    };
  };
  var defaultView = function defaultView(props) {
    var membershipText = props.membershipText,
      points = props.points,
      onPressed = props.onPressed,
      disabled = props.disabled,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "MonarchCRCardType" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "MonarchCRCardType" : _props$accessibilityL,
      disableEntryCR = props.disableEntryCR,
      onCheckEntryPoint = props.onCheckEntryPoint;
    var rewardPoints = (0, _react.useMemo)(function () {
      if (points) {
        var pointSubstr = points.substring(0, 8);
        if (points.length > 10) {
          return pointSubstr;
        }
        return points;
      }
      return "";
    }, [points]);
    var _React$useState = React.useState(false),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      loadingGetConfig = _React$useState2[0],
      setLoadingGetConfig = _React$useState2[1];
    var _useGetConfigurationP = (0, _getConfigurationPermission.useGetConfigurationPermissionHelper)(),
      getConfigApp = _useGetConfigurationP.getConfigApp,
      notifyDisableChangiRewards = _useGetConfigurationP.notifyDisableChangiRewards;
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var tierIconsAEM = (0, _lodash.get)(dataCommonAEM, "data.pageLanding.explore.tierIcons");
    var monarchTierIcon = tierIconsAEM == null ? undefined : tierIconsAEM.find(function (e) {
      return e.tier === "Monarch";
    }).icon;
    var colorTheme = handleGradientTheme();
    var gradientTheme = colorTheme == null ? undefined : colorTheme.theme;
    var pointsLabelTextStyle = {
      color: colorTheme == null ? undefined : colorTheme.textColor
    };
    var pointsNumberTextStyle = {
      color: colorTheme == null ? undefined : colorTheme.textColor
    };
    var titleText = (0, _i18n.translate)("changiRewardsMemberCard.changiRewards");
    var handlePointText = function handlePointText(point) {
      try {
        return parseFloat(point);
      } catch (e) {
        return 0;
      }
    };
    var handlePress = function handlePress() {
      getConfigApp({
        configKey: _constants.AppConfigPermissionTypes.changiappCREnabled,
        onTap: function onTap() {
          return setLoadingGetConfig(true);
        },
        callbackSuccess: function callbackSuccess() {
          onCheckEntryPoint(false);
          setLoadingGetConfig(false);
          onPressed == null || onPressed();
        },
        callbackFailure: function callbackFailure() {
          onCheckEntryPoint(true);
          setLoadingGetConfig(false);
          notifyDisableChangiRewards();
        }
      });
    };
    if (disableEntryCR) {
      return (0, _jsxRuntime.jsxs)(React.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _monarchCrCard2.styles.containerDisabledStyle,
          disabled: false,
          onPress: handlePress,
          testID: `${testID}__TouchableRewardMemberCard`,
          accessibilityLabel: `${testID}__TouchableRewardMemberCard`,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _monarchCrCard2.styles.imageBackgroundJewelCard,
            children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              style: _monarchCrCard2.styles.gradientContainerStyle,
              start: {
                x: 0.3558,
                y: 0
              },
              end: {
                x: 0.9436,
                y: 0
              },
              colors: gradientDisabledTheme,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption2Bold",
                style: _monarchCrCard2.styles.titleDisabledStyle,
                children: "CHANGI REWARDS"
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _monarchCrCard2.styles.wrapDisabledInfor,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: "We\u2019re currently unable to retrieve your membership details",
                preset: "caption1Bold",
                style: _monarchCrCard2.styles.captionDisabled
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: "Please try again later.",
                preset: "caption2Regular",
                style: _monarchCrCard2.styles.descriptionDisabled
              })]
            })]
          })
        }), loadingGetConfig && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _monarchCrCard2.styles.overLayLoading,
          children: (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
            visible: loadingGetConfig
          })
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(React.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _monarchCrCard2.styles.containerStyle,
        disabled: disabled,
        onPress: handlePress,
        testID: `${testID}__TouchableRewardMemberCard`,
        accessibilityLabel: `${accessibilityLabel}__TouchableRewardMemberCard`,
        children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _monarchCrCard2.styles.gradientContainerStyle,
          start: {
            x: 0.3558,
            y: 0
          },
          end: {
            x: 1,
            y: 0
          },
          colors: gradientTheme,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption2Bold",
            style: _monarchCrCard2.styles.titleStyle,
            children: titleText
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _monarchCrCard2.styles.wrapContentCardStyles,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _monarchCrCard2.styles.leftContentCardStyles,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _monarchCrCard2.styles.pointsContainerStyle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "h3",
                style: [_monarchCrCard2.styles.pointTextStyle, pointsNumberTextStyle],
                numberOfLines: 1,
                children: rewardPoints || "0"
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption1Regular",
                style: [pointsLabelTextStyle, _monarchCrCard2.styles.pointsLabelTextMarginStyle],
                numberOfLines: 1,
                tx: handlePointText(points) > 0 ? "changiRewardsMemberCard.points" : "changiRewardsMemberCard.point"
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption2Regular",
              style: _monarchCrCard2.styles.qualificationStyle,
              numberOfLines: 1,
              children: membershipText ? membershipText : ""
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _monarchCrCard2.styles.rightContentCardStyles,
            children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: (0, _mediaHelper.handleImageUrl)(monarchTierIcon)
              },
              style: _monarchCrCard2.styles.tierIconStyles
            }), (0, _jsxRuntime.jsx)(_icons.MonarchText, {})]
          })]
        })]
      }), loadingGetConfig && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _monarchCrCard2.styles.overLayLoading,
        children: (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
          visible: loadingGetConfig
        })
      })]
    });
  };
  function MonarchCRCard(props) {
    var isLoading = _monarchCrCard.MonarchCRCardType.loading === props.type;
    return isLoading ? loadingView() : defaultView(props);
  }
