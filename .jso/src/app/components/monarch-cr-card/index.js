  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _monarchCrCard = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_monarchCrCard).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _monarchCrCard[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _monarchCrCard[key];
      }
    });
  });
  var _monarchCrCard2 = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_monarchCrCard2).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _monarchCrCard2[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _monarchCrCard2[key];
      }
    });
  });
