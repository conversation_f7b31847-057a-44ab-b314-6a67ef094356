  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var CARD_WIDTH = width - 48;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    leftContentCardStyles: {
      width: CARD_WIDTH * 0.75,
      paddingLeft: 16
    },
    rightContentCardStyles: {
      marginRight: 28,
      width: 64,
      height: 64,
      alignItems: 'center'
    },
    wrapContentCardStyles: {
      flexDirection: "row",
      alignItems: "center",
      flex: 2,
      justifyContent: 'space-between'
    },
    tierIconStyles: {
      width: 48,
      height: 48,
      marginBottom: 8
    },
    monarchTextStyles: Object.assign({}, _text.presets.caption2Regular, {
      fontSize: 8,
      textAlign: "center",
      margin: 0,
      width: 48,
      padding: 0,
      color: "rgba(0, 19, 0, 1)",
      letterSpacing: 0.4
    }),
    containerStyle: Object.assign({
      minHeight: 160
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 20,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 6
        }
      },
      android: {
        elevation: 3
      }
    }), {
      borderRadius: 17,
      width: CARD_WIDTH,
      backgroundColor: _theme.color.palette.whiteGrey,
      alignSelf: "center",
      flex: 1
    }),
    containerDisabledStyle: Object.assign({}, _reactNative.Platform.select({
      ios: {
        shadowRadius: 20,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 6
        }
      },
      android: {
        elevation: 3
      }
    }), {
      borderRadius: 17,
      width: "100%",
      backgroundColor: _theme.color.palette.whiteGrey,
      alignSelf: "center",
      flex: 1,
      height: 160
    }),
    gradientContainerStyle: {
      width: "100%",
      height: 40,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      justifyContent: "center"
    },
    titleStyle: {
      color: _theme.color.palette.whiteGrey,
      marginLeft: 16,
      letterSpacing: 2.4,
      textTransform: "uppercase"
    },
    pointsContainerStyle: {
      flexDirection: "row",
      alignItems: "flex-end"
    },
    pointTextStyle: {
      marginBottom: -2
    },
    pointsLabelTextMarginStyle: {
      marginLeft: 3
    },
    qualificationTextStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 16,
      marginRight: 24,
      marginTop: 16,
      marginBottom: 24
    },
    skeletonContainerStyle: Object.assign({
      width: "100%",
      borderRadius: 8
    }, _theme.shadow.secondaryShadow),
    skeletonHeaderStyle: {
      width: "100%",
      height: 40,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8,
      justifyContent: "center",
      backgroundColor: _theme.color.palette.lighterGrey
    },
    skeletonImageStyle: {
      backgroundColor: _theme.color.background,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 8
    },
    wrapDisabledInfor: {
      paddingHorizontal: 16,
      marginTop: 8
    },
    captionDisabled: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left",
      marginBottom: 4
    },
    descriptionDisabled: {
      color: _theme.color.palette.almostBlackGrey
    },
    imageBackgroundJewelCard: {
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8,
      flex: 1
    },
    titleDisabledStyle: {
      color: "#999999",
      marginLeft: 16,
      letterSpacing: 2.4,
      textTransform: "uppercase"
    },
    overLayLoading: {
      width: width,
      height: height,
      position: "absolute"
    },
    qualificationStyle: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.almostBlackGrey,
      marginRight: 24,
      marginTop: 7,
      marginBottom: 0
    })
  });
