  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var widthDevice = _reactNative.Dimensions.get("screen").width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    imageContainer: {
      borderRadius: 12,
      height: (widthDevice - 80) / 3,
      overflow: "hidden",
      width: (widthDevice - 80) / 3
    },
    imageStyles: {
      height: (widthDevice - 80) / 3,
      width: (widthDevice - 80) / 3
    },
    itemContainer: {
      marginTop: 16,
      width: (widthDevice - 80) / 3
    },
    locationStyles: {
      color: _theme.color.palette.darkestGrey,
      marginTop: 4
    },
    titleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 8
    }
  });
