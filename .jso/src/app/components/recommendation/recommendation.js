  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _recommendationProps = _$$_REQUIRE(_dependencyMap[4]);
  var _recommendationStyles = _$$_REQUIRE(_dependencyMap[5]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var lightGreyLoadingColors1 = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lightGreyLoadingColors2 = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayout = [{
    backgroundColor: _theme.color.palette.lighterGrey,
    width: '65%',
    height: 12,
    borderRadius: 4,
    marginTop: 8,
    marginBottom: 8
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: '81%',
    height: 12,
    borderRadius: 4
  }];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _recommendationStyles.styles.itemContainer,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors1,
        shimmerStyle: _recommendationStyles.styles.imageContainer
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors2,
        shimmerStyle: skeletonLayout[0]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors2,
        shimmerStyle: skeletonLayout[1]
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var logoImage = props.logoImage,
      title = props.title,
      locationDisplay = props.locationDisplay;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _recommendationStyles.styles.itemContainer,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _recommendationStyles.styles.imageContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative.Image, {
          source: {
            uri: logoImage
          },
          resizeMode: "cover",
          style: _recommendationStyles.styles.imageStyles
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: title,
        preset: "bodyTextBold",
        numberOfLines: 2,
        style: _recommendationStyles.styles.titleStyles
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: locationDisplay,
        preset: "caption1Regular",
        numberOfLines: 1,
        style: _recommendationStyles.styles.locationStyles
      })]
    });
  };
  var Recommendation = function Recommendation(props) {
    var isLoading = props.state === _recommendationProps.RecommendationState.loading;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      children: isLoading ? loadingView() : defaultView(props)
    });
  };
  var _default = exports.default = Recommendation;
