  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.RedirectType = exports.NavigationType = exports.MenuOptionType = undefined;
  var MenuOptionType = exports.MenuOptionType = /*#__PURE__*/function (MenuOptionType) {
    MenuOptionType["default"] = "default";
    MenuOptionType["loading"] = "loading";
    MenuOptionType["search"] = "search";
    return MenuOptionType;
  }({});
  var RedirectType = exports.RedirectType = /*#__PURE__*/function (RedirectType) {
    RedirectType["External"] = "External";
    RedirectType["Internal"] = "Internal";
    RedirectType["Removal"] = "Removal";
    return RedirectType;
  }({});
  var NavigationType = exports.NavigationType = /*#__PURE__*/function (NavigationType) {
    NavigationType["inapp"] = "in-app";
    NavigationType["external"] = "external";
    NavigationType["deepLink"] = "deep-link";
    return NavigationType;
  }({});
