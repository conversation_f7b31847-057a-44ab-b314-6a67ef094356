  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.MenuOptionV2 = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _menuOption = _$$_REQUIRE(_dependencyMap[8]);
  var _menuOption2 = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var MenuOptionLoading = function MenuOptionLoading() {
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _menuOption2.styles.containerStyleV2,
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _menuOption2.styles.optionPlaceholderStyle,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _menuOption2.lighterGreyLoadingColors,
          shimmerStyle: _menuOption2.styles.loadingIconV2
        })
      })
    });
  };
  var MenuOptionDefault = function MenuOptionDefault(props) {
    var title = props.title,
      txTitle = props.txTitle,
      icon = props.icon,
      _onPress = props.onPress,
      iconComponent = props.iconComponent,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "MenuOptionDefault" : _props$testID,
      _props$disabled = props.disabled,
      disabled = _props$disabled === undefined ? false : _props$disabled;
    var iconSource = typeof icon === "string" ? {
      uri: icon
    } : icon;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      activeOpacity: 0.7,
      style: _menuOption2.styles.containerStyleV2,
      onPress: function onPress() {
        return _onPress == null ? undefined : _onPress(props);
      },
      testID: `${testID}__TouchableMenu`,
      accessibilityLabel: `${testID}__TouchableMenu`,
      disabled: disabled,
      accessible: false,
      children: [iconComponent ? iconComponent : (0, _jsxRuntime.jsx)(_baseImage.default, {
        style: _menuOption2.styles.iconStyle,
        source: iconSource
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        numberOfLines: 1,
        style: _menuOption2.styles.titleStyleV2,
        text: title,
        tx: txTitle
      })]
    });
  };
  var MenuOptionV2 = exports.MenuOptionV2 = function MenuOptionV2(props) {
    return props.type === _menuOption.MenuOptionType.loading ? (0, _jsxRuntime.jsx)(MenuOptionLoading, {}) : (0, _jsxRuntime.jsx)(MenuOptionDefault, Object.assign({}, props));
  };
  var _default = exports.default = MenuOptionV2;
