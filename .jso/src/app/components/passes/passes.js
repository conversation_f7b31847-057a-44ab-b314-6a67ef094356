  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Passes = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeDashedLine = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _passes = _$$_REQUIRE(_dependencyMap[12]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _passes3 = _$$_REQUIRE(_dependencyMap[14]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var iconsDefault = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _passes3.PassesType.event, _passes.Event), _passes3.PassesType.attraction, _passes.Attraction), _passes3.PassesType.pwpGwpPass, _passes.Gift), _passes3.PassesType.counterPass, _passes.BoardingPass), _passes3.PassesType.boardingPass, _passes.BoardingPass), _passes3.PassesType.healthCertificate, _passes.HealthCertificate);
  var LineDotted = function LineDotted() {
    return (0, _jsxRuntime.jsx)(_reactNativeDashedLine.default, {
      axis: "vertical",
      dashThickness: 1,
      dashLength: 6,
      dashGap: 3,
      dashColor: _theme.color.palette.lightGrey,
      style: styles.dottedLine
    });
  };
  var PassesLoading = function PassesLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.cardContainer,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.imageCardContainer,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: styles.shimmerImage
        }, "0999"), (0, _jsxRuntime.jsx)(LineDotted, {})]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.loadingCardContainer,
        children: styles.skeletonView.map(function (style, index) {
          return (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: Object.assign({}, styles.loadingTextStyle, style)
          }, index);
        })
      })]
    });
  };
  var PassesDefault = function PassesDefault(_ref) {
    var type = _ref.type,
      category = _ref.category,
      tag = _ref.tag,
      tagBackground = _ref.tagBackground,
      tagColor = _ref.tagColor,
      title = _ref.title,
      text = _ref.text,
      isPast = _ref.isPast,
      retimed = _ref.retimed,
      image = _ref.image,
      keys = _ref.keys,
      _ref$boldTime = _ref.boldTime,
      boldTime = _ref$boldTime === undefined ? false : _ref$boldTime,
      onPress = _ref.onPress;
    var Icon = (0, _react.useMemo)(function () {
      return iconsDefault[type];
    }, [type]);
    var isCertificate = (0, _react.useMemo)(function () {
      return type === _passes3.PassesType.healthCertificate;
    }, [type]);
    var titleMaxLine = (0, _react.useMemo)(function () {
      return isCertificate ? 2 : 1;
    }, [isCertificate]);
    var _useState = (0, _react.useState)(1),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      opacity = _useState2[0],
      setOpacity = _useState2[1];
    var texts = (0, _react.useMemo)(function () {
      return [].concat(text).map(function (txt, index) {
        var parsedText = txt.trim();
        if (isCertificate && !index) {
          return (0, _toConsumableArray2.default)(parsedText).map(function (singleText, ind) {
            return ind <= 3 ? singleText : "*";
          }).join("");
        }
        return parsedText;
      }).filter(function (e) {
        return !!e;
      });
    }, [text, isCertificate]);
    var pastMainContainerStyle = (0, _react.useMemo)(function () {
      return isPast ? styles.pastMainContainer : {};
    }, [isPast]);
    var pastOpacityStyle = (0, _react.useMemo)(function () {
      return isPast ? styles.pastOpacity : {};
    }, [isPast]);
    var pastContainerStyle = (0, _react.useMemo)(function () {
      return isPast ? styles.pastContainer : {};
    }, [isPast]);
    var trueTagBgColor = (0, _react.useMemo)(function () {
      return (0, _reactNative2.processColor)(tagBackground) ? {
        backgroundColor: tagBackground
      } : {};
    }, [tagBackground]);
    var trueTagColor = (0, _react.useMemo)(function () {
      return (0, _reactNative2.processColor)(tagColor) ? {
        color: tagColor
      } : {};
    }, [tagColor]);
    var getIconDefault = (0, _react.useMemo)(function () {
      if (!!Icon) {
        return (0, _jsxRuntime.jsx)(Icon, {
          width: 60,
          height: 60
        });
      } else {
        if ([_passes3.PassesType.activatedCarpass, _passes3.PassesType.availableCarpass].includes(type)) {
          return (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: _$$_REQUIRE(_dependencyMap[17]),
            style: styles.imageCardStyle
          });
        }
      }
    }, [Icon]);
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: onPress,
      disabled: isPast,
      onPressIn: function onPressIn() {
        return setOpacity(0.2);
      },
      onPressOut: function onPressOut() {
        return setOpacity(1);
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [styles.cardContainer, pastMainContainerStyle],
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.imageCardContainer, pastOpacityStyle, !isPast && {
            opacity: opacity
          }],
          children: [image ? (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: image
            },
            style: styles.imageCardStyle,
            resizeMode: "contain"
          }) : getIconDefault, (0, _jsxRuntime.jsx)(LineDotted, {})]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.textCardContainer, pastContainerStyle, !isPast && {
            opacity: opacity
          }],
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.headerCard,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: [styles.labelCardStyle, pastOpacityStyle],
              text: category
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.tagsWrapperStyle,
              children: [!!retimed && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.tagsRetimedContainerStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  text: retimed,
                  style: styles.tagsRetimedLabelStyle
                })
              }), !!tag && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: Object.assign({}, styles.tagsContainerStyle, trueTagBgColor),
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  text: tag,
                  style: Object.assign({}, styles.tagsLabelStyle, trueTagColor)
                })
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: titleMaxLine,
            style: [styles.titleStyle, pastOpacityStyle],
            text: title
          }), texts.map(function (str, ind) {
            var randomKey = new Date().getTime() + Math.random();
            if (str != null && str.includes(" at ") && boldTime) {
              var index = str == null ? undefined : str.indexOf("at");
              return (0, _jsxRuntime.jsxs)(_text.Text, {
                style: styles.wrapDescription,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: [styles.textStyle, pastOpacityStyle],
                  text: str == null ? undefined : str.slice(0, index + 2)
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: [styles.textStyle, pastOpacityStyle, styles.boldText],
                  text: str == null ? undefined : str.slice(index + 2, str == null ? undefined : str.length)
                })]
              }, `${randomKey}_${index}_${keys}`);
            }
            return (0, _jsxRuntime.jsx)(_text.Text, {
              style: [styles.textStyle, pastOpacityStyle],
              text: str
            }, `${ind}_${keys}_${randomKey}`);
          })]
        })]
      })
    });
  };
  var Passes = exports.Passes = function Passes(props) {
    return props.state === _passes3.PassesState.loading ? (0, _jsxRuntime.jsx)(PassesLoading, {}) : (0, _jsxRuntime.jsx)(PassesDefault, Object.assign({}, props));
  };
