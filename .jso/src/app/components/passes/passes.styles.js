  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.wrapDescription = exports.touchableOpacityWrap = exports.titleStyle = exports.textStyle = exports.textCardContainer = exports.tagsWrapperStyle = exports.tagsRetimedLabelStyle = exports.tagsRetimedContainerStyle = exports.tagsLabelStyle = exports.tagsContainerStyle = exports.skeletonView = exports.shimmerImage = exports.pastOpacity = exports.pastMainContainer = exports.pastContainer = exports.loadingTextStyle = exports.loadingCardContainer = exports.labelCardStyle = exports.imageCardStyle = exports.imageCardContainer = exports.headerCard = exports.dottedLine = exports.cardContainer = exports.boldText = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var isAndroid = _reactNative.Platform.OS === "android";
  var pastMainContainer = exports.pastMainContainer = Object.assign({}, _theme.shadow.noShadow);
  var pastContainer = exports.pastContainer = Object.assign({}, _theme.shadow.noShadow, {
    backgroundColor: "rgba(255,255,255,.5)"
  });
  var pastOpacity = exports.pastOpacity = Object.assign({}, _theme.shadow.noShadow, {
    opacity: 0.5
  });
  var cardContainer = exports.cardContainer = Object.assign({}, !isAndroid ? _theme.shadow.primaryShadow : {}, {
    flexDirection: "row"
  });
  var imageCardContainer = exports.imageCardContainer = Object.assign({}, isAndroid ? _theme.shadow.primaryShadow : {}, {
    justifyContent: "center",
    alignItems: "center",
    width: 79,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16,
    zIndex: 1
  });
  var imageCardStyle = exports.imageCardStyle = {
    // width: "100%",
    width: 60,
    height: 60,
    resizeMode: "contain"
  };
  var textCardContainer = exports.textCardContainer = Object.assign({}, isAndroid ? _theme.shadow.primaryShadow : {}, {
    flex: 1,
    paddingTop: 12,
    paddingBottom: 16,
    paddingHorizontal: 12,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16
  });
  var headerCard = exports.headerCard = {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  };
  var labelCardStyle = exports.labelCardStyle = Object.assign({}, _text.presets.caption2Bold, {
    flex: 1,
    color: _theme.color.palette.lightPurple,
    textTransform: "uppercase"
  });
  var tagsWrapperStyle = exports.tagsWrapperStyle = {
    flexDirection: "row"
  };
  var tagsContainerStyle = exports.tagsContainerStyle = {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginStart: 8,
    overflow: "hidden"
  };
  var tagsRetimedContainerStyle = exports.tagsRetimedContainerStyle = Object.assign({}, tagsContainerStyle, {
    backgroundColor: _theme.color.palette.lightestBlue
  });
  var tagsLabelStyle = exports.tagsLabelStyle = Object.assign({}, _text.presets.caption2Bold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var tagsRetimedLabelStyle = exports.tagsRetimedLabelStyle = Object.assign({}, tagsLabelStyle, {
    color: _theme.color.palette.blueDark
  });
  var dottedLine = exports.dottedLine = {
    position: "absolute",
    right: -1,
    top: "12.5%",
    height: "75%"
  };
  var titleStyle = exports.titleStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.almostBlackGrey,
    marginTop: 4
  });
  var textStyle = exports.textStyle = Object.assign({}, _text.presets.caption1Regular, {
    marginTop: 4
  });
  var boldText = exports.boldText = {
    fontFamily: _theme.typography.bold,
    marginTop: 4
  };
  var wrapDescription = exports.wrapDescription = {
    marginTop: 4
  };
  var loadingCardContainer = exports.loadingCardContainer = Object.assign({}, _theme.shadow.primaryShadow, {
    flex: 1,
    paddingTop: 20,
    paddingBottom: 21,
    paddingHorizontal: 12,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16
  });
  var skeletonView = exports.skeletonView = [{
    width: 62
  }, {
    width: "100%",
    marginTop: 11
  }, {
    width: "100%",
    marginTop: 8
  }, {
    width: 107,
    marginTop: 8
  }];
  var loadingTextStyle = exports.loadingTextStyle = {
    height: 13,
    borderRadius: 4
  };
  var shimmerImage = exports.shimmerImage = {
    width: "100%",
    height: 120,
    borderRadius: 16
  };
  var touchableOpacityWrap = exports.touchableOpacityWrap = Object.assign({}, isAndroid ? Object.assign({}, _theme.shadow.primaryShadow, {
    borderRadius: 16
  }) : {});
