  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExtendedWebView = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNativeWebview = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeBase = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _storage = _$$_REQUIRE(_dependencyMap[8]);
  var _cookies = _$$_REQUIRE(_dependencyMap[9]);
  var _cookies2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _extendedWebview = _$$_REQUIRE(_dependencyMap[12]);
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var webViewStyle = {
    opacity: 0.99,
    overflow: "hidden"
  };
  var scriptToInject = `
window.ReactNativeWebView.postMessage(document.title)
`;
  var LoadingCookies = function LoadingCookies(_ref) {
    var loadingCookiesStyles = _ref.loadingCookiesStyles;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [_extendedWebview.styles.loadingWrapper, loadingCookiesStyles],
      children: (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: 50
      })
    });
  };
  var ExtendedWebView = exports.ExtendedWebView = function ExtendedWebView(props) {
    var navigationHandler = props.navigationHandler,
      uri = props.uri,
      username = props.username,
      password = props.password,
      needCredentialDecoding = props.needCredentialDecoding,
      webviewRef = props.webviewRef,
      onTitleChange = props.onTitleChange,
      onNavigationStateChange = props.onNavigationStateChange,
      _props$headers = props.headers,
      headers = _props$headers === undefined ? {} : _props$headers,
      _props$basicAuthCrede = props.basicAuthCredential,
      basicAuthCredential = _props$basicAuthCrede === undefined ? {
        username: needCredentialDecoding ? _reactNativeBase.default.decode(username) : username,
        password: needCredentialDecoding ? _reactNativeBase.default.decode(password) : password
      } : _props$basicAuthCrede,
      _props$isPlayPassView = props.isPlayPassView,
      isPlayPassView = _props$isPlayPassView === undefined ? false : _props$isPlayPassView,
      playpassOnLoadEnd = props.playpassOnLoadEnd,
      _props$incognito = props.incognito,
      incognito = _props$incognito === undefined ? false : _props$incognito,
      onError = props.onError,
      onLoadStart = props.onLoadStart,
      onLoad = props.onLoad,
      loadingCookiesSection = props.loadingCookiesSection,
      loadingCookiesStyles = props.loadingCookiesStyles,
      shouldShowLoadingCookies = props.shouldShowLoadingCookies,
      handleForAA = props.handleForAA;
    var _useState = (0, _react.useState)(),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      webViewInterval = _useState2[0],
      setWebViewInterval = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isHaveCookies = _useState4[0],
      setHaveCookies = _useState4[1];
    (0, _react.useEffect)(function () {
      var setCookies = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var authCookies = yield (0, _storage.loadFromEncryptedStorage)(_storage.StorageKey.authCookies);
          if (authCookies) {
            var _loop = function* _loop(parentKey) {
              var dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ";
              var epxireDate = (0, _moment.default)().add(1, "year").format(dateFormat).toString();
              yield Promise.all(Object.entries(childObject).map(function (el) {
                return _cookies2.default.set(parentKey, {
                  name: el[0],
                  value: el[1].value,
                  version: "1",
                  expires: epxireDate
                });
              }));
            };
            for (var _ref3 of Object.entries(authCookies)) {
              var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);
              var parentKey = _ref4[0];
              var childObject = _ref4[1];
              yield* _loop(parentKey);
            }
          }
          setTimeout(function () {
            setHaveCookies(true);
          }, 500);
        });
        return function setCookies() {
          return _ref2.apply(this, arguments);
        };
      }();
      setCookies();
      return function () {
        if (isPlayPassView) {
          (0, _cookies.removeAllCookies)();
        }
      };
    }, []);
    (0, _react.useEffect)(function () {
      return function () {
        return clearInterval(webViewInterval);
      };
    }, [webViewInterval]);
    var handleMessage = function handleMessage(message) {
      if (handleForAA) {
        var jsonData = (0, _utils.parseJsonWebviewMessage)(message);
        var pageL3Info = (0, _lodash.get)(jsonData, "pageL3Info");
        if (!(0, _lodash.isEmpty)(pageL3Info)) return handleForAA(jsonData);
      }
      var title = message.nativeEvent.data;
      var truncatedTitle = title.substring(0, 20);
      onTitleChange(truncatedTitle);
    };
    var handleOnLoadEnd = function handleOnLoadEnd(syntheticEvent) {
      var interval = setInterval(function () {
        var _webviewRef$current;
        webviewRef == null || (_webviewRef$current = webviewRef.current) == null || _webviewRef$current.injectJavaScript(scriptToInject);
      }, 1000);
      setWebViewInterval(interval);
      if (isPlayPassView && !(0, _lodash.isEmpty)(syntheticEvent)) {
        playpassOnLoadEnd(syntheticEvent);
      }
    };
    var checkEncodeURI = function checkEncodeURI(url) {
      return /%/i.test(url);
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [loadingCookiesSection && loadingCookiesSection, !isHaveCookies && shouldShowLoadingCookies && !loadingCookiesSection && (0, _jsxRuntime.jsx)(LoadingCookies, {
        loadingCookiesStyles: loadingCookiesStyles
      }), isHaveCookies && (0, _jsxRuntime.jsx)(_reactNativeWebview.WebView, {
        source: {
          uri: _reactNative.Platform.OS === "ios" && !checkEncodeURI(uri) ? encodeURI(uri) : uri,
          headers: headers
        },
        ref: webviewRef,
        incognito: incognito,
        sharedCookiesEnabled: true,
        javaScriptEnabled: true,
        onLoadEnd: function onLoadEnd(syntheticEvent) {
          return handleOnLoadEnd(syntheticEvent);
        },
        onShouldStartLoadWithRequest: navigationHandler,
        onNavigationStateChange: onNavigationStateChange,
        originWhitelist: ["https://*", "cagichangi://*"],
        basicAuthCredential: basicAuthCredential,
        allowUniversalAccessFromFileURLs: true,
        domStorageEnabled: true,
        onMessage: handleMessage,
        onError: onError,
        setSupportMultipleWindows: false,
        allowsInlineMediaPlayback: true,
        style: webViewStyle,
        cacheEnabled: true,
        onLoadStart: onLoadStart,
        onLoad: onLoad
      })]
    });
  };
