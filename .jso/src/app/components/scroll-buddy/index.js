  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ScrollBuddy = undefined;
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedTouchableOpacity = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.TouchableOpacity);
  var _worklet_6619558881998_init_data = {
    code: "function indexTsx1(){const{bottomTabActualHeight,bottomTabsPosition,opacity}=this.__closure;return{position:\"absolute\",right:8,bottom:bottomTabActualHeight.value+bottomTabsPosition.value,opacity:bottomTabActualHeight.value>0?opacity.value:0,pointerEvents:opacity.value===1?\"auto\":\"none\"};}"
  };
  var ScrollBuddy = exports.ScrollBuddy = (0, _react.memo)(function (props) {
    var scrollBuddyImage = props.scrollBuddyImage,
      opacity = props.opacity,
      onPress = props.onPress;
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabsPosition = _useContext.bottomTabsPosition,
      bottomTabActualHeight = _useContext.bottomTabActualHeight;
    var lottieRef = (0, _react.useRef)(null);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if (_reactNative2.Platform.OS === 'ios') {
        var _lottieRef$current;
        (_lottieRef$current = lottieRef.current) == null || _lottieRef$current.play();
      }
    }, [lottieRef]));
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        return {
          position: "absolute",
          right: 8,
          bottom: bottomTabActualHeight.value + bottomTabsPosition.value,
          opacity: bottomTabActualHeight.value > 0 ? opacity.value : 0,
          pointerEvents: opacity.value === 1 ? "auto" : "none"
        };
      };
      indexTsx1.__closure = {
        bottomTabActualHeight: bottomTabActualHeight,
        bottomTabsPosition: bottomTabsPosition,
        opacity: opacity
      };
      indexTsx1.__workletHash = 6619558881998;
      indexTsx1.__initData = _worklet_6619558881998_init_data;
      return indexTsx1;
    }(), [bottomTabActualHeight, bottomTabsPosition, opacity]);
    var Icon = (0, _react.useMemo)(function () {
      if (!scrollBuddyImage) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.scrollBuddyImage
        });
      }
      if (scrollBuddyImage.endsWith(".json") || scrollBuddyImage.endsWith(".lottie")) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.scrollBuddyImage,
          children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
            style: styles.lottieStyle,
            ref: lottieRef,
            source: {
              uri: (0, _screenHelper.getUriImage)(encodeURI(scrollBuddyImage))
            },
            autoPlay: true,
            loop: true
          })
        });
      } else {
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: styles.scrollBuddyImage,
          source: {
            uri: (0, _screenHelper.getUriImage)(scrollBuddyImage)
          }
        });
      }
    }, [scrollBuddyImage]);
    return (0, _jsxRuntime.jsx)(AnimatedTouchableOpacity, {
      style: animatedStyle,
      onPress: onPress,
      children: Icon
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    scrollBuddyImage: {
      height: 100,
      width: 100
    },
    lottieStyle: {
      height: '100%',
      width: '100%'
    }
  });
