  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _flightInformationDisclaimer = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var FlightInformationDisclaimer = function FlightInformationDisclaimer(props) {
    var _dataCommonAEM$data;
    var marginTop = props.marginTop,
      marginLeft = props.marginLeft,
      marginRight = props.marginRight,
      marginBottom = props.marginBottom,
      customStyle = props.customStyle;
    var wrapperStyle = customStyle ? customStyle : Object.assign({}, marginTop !== undefined && {
      marginTop: marginTop
    }, marginLeft !== undefined && {
      marginLeft: marginLeft
    }, marginRight !== undefined && {
      marginRight: marginRight
    }, marginBottom !== undefined && {
      marginBottom: marginBottom
    });
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var inf21 = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.informatives) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "INF21";
    });
    if (!(inf21 != null && inf21.informativeText)) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_flightInformationDisclaimer.styles.wrapper, wrapperStyle],
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _flightInformationDisclaimer.styles.disclaimerText,
          children: inf21 == null ? undefined : inf21.informativeText
        })
      })
    });
  };
  var _default = exports.default = FlightInformationDisclaimer;
