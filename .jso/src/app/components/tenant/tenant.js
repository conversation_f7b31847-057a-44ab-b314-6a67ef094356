  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Tenant = Tenant;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _tenant = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var gradientColors = ["rgba(44, 37, 54, 0)", "rgba(44, 37, 54, 0.0125)", "rgba(71, 71, 71, 0.55)"];
  var otherGradientProps = Object.assign({}, {
    useAngle: true,
    angle: 188
  });
  var COMPONENT_NAME = "Tenant";
  var imageStyle = {
    borderRadius: 16
  };
  var imageStuff = {
    width: 140,
    height: 170,
    borderRadius: 16
  };
  var logoView = {
    width: 50,
    height: 50,
    borderRadius: 28
  };
  var titleLocationContainer = {
    position: "absolute",
    paddingLeft: 12,
    paddingRight: 12,
    bottom: 12
  };
  var logoContainer = {
    paddingTop: 12,
    paddingLeft: 12
  };
  var locationViewStyle = {
    paddingTop: 4
  };
  var cardStyle = Object.assign({}, imageStuff, imageStyle, {
    backgroundColor: _color.color.palette.lightGrey
  });
  var locationTextStyle = {
    color: _color.color.palette.whiteGrey
  };
  var skeletonLayout = [{
    width: 50,
    height: 50,
    borderRadius: 28,
    marginLeft: 12,
    marginTop: 12
  }, {
    width: 109,
    height: 15,
    borderRadius: 4,
    marginLeft: 12,
    marginTop: 59
  }, {
    width: 53,
    height: 15,
    borderRadius: 4,
    marginLeft: 12,
    marginTop: 7
  }];
  var gradientStyle = Object.assign({}, cardStyle, {
    backgroundColor: "transparent",
    position: "absolute",
    top: 0,
    bottom: 0,
    left: 0,
    right: 0
  });
  var whiteGreyLoadingColors = [_color.color.palette.whiteGrey, _color.color.background, _color.color.palette.whiteGrey];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: whiteGreyLoadingColors,
        shimmerStyle: skeletonLayout[0]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: whiteGreyLoadingColors,
        shimmerStyle: skeletonLayout[1]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: whiteGreyLoadingColors,
        shimmerStyle: skeletonLayout[2]
      })]
    });
  };
  var contentView = function contentView(props) {
    var onPressed = props.onPressed,
      imageUrl = props.imageUrl,
      logoUrl = props.logoUrl,
      tenantName = props.tenantName,
      location = props.location;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      accessible: false,
      children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
        imageStyle: imageStuff,
        source: {
          uri: imageUrl
        },
        children: (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, Object.assign({
          colors: gradientColors
        }, otherGradientProps, {
          style: gradientStyle,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: logoContainer,
            children: (0, _jsxRuntime.jsx)(_baseImage.default, {
              style: logoView,
              source: {
                uri: logoUrl
              },
              testID: `${COMPONENT_NAME}__Image`,
              accessibilityLabel: logoUrl
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: titleLocationContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 2,
              preset: "bodyTextBold",
              textBreakStrategy: "highQuality",
              testID: `${COMPONENT_NAME}__Title`,
              accessibilityLabel: tenantName,
              children: tenantName
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: locationViewStyle,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: locationTextStyle,
                numberOfLines: 1,
                preset: "caption2Regular",
                testID: `${COMPONENT_NAME}__Location`,
                accessibilityLabel: location,
                children: location
              })
            })]
          })]
        }))
      })
    });
  };
  /**
   * Describe your component here
   */
  function Tenant(props) {
    var isLoading = props.type === _tenant.TenantType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: cardStyle,
      children: isLoading ? loadingView() : contentView(props)
    });
  }
