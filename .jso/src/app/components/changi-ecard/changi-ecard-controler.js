  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _ChangiEcardController;
  var ChangiEcardController = exports.default = /*#__PURE__*/(0, _createClass2.default)(function ChangiEcardController() {
    (0, _classCallCheck2.default)(this, ChangiEcardController);
  });
  _ChangiEcardController = ChangiEcardController;
  ChangiEcardController.setAppRef = function (ref) {
    _ChangiEcardController.appRef = ref;
  };
  ChangiEcardController.setModalRef = function (ref) {
    _ChangiEcardController.modalRef = ref;
  };
  ChangiEcardController.showModal = function (nav) {
    var _ChangiEcardControlle;
    (_ChangiEcardControlle = _ChangiEcardController.modalRef) == null || (_ChangiEcardControlle = _ChangiEcardControlle.current) == null || _ChangiEcardControlle.show(nav);
  };
  ChangiEcardController.hideModal = function () {
    var _ChangiEcardControlle2;
    (_ChangiEcardControlle2 = _ChangiEcardController.modalRef) == null || (_ChangiEcardControlle2 = _ChangiEcardControlle2.current) == null || _ChangiEcardControlle2.hide();
  };
