  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _changiRewardsRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _changiEcardModal = _$$_REQUIRE(_dependencyMap[10]);
  var _clipboard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _constants = _$$_REQUIRE(_dependencyMap[16]);
  var _getConfigurationPermission = _$$_REQUIRE(_dependencyMap[17]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[19]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[20]);
  var _reactNativeDeviceBrightness = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _utils = _$$_REQUIRE(_dependencyMap[22]);
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[24]);
  var _text = _$$_REQUIRE(_dependencyMap[25]);
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[26]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[27]);
  var _crForEconomyType = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _crForMonarchType = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _qualtrics = _$$_REQUIRE(_dependencyMap[30]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[31]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[32]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[33]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[34]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[35]);
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[36]));
  var _changipay = _$$_REQUIRE(_dependencyMap[37]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[38]));
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[39]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[40]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var presetCard = {
    Member: {
      linearGradientColor: ["#46268C", "#6950A1", "#B28DC1"],
      cornerColor: "#6D4FA5",
      dotConnerColor: "#563694",
      dotColor: ["#6D4FA5", "#AF8AC0"],
      boxShadow: "rgba(50, 25, 107, 0.4)",
      isStaff: false
    },
    Gold: {
      linearGradientColor: ["#916D28", "#947C4E", "#B29E78"],
      cornerColor: "#9C7C3D",
      dotConnerColor: "#936E27",
      dotColor: ["#9C7C3D", "#B29E78"],
      boxShadow: "rgba(121, 93, 38, 0.4)",
      isStaff: false
    },
    Platinum: {
      linearGradientColor: ["#29343C", "#2B4E63", "#456374"],
      cornerColor: "#57656D",
      dotConnerColor: "#29343C",
      dotColor: ["#57656D", "#55636B"],
      boxShadow: "rgba(42, 61, 73, 0.4)",
      isStaff: false
    },
    StaffMember: {
      linearGradientColor: ["#46268C", "#6950A1", "#B28DC1"],
      cornerColor: "#6D4FA5",
      dotConnerColor: "#563694",
      dotColor: ["#6D4FA5", "#AF8AC0"],
      boxShadow: "rgba(50, 25, 107, 0.4)",
      isStaff: true
    },
    StaffGold: {
      linearGradientColor: ["#916D28", "#947C4E", "#B29E78"],
      cornerColor: "#9C7C3D",
      dotConnerColor: "#936E27",
      dotColor: ["#9C7C3D", "#B29E78"],
      boxShadow: "rgba(121, 93, 38, 0.4)",
      isStaff: true
    },
    StaffPlatinum: {
      linearGradientColor: ["#29343C", "#2B4E63", "#456374"],
      cornerColor: "#57656D",
      dotConnerColor: "#29343C",
      dotColor: ["#57656D", "#55636B"],
      boxShadow: "rgba(42, 61, 73, 0.4)",
      isStaff: true
    },
    Monarch: {
      linearGradientColor: ["#29343C", "#2B4E63", "#456374"],
      cornerColor: "#57656D",
      dotConnerColor: "#29343C",
      dotColor: ["#57656D", "#55636B"],
      boxShadow: "rgba(42, 61, 73, 0.4)",
      isStaff: false
    },
    StaffMonarch: {
      linearGradientColor: ["#000000", "#000000"],
      cornerColor: "#57656D",
      dotConnerColor: "#29343C",
      dotColor: ["#57656D", "#55636B"],
      boxShadow: "rgba(42, 61, 73, 0.4)",
      isStaff: true
    }
  };
  var MAX_BRIGHTNESS = 1;
  var _worklet_12197378533179_init_data = {
    code: "function changiEcardModalTsx1(){const{animatedTop}=this.__closure;return{position:\"absolute\",top:animatedTop.value};}"
  };
  var _worklet_652093747900_init_data = {
    code: "function changiEcardModalTsx2(isFinished){const{runOnJS,checkChangiPay}=this.__closure;if(isFinished){runOnJS(checkChangiPay)();}}"
  };
  var ChangiEcardModal = function ChangiEcardModal() {
    var _useSelector;
    var modalRef = (0, _react.useRef)(null);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      newNavigation = _useState2[0],
      setNewNavigation = _useState2[1];
    var rewardDetails = (_useSelector = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData)) == null ? undefined : _useSelector.reward;
    var toastRef = (0, _react.useRef)(null);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var aemGroupTwoLoading = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.aemGroupTwoLoading);
    var horizontalContentCardDetailPayload = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.horizontalContentCardDetailPayload);
    var horizontalContentCardDetailMonarchPayload = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.horizontalContentCardDetailMonarchPayload);
    var contentBannerCard = {
      data: {
        list: horizontalContentCardDetailPayload
      }
    };
    var contentMonarchTierBannerCard = {
      data: {
        list: horizontalContentCardDetailMonarchPayload
      }
    };
    var isLoadingReward = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsFetching);
    var rewardsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsError);
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      currentTier = _useRewardTier.currentTier;
    var selectedPresetTier = presetCard[currentTier] || presetCard.Member;
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoInternet = _useState6[0],
      setIsNoInternet = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isOpenChangiPay = _useState8[0],
      setIsOpenChangPay = _useState8[1];
    var brightnessCurrentLevel = (0, _react.useRef)(0);
    var isMonarchTier = currentTier === _changiRewardsMemberCard.Tier.Monarch || currentTier === _changiRewardsMemberCard.Tier.StaffMonarch;
    var _useGetConfigurationP = (0, _getConfigurationPermission.useGetConfigurationPermissionHelper)(),
      loadingGetConfig = _useGetConfigurationP.loadingGetConfig,
      getConfigApp = _useGetConfigurationP.getConfigApp,
      notifyDisableChangiPay = _useGetConfigurationP.notifyDisableChangiPay,
      notifyDisableChangiRewards = _useGetConfigurationP.notifyDisableChangiRewards;
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      modalVisible = _useState0[0],
      setModalVisible = _useState0[1];
    var isModalOpenRef = (0, _react.useRef)(false);
    var appState = (0, _react.useRef)(_reactNative.AppState.currentState);
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      conditionTimeRef = _useContext.conditionTimeRef,
      idleTimeRef = _useContext.idleTimeRef,
      isPageLoadingRef = _useContext.isPageLoadingRef;
    var _useCPay = (0, _changipay.useCPay)(),
      openChangiPay = _useCPay.openChangiPay;
    (0, _react.useEffect)(function () {
      _changiEcardControler.default.setModalRef(modalRef);
    }, []);
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var modalStyle = {
      marginHorizontal: 0,
      marginBottom: 0,
      marginTop: _reactNative.Platform.select({
        ios: 60,
        android: (0, _utils.handleCondition)(inset == null ? undefined : inset.top, (inset == null ? undefined : inset.top) + 10, 25)
      }),
      overflow: "hidden",
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10
    };
    (0, _react.useImperativeHandle)(modalRef, function () {
      return {
        show: function show(nav) {
          setNewNavigation(nav);
          setModalVisible(true);
          isModalOpenRef.current = true;
          dispatch(_changiRewardsRedux.default.setChangiECardModalOpenning(true));
        },
        hide: function hide() {
          setModalVisible(false);
          isModalOpenRef.current = false;
          dispatch(_changiRewardsRedux.default.setChangiECardModalOpenning(false));
          (0, _screenHelper.resetInactivityTimeout)({
            conditionTimeRef: conditionTimeRef,
            idleTimeRef: idleTimeRef,
            callback: function callback() {
              return (0, _screenHook.getCurrentScreenActive)() === _constants.TrackingScreenName.Explore ? (0, _screenHelper.trackingShowRatingPopupExploreScreen)({
                isPageLoadingRef: isPageLoadingRef
              }) : (0, _screenHelper.trackingShowRatingPopup)({
                isPageLoadingRef: isPageLoadingRef
              });
            }
          });
        }
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (modalVisible) {
        getInitialConfig();
        openModal();
        (0, _qualtrics.markTimeForQualtrics)(_qualtrics.USER_ACTION_ENUM.E_CARD);
        (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
      } else {
        closeModal();
        (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      }
    }, [modalVisible]);
    (0, _react.useEffect)(function () {
      if (_reactNative.Platform.OS === "ios") {
        var subscription = /*#__PURE__*/function () {
          var _ref = (0, _asyncToGenerator2.default)(function* (nextAppState) {
            if ((0, _utils.ifAllTrue)([appState.current.match(/active/), nextAppState === "inactive", isModalOpenRef.current])) {
              _reactNativeDeviceBrightness.default.setBrightnessLevel(brightnessCurrentLevel.current);
            }
            if ((0, _utils.ifAllTrue)([appState.current.match(/inactive|background/), nextAppState === "active", isModalOpenRef.current])) {
              getBrightness();
            }
            appState.current = nextAppState;
          });
          return function subscription(_x) {
            return _ref.apply(this, arguments);
          };
        }();
        _reactNative.AppState.addEventListener("change", subscription);
        return function () {
          //AppState.removeEventListener("change", subscription)
          //@ts-ignore
          _reactNative.AppState && (_reactNative.AppState == null || _reactNative.AppState.remove == null ? undefined : _reactNative.AppState.remove());
        };
      }
    }, []);
    var getBrightness = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var tBrightness = yield _reactNativeDeviceBrightness.default.getBrightnessLevel();
          brightnessCurrentLevel.current = tBrightness;
          _reactNativeDeviceBrightness.default.setBrightnessLevel(MAX_BRIGHTNESS);
        } catch (err) {}
      });
      return function getBrightness() {
        return _ref2.apply(this, arguments);
      };
    }();
    var getConfigECard = function getConfigECard() {
      getConfigApp({
        configKey: _constants.AppConfigPermissionTypes.changiappEcardEnabled,
        callbackSuccess: function callbackSuccess() {
          return setLoading(false);
        },
        callbackFailure: function callbackFailure() {
          checkConnection().then(function (res) {
            if (res) {
              setLoading(false);
              closeScreen();
              notifyDisableChangiRewards();
            } else {
              setIsNoInternet(true);
            }
          });
        }
      });
    };
    var getInitialConfig = function getInitialConfig() {
      getBrightness();
      getConfigECard();
    };
    var checkConnection = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkConnection() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_Ecard");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = newNavigation == null ? undefined : newNavigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_Ecard", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, []);
    (0, _react.useEffect)(function () {
      handleFetchData();
    }, [isLoggedIn, profilePayload == null ? undefined : profilePayload.cardNo]);
    var handleFetchData = function handleFetchData() {
      checkConnection().then(function (res) {
        if (res) {
          if (isNoInternet) {
            setIsNoInternet(false);
          }
          if ((0, _utils.ifAllTrue)([isLoggedIn, profilePayload])) {
            dispatch(_forYouRedux.default.rewardsRequest((profilePayload == null ? undefined : profilePayload.cardNo) || ""));
          }
        } else {
          setIsNoInternet(true);
        }
      });
    };
    var onPressCopy = function onPressCopy() {
      _clipboard.default.setString(rewardDetails == null ? undefined : rewardDetails.cardNo);
      onCopyQrCode();
    };
    var onCopyQrCode = function onCopyQrCode() {
      toastRef.current.show();
    };
    var scanToPay = function scanToPay() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppRewardsCard, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppRewardsCard, (0, _i18n.translate)("changiRewardsEcard.scanToPay")));
      checkConnection().then(function (res) {
        if (res) {
          if (isNoInternet) {
            setIsNoInternet(false);
          }
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeWallet, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeWallet, "1"));
          getConfigApp({
            configKey: _constants.AppConfigPermissionTypes.changiappWalletEnabled,
            callbackSuccess: function callbackSuccess() {
              setIsOpenChangPay(true);
              (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
              _changiEcardControler.default.hideModal();
            },
            callbackFailure: function callbackFailure() {
              return notifyDisableChangiPay();
            }
          });
        } else {
          setIsNoInternet(true);
        }
      });
    };
    var closeScreen = function closeScreen() {
      dispatch(_changiRewardsRedux.default.setChangiECardModalOpenning(false));
      _reactNativeDeviceBrightness.default.setBrightnessLevel(brightnessCurrentLevel.current);
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      _changiEcardControler.default.hideModal();
    };
    var animatedTop = (0, _reactNativeReanimated.useSharedValue)(_reactNative.Dimensions.get("screen").height);
    var animatedModalStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var changiEcardModalTsx1 = function changiEcardModalTsx1() {
        return {
          position: "absolute",
          top: animatedTop.value
        };
      };
      changiEcardModalTsx1.__closure = {
        animatedTop: animatedTop
      };
      changiEcardModalTsx1.__workletHash = 12197378533179;
      changiEcardModalTsx1.__initData = _worklet_12197378533179_init_data;
      return changiEcardModalTsx1;
    }());
    var openModal = function openModal() {
      var position = _reactNative.Platform.select({
        ios: 60,
        android: (0, _utils.handleCondition)(inset == null ? undefined : inset.top, (inset == null ? undefined : inset.top) + 10, 25)
      });
      animatedTop.value = (0, _reactNativeReanimated.withTiming)(position, {
        duration: TRANSITION_TIMING / 2
      });
    };
    var closeModal = function closeModal() {
      animatedTop.value = (0, _reactNativeReanimated.withTiming)(_reactNative.Dimensions.get("screen").height, {
        duration: TRANSITION_TIMING / 2
      }, function () {
        var changiEcardModalTsx2 = function changiEcardModalTsx2(isFinished) {
          if (isFinished) {
            (0, _reactNativeReanimated.runOnJS)(checkChangiPay)();
          }
        };
        changiEcardModalTsx2.__closure = {
          runOnJS: _reactNativeReanimated.runOnJS,
          checkChangiPay: checkChangiPay
        };
        changiEcardModalTsx2.__workletHash = 652093747900;
        changiEcardModalTsx2.__initData = _worklet_652093747900_init_data;
        return changiEcardModalTsx2;
      }());
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      _changiEcardControler.default.hideModal();
    };
    var checkChangiPay = function checkChangiPay() {
      if (isOpenChangiPay) {
        _reactNativeDeviceBrightness.default.setBrightnessLevel(brightnessCurrentLevel.current);
        processChangiPay();
      }
      setIsOpenChangPay(false);
    };
    var onReloadPage = function onReloadPage() {
      getConfigECard();
      handleFetchData();
    };
    var TRANSITION_TIMING = 400;
    var processChangiPay = function processChangiPay() {
      if (_reactNative.Platform.OS === "ios") {
        setTimeout(function () {
          openChangiPay();
        }, 200);
      } else {
        openChangiPay();
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      style: animatedModalStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        barStyle: "light-content"
      }), currentTier && (0, _utils.handleCondition)(isMonarchTier, (0, _jsxRuntime.jsx)(_crForMonarchType.default, {
        selectedPresetTier: selectedPresetTier,
        closeScreen: closeScreen,
        profilePayload: profilePayload,
        rewardDetails: rewardDetails,
        onPressCopy: onPressCopy,
        scanToPay: scanToPay,
        newNavigation: newNavigation,
        contentBannerCard: contentMonarchTierBannerCard
      }), (0, _jsxRuntime.jsx)(_crForEconomyType.default, {
        selectedPresetTier: selectedPresetTier,
        closeScreen: closeScreen,
        profilePayload: profilePayload,
        rewardDetails: rewardDetails,
        onPressCopy: onPressCopy,
        scanToPay: scanToPay,
        newNavigation: newNavigation,
        contentBannerCard: contentBannerCard
      })), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loadingGetConfig && !isNoInternet
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: (aemGroupTwoLoading || isLoadingReward || loadingGetConfig || loading || !currentTier) && !isNoInternet,
        isTransparent: true
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        headerBackgroundColor: "transparent",
        visible: isNoInternet,
        testID: `ChangiECards__ErrorOverlayNoConnection`,
        onReload: onReloadPage,
        onBack: closeScreen,
        hideScreenHeader: false,
        noInternetOverlayStyle: _changiEcardModal.styles.overlayStyle,
        storyMode: true
      }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        storyMode: true,
        headerBackgroundColor: "transparent",
        header: true,
        hideScreenHeader: false,
        visible: rewardsError && !isNoInternet,
        onReload: onReloadPage,
        onBack: closeScreen,
        testID: `ChangiECards__ErrorOverlay`,
        accessibilityLabel: `ChangiECards__ErrorOverlay`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
        overlayStyle: _changiEcardModal.styles.overlayStyle
      }), (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastRef,
        style: feedBackToastStyle,
        position: "custom",
        textStyle: toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("changiRewardsEcard.cardNoCopied")
      })]
    });
  };
  var feedBackToastStyle = {
    bottom: 40
  };
  var toastTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.whiteGrey,
    width: "80%"
  });
  var _default = exports.default = (0, _react.forwardRef)(ChangiEcardModal);
