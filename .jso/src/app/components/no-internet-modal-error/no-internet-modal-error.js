  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _button = _$$_REQUIRE(_dependencyMap[10]);
  var _noInternetModalErrorStyles = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var NoInternetModalError = function NoInternetModalError(props) {
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "NoInternetModalError" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "NoInternetModalError" : _props$accessibilityL;
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: props.isShowModalError,
      onClosedSheet: props.onCloseModalError,
      stopDragCollapse: true,
      onBackPressHandle: props.onCloseModalError,
      animationInTiming: 200,
      animationOutTiming: 200,
      containerStyle: _noInternetModalErrorStyles.styles.bottomSheetStyle,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _noInternetModalErrorStyles.styles.errorContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _noInternetModalErrorStyles.styles.iconInfoError,
          children: (0, _jsxRuntime.jsx)(_icons.InfoRed, {})
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _noInternetModalErrorStyles.styles.dismissIconContainer,
          onPress: props.onCloseModalError,
          testID: `${testID}__TouchableCrossClose`,
          accessibilityLabel: `${accessibilityLabel}__TouchableCrossClose`,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {
            color: _theme.color.palette.darkGrey
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _noInternetModalErrorStyles.styles.textContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _noInternetModalErrorStyles.styles.titleStyle,
            text: (0, _i18n.translate)("errorOverlay.variant3.title")
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _noInternetModalErrorStyles.styles.messageStyle,
            text: (0, _i18n.translate)("errorOverlay.variant3.message")
          })]
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _noInternetModalErrorStyles.styles.buttonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "secondary",
            text: (0, _i18n.translate)("popupError.retry"),
            statePreset: "default",
            backgroundPreset: "light",
            onPress: props.onCloseModalError,
            testID: `${testID}__ButtonClose`,
            accessibilityLabel: `${accessibilityLabel}__ButtonClose`
          })
        })]
      })
    });
  };
  var _default = exports.default = NoInternetModalError;
