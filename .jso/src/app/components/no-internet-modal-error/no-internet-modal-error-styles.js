  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetStyle: {
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      height: "100%",
      overflow: "hidden",
      width: "100%"
    },
    buttonStyle: {
      borderRadius: 60,
      width: "100%"
    },
    dismissIconContainer: {
      alignSelf: "flex-end",
      marginTop: 15
    },
    errorContainer: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopEndRadius: 16,
      borderTopLeftRadius: 16,
      bottom: 0,
      height: 280,
      paddingHorizontal: 24,
      position: 'absolute',
      width: '100%'
    },
    iconInfoError: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 50,
      height: 70,
      justifyContent: "center",
      position: "absolute",
      top: -35,
      width: 70
    },
    messageStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    textContainerStyle: {
      marginVertical: 30,
      paddingHorizontal: 10
    },
    titleStyle: Object.assign({}, _text.presets.h2, {
      lineHeight: 28,
      marginBottom: 16,
      textAlign: "center"
    })
  });
