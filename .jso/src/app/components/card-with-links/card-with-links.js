  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.CardWithLinks = CardWithLinks;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[7]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _cardWithLinks = _$$_REQUIRE(_dependencyMap[9]);
  var _location_filled = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _screens = _$$_REQUIRE(_dependencyMap[12]);
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _text2 = _$$_REQUIRE(_dependencyMap[14]);
  var _gttdUtils = _$$_REQUIRE(_dependencyMap[15]);
  var _native = _$$_REQUIRE(_dependencyMap[16]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var container = Object.assign({
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16,
    elevation: 5,
    width: width - (0, _reactNativeSizeMatters.scale)(45)
  }, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 16,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      },
      backgroundColor: _theme.color.palette.whiteGrey
    },
    android: {
      elevation: 3,
      backgroundColor: _theme.color.palette.whiteGrey
    }
  }));
  var titleContainer = {
    flex: 1,
    marginLeft: 16,
    marginTop: 16,
    marginRight: 16
  };
  var bottomContainer = {
    marginLeft: 14,
    marginBottom: 16,
    marginTop: 12,
    marginRight: 12
  };
  var firstRowStyle = {
    flexDirection: "row"
  };
  var columnStyle = {
    flexDirection: "column",
    flex: 1.6,
    paddingEnd: 8,
    marginStart: 2
  };
  var leftColumnStyle = {
    flexDirection: "column",
    flex: 1.5,
    alignSelf: "flex-start"
  };
  var textContainerStyleColumn = {
    marginTop: 2,
    alignSelf: "flex-start",
    flexDirection: "row"
  };
  var textContainerStyle = Object.assign({}, textContainerStyleColumn, {
    flex: 1
  });
  var labelTextStyle = {
    color: _theme.color.palette.darkestGrey,
    width: "90%",
    height: 14
  };
  var textStyle = {
    color: _theme.color.palette.darkestGrey
  };
  var textLinkStyle = {
    height: 22
  };
  var textLinkStyleDisabled = {
    height: 22,
    color: _theme.color.palette.almostBlackGrey
  };
  var textIconContainer = {
    width: "90%",
    flexDirection: "row"
  };
  var locationIcon = {
    alignSelf: "center",
    marginLeft: 4,
    color: _theme.color.palette.lightPurple
  };
  var locationIconInActive = {
    alignSelf: "center",
    marginLeft: 4,
    color: _theme.color.palette.midGrey
  };
  var accessibilityIcon = {
    justifyContent: "flex-start",
    alignSelf: "center",
    marginRight: 4
  };
  var marginContainer = {
    marginTop: 12
  };
  var textCardMiddleMargin = {
    paddingRight: 8
  };
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var loadingStyle = [{
    width: 232,
    borderRadius: 4,
    height: 18
  }, {
    width: 80,
    height: 7,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginBottom: 12
  }, {
    width: 60,
    height: 10,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lightGrey
  }, {
    width: 80,
    height: 7,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginBottom: 12
  }, {
    width: 60,
    height: 10,
    borderRadius: 2,
    backgroundColor: _theme.color.palette.lighterGrey
  }];
  var wrapGTTDCard = {
    borderColor: _theme.color.transparent,
    borderTopWidth: 1,
    borderTopColor: _theme.color.palette.lighterGrey,
    marginTop: 24,
    paddingTop: 24,
    alignItems: "flex-start"
  };
  var textTitleGTTDStyle = Object.assign({}, _text2.presets.caption1Bold, {
    color: "rgba(69, 69, 69, 1)"
  });
  var wrapContentGTTDCardStyle = {
    flexDirection: "row",
    width: "100%",
    alignItems: "center",
    marginTop: 16,
    justifyContent: "space-between"
  };
  var nameAndSubTextSectionStyle = {
    alignItems: "flex-start",
    flexDirection: "row"
  };
  var priceAndTimeSectionStyle = {
    alignItems: "flex-start",
    marginLeft: 8
  };
  var textNameGTTDStyle = Object.assign({
    marginLeft: 4
  }, _text2.presets.bodyTextBold, {
    color: "rgba(18, 18, 18, 1)"
  });
  var textSubText2GTTDStyle = Object.assign({}, _text2.presets.XSmallBold, {
    marginLeft: 4,
    color: "rgba(18, 18, 18, 1)",
    textTransform: "none"
  });
  var textPriceGTTDStyle = Object.assign({}, _text2.presets.caption1Regular, {
    color: "rgba(18, 18, 18, 1)"
  });
  var textTimeGTTDStyle = Object.assign({}, _text2.presets.caption1Regular, {
    color: "rgba(18, 18, 18, 1)"
  });
  var touchableGTTDStyle = {
    alignSelf: "flex-end"
  };
  var textTouchableGTTDStyle = Object.assign({}, _text2.presets.bodyTextBold, {
    marginTop: 16,
    color: "rgba(103, 26, 157, 1)"
  });
  var DataStatus = /*#__PURE__*/function (DataStatus) {
    DataStatus["NA"] = "N/A";
    return DataStatus;
  }(DataStatus || {});
  var COMPONENT_NAME = "CardWithLink__";
  function CardWithIcons(props) {
    var label1 = props.label1,
      label2 = props.label2,
      label3 = props.label3,
      text2 = props.text2,
      text3 = props.text3,
      link1 = props.link1,
      link2 = props.link2,
      link3 = props.link3,
      onPressed = props.onPressed,
      disabled = props.disabled;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: firstRowStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: leftColumnStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallRegular",
            text: label1,
            style: labelTextStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              return onPressed(_screens.TypeGetIntoAirport.LINK1, link1);
            },
            style: textContainerStyle,
            testID: `${COMPONENT_NAME}IconLink1`,
            accessibilityLabel: `${COMPONENT_NAME}IconLink1`,
            disabled: link1 === DataStatus.NA || (disabled == null ? undefined : disabled.disabled1),
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: textIconContainer,
              children: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "textLink",
                  text: link1,
                  style: link1 === DataStatus.NA || disabled != null && disabled.disabled1 ? textLinkStyleDisabled : textLinkStyle
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: (0, _utils.handleCondition)(link1 !== DataStatus.NA && !(disabled != null && disabled.disabled1), locationIcon, locationIconInActive)
                })]
              })
            })
          }), link3 || label3 || text3 ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: marginContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "XSmallRegular",
              text: label3,
              style: labelTextStyle
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: textContainerStyle,
              onPress: function onPress() {
                return onPressed(_screens.TypeGetIntoAirport.LINK3, link3);
              },
              testID: `${COMPONENT_NAME}IconLink3`,
              accessibilityLabel: `${COMPONENT_NAME}IconLink3`,
              disabled: (0, _utils.handleCondition)(link3 === DataStatus.NA || text3 === DataStatus.NA || (disabled == null ? undefined : disabled.disabled3), true, false),
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: textIconContainer,
                children: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    style: (0, _utils.handleCondition)(link3 === DataStatus.NA || text3 === DataStatus.NA || (disabled == null ? undefined : disabled.disabled3), textLinkStyleDisabled, textLinkStyle),
                    preset: "textLink",
                    text: link3 || text3
                  }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                    width: 15,
                    height: 15,
                    style: (0, _utils.handleCondition)(link3 !== DataStatus.NA && text3 !== DataStatus.NA && !(disabled != null && disabled.disabled3), locationIcon, locationIconInActive)
                  })]
                })
              })
            })]
          }) : null]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: columnStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallRegular",
            text: label2,
            style: labelTextStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: textContainerStyleColumn,
            onPress: function onPress() {
              return onPressed(_screens.TypeGetIntoAirport.LINK2, link2);
            },
            testID: `${COMPONENT_NAME}IconLink2`,
            accessibilityLabel: `${COMPONENT_NAME}IconLink2`,
            disabled: link2 === DataStatus.NA || (disabled == null ? undefined : disabled.disabled2),
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: textIconContainer,
              children: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: link2 === DataStatus.NA || disabled != null && disabled.disabled2 ? textLinkStyleDisabled : textLinkStyle,
                  preset: "textLink",
                  text: text2
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: (0, _utils.handleCondition)(link2 !== DataStatus.NA && !(disabled != null && disabled.disabled2), locationIcon, locationIconInActive)
                })]
              })
            })
          })]
        })]
      })
    });
  }
  var isDisabled = function isDisabled(_link) {
    return true;
  };
  function CardWithLinkTexts(props) {
    var link1 = props.link1,
      link2 = props.link2,
      text1 = props.text1,
      text2 = props.text2,
      label1 = props.label1,
      label2 = props.label2,
      onPressed = props.onPressed;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: firstRowStyle,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: leftColumnStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 2,
          preset: "XSmallRegular",
          text: label1,
          style: labelTextStyle
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return onPressed(_screens.TypeGetIntoAirport.LINK1, link1);
          },
          style: textContainerStyle,
          disabled: isDisabled(link1),
          testID: `${COMPONENT_NAME}TextLink1`,
          accessibilityLabel: `${COMPONENT_NAME}TextLink1`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "textLink",
            numberOfLines: 2,
            text: link1 || text1,
            style: !isDisabled(link1) ? textStyle : null
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: textCardMiddleMargin
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: columnStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 2,
          preset: "XSmallRegular",
          text: label2,
          style: labelTextStyle
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return onPressed(_screens.TypeGetIntoAirport.LINK2, link2);
          },
          style: textContainerStyle,
          disabled: isDisabled(link2),
          testID: `${COMPONENT_NAME}TextLink2`,
          accessibilityLabel: `${COMPONENT_NAME}TextLink2`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "textLink",
            numberOfLines: 2,
            text: link2 || text2,
            style: !isDisabled(link2) ? textStyle : null
          })
        })]
      })]
    });
  }
  function GTTDCard(props) {
    var navigation = (0, _native.useNavigation)();
    var _ref = (props == null ? undefined : props.data) || "",
      name = _ref.name,
      price = _ref.price,
      subText2 = _ref.subText2,
      time = _ref.time,
      url = _ref.url,
      transportType = _ref.transportType;
    var flightNumber = props.flightNumber,
      scheduledDate = props.scheduledDate;
    var onPressGTTDOptions = function onPressGTTDOptions() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, `${name}|${flightNumber}|${scheduledDate}`));
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: url
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: wrapGTTDCard,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: "flightDetails.gttdCardTitle",
        style: textTitleGTTDStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: wrapContentGTTDCardStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: nameAndSubTextSectionStyle,
          children: [(0, _gttdUtils.handleGTTDIcon)(transportType), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: name,
              style: textNameGTTDStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: subText2,
              style: textSubText2GTTDStyle
            })]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: priceAndTimeSectionStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: price,
            style: textPriceGTTDStyle
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: time,
            style: textTimeGTTDStyle
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: touchableGTTDStyle,
        onPress: onPressGTTDOptions,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: "More options",
          style: textTouchableGTTDStyle
        })
      })]
    });
  }
  var ComponentVariations = {
    CardWithIcons: CardWithIcons,
    CardWithLinkTexts: CardWithLinkTexts
  };
  var LoadingView = function LoadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: container,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: titleContainer,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingStyle[0]
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: bottomContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: firstRowStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: leftColumnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[1]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[2]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: columnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[3]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[4]
            })]
          })]
        })
      })]
    });
  };
  var DefaultView = function DefaultView(props) {
    var state = props.state,
      title = props.title,
      flightNumber = props.flightNumber,
      scheduledDate = props.scheduledDate;
    var CardWithLinksVariation = ComponentVariations[state];
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: container,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          preset: "subTitleBold",
          numberOfLines: 1
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: bottomContainer,
        children: [(0, _jsxRuntime.jsx)(CardWithLinksVariation, Object.assign({}, props)), !(0, _isEmpty.default)(props == null ? undefined : props.groundTransport) && (0, _jsxRuntime.jsx)(GTTDCard, {
          data: props == null ? undefined : props.groundTransport,
          flightNumber: flightNumber,
          scheduledDate: scheduledDate
        })]
      })]
    });
  };
  function CardWithLinks(props) {
    var isLoading = props.type === _cardWithLinks.CardWithLinksType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      children: isLoading ? (0, _jsxRuntime.jsx)(LoadingView, {}) : (0, _jsxRuntime.jsx)(DefaultView, Object.assign({}, props))
    });
  }
