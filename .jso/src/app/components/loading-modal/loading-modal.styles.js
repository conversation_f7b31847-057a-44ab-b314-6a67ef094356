  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.lottieStyle = exports.containerStyleNoTransparent = exports.containerStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var containerStyle = exports.containerStyle = Object.assign({
    flex: 1,
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    backgroundColor: "rgba(252, 252, 252, 0.8)",
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10
  }, _reactNative.Platform.select({
    android: {
      elevation: 5
    }
  }));
  var containerStyleNoTransparent = exports.containerStyleNoTransparent = Object.assign({
    flex: 1,
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    backgroundColor: "rgba(252, 252, 252, 1)",
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10
  }, _reactNative.Platform.select({
    android: {
      elevation: 5
    }
  }));
  var lottieStyle = exports.lottieStyle = {
    width: "70%",
    height: '100%',
    marginLeft: 5
  };
