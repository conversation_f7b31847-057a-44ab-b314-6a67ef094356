  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LoadingOverlay = exports.LoadingModal = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _loadingAnimation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[8]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LoadingOverlay = exports.LoadingOverlay = function LoadingOverlay(props) {
    var visible = props.visible,
      isPopUp = props.isPopUp,
      onPressed = props.onPressed,
      isTransparent = props.isTransparent,
      customStyle = props.customStyle;
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      conditionTimeRef = _useContext.conditionTimeRef,
      idleTimeRef = _useContext.idleTimeRef,
      isPageLoadingRef = _useContext.isPageLoadingRef;
    var styleVisible = Object.assign({}, styles.containerStyle, {
      borderTopLeftRadius: 16,
      borderTopEndRadius: 16
    });

    // Handle preventing app rating when loading is visible
    (0, _react.useEffect)(function () {
      isPageLoadingRef.current = !!visible;
      if (visible) {
        (0, _screenHelper.clearAppRatingTimers)({
          conditionTimeRef: conditionTimeRef,
          idleTimeRef: idleTimeRef
        });
      } else {
        (0, _screenHelper.resetInactivityTimeout)({
          conditionTimeRef: conditionTimeRef,
          idleTimeRef: idleTimeRef,
          callback: function callback() {
            return (0, _screenHook.getCurrentScreenActive)() === _constants.TrackingScreenName.Explore ? (0, _screenHelper.trackingShowRatingPopupExploreScreen)({
              isPageLoadingRef: isPageLoadingRef
            }) : (0, _screenHelper.trackingShowRatingPopup)({
              isPageLoadingRef: isPageLoadingRef
            });
          }
        });
      }
    }, [visible]);
    (0, _react.useEffect)(function () {
      return function () {
        isPageLoadingRef.current = false;
      };
    }, []);
    if (visible) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return onPressed == null ? undefined : onPressed();
        },
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: Object.assign({}, (0, _utils.handleCondition)(isPopUp, styleVisible, (0, _utils.handleCondition)(isTransparent, styles.containerStyleNoTransparent, styles.containerStyle)), customStyle),
          children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
            style: styles.lottieStyle,
            source: _loadingAnimation.default,
            autoPlay: true,
            loop: true
          })
        })
      });
    }
    return null;
  };
  var LoadingModal = exports.LoadingModal = function LoadingModal(props) {
    var visible = props.visible;
    return visible && (0, _jsxRuntime.jsx)(_reactNative2.Modal, Object.assign({
      transparent: true,
      animationType: "fade"
    }, props, {
      children: (0, _jsxRuntime.jsx)(LoadingOverlay, {
        visible: true
      })
    }));
  };
