  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TypeSavedFlightCardProp = exports.SavedFlightErrorCodes = exports.SavedFlightCardState = undefined;
  var SavedFlightCardState = exports.SavedFlightCardState = /*#__PURE__*/function (SavedFlightCardState) {
    SavedFlightCardState["default"] = "default";
    SavedFlightCardState["loading"] = "loading";
    return SavedFlightCardState;
  }({});
  var TypeSavedFlightCardProp = exports.TypeSavedFlightCardProp = /*#__PURE__*/function (TypeSavedFlightCardProp) {
    TypeSavedFlightCardProp["DEFAULT"] = "default";
    TypeSavedFlightCardProp["NEW_CARD"] = "newCard";
    return TypeSavedFlightCardProp;
  }({});
  var SavedFlightErrorCodes = exports.SavedFlightErrorCodes = /*#__PURE__*/function (SavedFlightErrorCodes) {
    SavedFlightErrorCodes["RecordNotFound"] = "MT-GSF-030";
    SavedFlightErrorCodes["RecordAlreadyExist"] = "Record Already Exist";
    SavedFlightErrorCodes["RecordAlreadyRemoved"] = "Record Already Removed";
    return SavedFlightErrorCodes;
  }({});
