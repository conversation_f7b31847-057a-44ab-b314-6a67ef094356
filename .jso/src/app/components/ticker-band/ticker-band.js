  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _button = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _slideableView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _tickerBand = _$$_REQUIRE(_dependencyMap[13]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[14]));
  var _htmlRichtext = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TickerBandLoadingView = function TickerBandLoadingView() {
    return (0, _jsxRuntime.jsx)(_slideableView.default, {
      toggle: true,
      animationFromStart: true,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.skeletonStyles[0],
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColorLightest,
          shimmerStyle: styles.skeletonStyles[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColorLightest,
          shimmerStyle: styles.skeletonStyles[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColorLightest,
          shimmerStyle: styles.skeletonStyles[3]
        })]
      })
    });
  };
  var TickerBandDefaultView = function TickerBandDefaultView(_ref) {
    var timestamp = _ref.timestamp,
      title = _ref.title,
      description = _ref.description,
      buttonText = _ref.buttonText,
      urgent = _ref.urgent,
      isLanding = _ref.isLanding,
      onCTAPress = _ref.onCTAPress,
      onClose = _ref.onClose,
      onLayout = _ref.onLayout,
      tickerStyle = _ref.tickerStyle;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isShowing = _useState2[0],
      setIsShowing = _useState2[1];
    var onCloseTicker = (0, _react.useCallback)(function () {
      setIsShowing(!isShowing);
      onClose == null || onClose();
    }, [isShowing, onClose]);
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.containerTickerBand,
      onLayout: onLayout,
      children: isShowing && (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
        style: [isLanding ? styles.containerDefaultStyle[0] : styles.containerDefaultStyle[3], tickerStyle],
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        colors: _theme.color.purpleGradientColor,
        children: [urgent && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.urgentIconContainerStyle,
          children: (0, _jsxRuntime.jsx)(_icons.Urgent, {
            style: styles.urgentIconStyle
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.containerDefaultStyle[1],
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.containerDefaultStyle[2],
            children: [!!timestamp && (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.containerDefaultStyle[1],
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.textDefaultStyle[0],
                text: timestamp
              })
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onCloseTicker,
              children: (0, _jsxRuntime.jsx)(_icons.CrossWhite, {
                height: "13.33",
                width: "13.33"
              })
            })]
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 2,
            style: styles.textDefaultStyle[1],
            text: title
          }), (0, _jsxRuntime.jsx)(_htmlRichtext.HtmlRichtext, {
            style: styles.textDefaultStyle[2],
            value: `<p>${description}</p>`
          }), !!buttonText && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.buttonStyle[0],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              onPress: onCTAPress,
              style: styles.buttonStyle[1],
              sizePreset: "small",
              textPreset: "buttonSmall",
              typePreset: "secondary",
              statePreset: "default",
              backgroundPreset: "light",
              text: buttonText
            })
          })]
        })]
      })
    });
  };
  var TickerBand = function TickerBand(props) {
    return props.type === _tickerBand.TickerBandType.loading ? (0, _jsxRuntime.jsx)(TickerBandLoadingView, {}) : (0, _jsxRuntime.jsx)(TickerBandDefaultView, Object.assign({}, props));
  };
  var _default = exports.default = TickerBand;
