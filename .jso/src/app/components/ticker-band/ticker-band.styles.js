  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.urgentIconStyle = exports.urgentIconContainerStyle = exports.textDefaultStyle = exports.skeletonStyles = exports.containerTickerBand = exports.containerDefaultStyle = exports.buttonStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var skeletonStyles = exports.skeletonStyles = [{
    backgroundColor: _theme.color.palette.lightGrey,
    paddingTop: 25,
    paddingBottom: 34,
    paddingLeft: 24,
    paddingRight: 38
  }, {
    width: 41,
    height: 15,
    borderRadius: 4
  }, {
    height: 15,
    width: "100%",
    marginTop: 21,
    borderRadius: 4
  }, {
    width: 186,
    height: 15,
    marginTop: 17,
    borderRadius: 4
  }];
  var containerTickerBand = exports.containerTickerBand = Object.assign({
    zIndex: 1,
    width: "100%",
    backgroundColor: _theme.color.palette.almostBlackGrey
  }, _reactNative.Platform.select({
    ios: {
      shadowRadius: 24,
      shadowOpacity: 0.24,
      shadowOffset: {
        width: 0,
        height: 3
      }
    },
    android: {
      elevation: 3
    }
  }));
  var containerDefaultStyle = exports.containerDefaultStyle = [{
    width: "100%",
    padding: 24,
    flexDirection: "row",
    paddingTop: 50
  }, {
    flex: 1,
    marginEnd: 5
  }, {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    minHeight: 16
  }, {
    width: "100%",
    padding: 24,
    flexDirection: "row"
  }];
  var textDefaultStyle = exports.textDefaultStyle = [Object.assign({}, _text.presets.caption2Italic, {
    color: _theme.color.palette.whiteGrey
  }), Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.whiteGrey,
    marginTop: 12
  }), Object.assign({}, _text.presets.caption1Regular, {
    color: _theme.color.palette.whiteGrey,
    marginTop: 8
  })];
  var buttonStyle = exports.buttonStyle = [{
    flexDirection: "row",
    justifyContent: "space-between"
  }, {
    backgroundColor: _theme.color.palette.whiteGrey,
    borderColor: _theme.color.palette.whiteGrey,
    marginTop: 16,
    paddingHorizontal: 16,
    borderWidth: 0
  }];
  var urgentIconContainerStyle = exports.urgentIconContainerStyle = {
    paddingTop: 33,
    paddingRight: 24
  };
  var urgentIconStyle = exports.urgentIconStyle = {
    width: 48,
    height: 48
  };
