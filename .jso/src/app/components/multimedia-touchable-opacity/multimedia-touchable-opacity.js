  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MultimediaTouchableOpacity = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var _excluded = ["androidRippleColor", "children", "getStyle", "style"];
  var MultimediaTouchableOpacity = exports.MultimediaTouchableOpacity = (0, _react.forwardRef)(function (props, _ref) {
    var _props$androidRippleC = props.androidRippleColor,
      androidRippleColor = _props$androidRippleC === undefined ? "transparent" : _props$androidRippleC,
      children = props.children,
      getStyle = props.getStyle,
      customStyle = props.style,
      remains = (0, _objectWithoutProperties2.default)(props, _excluded);
    var _style = [];
    if (customStyle) {
      _style = Array.isArray(customStyle) ? customStyle : [customStyle];
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.Pressable, Object.assign({
      android_ripple: {
        color: androidRippleColor || _theme.color.palette.almostWhiteGrey80,
        foreground: true
      },
      style: function style(_ref2) {
        var _getStyle;
        var pressed = _ref2.pressed;
        return [{
          opacity: _reactNative.Platform.select({
            android: 1,
            ios: pressed ? 0.7 : 1
          })
        }].concat((0, _toConsumableArray2.default)((_getStyle = getStyle == null ? undefined : getStyle({
          pressed: pressed
        })) != null ? _getStyle : []), (0, _toConsumableArray2.default)(_style));
      }
    }, remains, {
      children: children
    }));
  });
