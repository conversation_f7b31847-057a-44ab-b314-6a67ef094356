  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var ModalCommonComponent = function ModalCommonComponent(props) {
    var children = props.children,
      _props$visible = props.visible,
      visible = _props$visible === undefined ? false : _props$visible,
      _props$animationInTim = props.animationInTiming,
      animationInTiming = _props$animationInTim === undefined ? 500 : _props$animationInTim,
      _props$animationOutTi = props.animationOutTiming,
      animationOutTiming = _props$animationOutTi === undefined ? 500 : _props$animationOutTi,
      _props$onModalHide = props.onModalHide,
      onModalHide = _props$onModalHide === undefined ? function () {
        return null;
      } : _props$onModalHide,
      coverScreen = props.coverScreen;
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      isVisible: visible,
      style: styles.modal,
      animationInTiming: animationInTiming,
      animationOutTiming: animationOutTiming,
      animationIn: "fadeIn",
      animationOut: "fadeOut",
      statusBarTranslucent: true,
      backdropColor: "#eee",
      backdropOpacity: 0.1,
      coverScreen: coverScreen,
      onModalHide: onModalHide,
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.container, {
          backgroundColor: props.isTransparentBackground ? "transparent" : _theme.color.palette.overlayColor
        }],
        children: children
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.overlayColor,
      flex: 1,
      justifyContent: "center"
    },
    modal: {
      margin: 0
    }
  });
  var _default = exports.default = ModalCommonComponent;
