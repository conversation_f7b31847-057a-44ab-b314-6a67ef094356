  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BenefitsCard = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _benefitsCard = _$$_REQUIRE(_dependencyMap[8]);
  var _butterflyBlue = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _tickGreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _rightArrow = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var cardContainer = {
    width: "100%"
  };
  var cardView = {
    backgroundColor: _theme.color.palette.whiteGrey,
    elevation: 5,
    borderRadius: 16,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.2,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0
    }
  };
  var rowsStyle = {
    flexDirection: "row"
  };
  var titleStyle = {
    color: _theme.color.palette.almostBlackGrey,
    marginStart: 12,
    marginTop: 16,
    paddingEnd: 42
  };
  var checkIconTextStyle = {
    color: _theme.color.palette.darkestGrey,
    marginStart: 12,
    marginTop: 16,
    flex: 1,
    paddingEnd: 42
  };
  var butterFlyImageStyle = {
    marginStart: 12,
    marginTop: 16,
    height: 24,
    width: 24
  };
  var greenTickStyle = {
    marginStart: 14,
    marginTop: 14,
    height: 24,
    width: 24
  };
  var arrowStyle = {
    position: "absolute",
    height: 12,
    width: 15,
    bottom: 22,
    right: 20
  };
  var crtBenefitsViewPaddingStyle = {
    paddingBottom: 24
  };
  var signUpBenefitsViewPaddingStyle = {
    paddingBottom: 41
  };
  var listStyle = {
    marginTop: 5
  };
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var silverLoadingColors = [_theme.color.palette.silver, _theme.color.background, _theme.color.palette.silver];
  var loadingContainerStyle = [{
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16,
    // width: "100%" ,
    paddingBottom: 24
  }, {
    flexDirection: "row"
  }, {
    flexDirection: "column"
  }, {
    flexDirection: "column",
    paddingStart: 13,
    paddingTop: 16
  }];
  var loadingStyle = [{
    flexDirection: "column"
  }, {
    width: 24,
    height: 24,
    borderRadius: 58,
    marginLeft: 11,
    marginTop: 14
  }, {
    width: 24,
    height: 24,
    borderRadius: 58,
    marginLeft: 11,
    marginTop: 8
  }, {
    flex: 1
  }, {
    height: 18,
    borderRadius: 4
  }, {
    width: 155,
    height: 18,
    marginTop: 21,
    borderRadius: 4
  }, {
    width: 155,
    height: 18,
    borderRadius: 4,
    marginTop: 14
  }];
  var crtBenefitsView = function crtBenefitsView(props) {
    var title = props.title,
      icon = props.icon;
    var dataArray = props.data;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: props.onPressed,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: crtBenefitsViewPaddingStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: rowsStyle,
          children: [icon ? (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: icon
            },
            style: butterFlyImageStyle
          }) : (0, _jsxRuntime.jsx)(_butterflyBlue.default, {
            style: butterFlyImageStyle
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            text: title,
            preset: "bodyTextBold",
            style: titleStyle
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: listStyle,
          children: dataArray.map(function (item, index) {
            return renderFlatListView(item, index);
          })
        }), (0, _jsxRuntime.jsx)(_rightArrow.default, {
          style: arrowStyle
        })]
      })
    });
  };
  var signUpBenefitsView = function signUpBenefitsView(props) {
    var title = props.title;
    var dataArray = props.data;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: props.onPressed,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: signUpBenefitsViewPaddingStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: rowsStyle,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            style: butterFlyImageStyle,
            source: _$$_REQUIRE(_dependencyMap[14])
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            text: title,
            preset: "bodyTextBold",
            style: titleStyle
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: listStyle,
          children: dataArray == null ? undefined : dataArray.map(function (item, index) {
            return renderFlatListView(item, index);
          })
        }), (0, _jsxRuntime.jsx)(_rightArrow.default, {
          style: arrowStyle
        })]
      })
    });
  };
  var renderFlatListView = function renderFlatListView(item, index) {
    return (0, _jsxRuntime.jsx)(_react.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: rowsStyle,
        children: [(0, _jsxRuntime.jsx)(_tickGreen.default, {
          style: greenTickStyle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 2,
          text: item.checkIconText,
          preset: "caption1Regular",
          style: checkIconTextStyle
        })]
      })
    }, index);
  };
  var defaultView = function defaultView(props) {
    var state = props.state;
    return (0, _jsxRuntime.jsx)(_react.Fragment, {
      children: state === _benefitsCard.BenefitsCardState.crtBenefits ? crtBenefitsView(props) : signUpBenefitsView(props)
    });
  };
  var LoadingView = function LoadingView() {
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: loadingContainerStyle[0],
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: loadingContainerStyle[1],
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: loadingContainerStyle[2],
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: silverLoadingColors,
            shimmerStyle: loadingStyle[1]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: silverLoadingColors,
            shimmerStyle: loadingStyle[1]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: silverLoadingColors,
            shimmerStyle: loadingStyle[2]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: loadingContainerStyle[3],
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: loadingStyle[4]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: loadingStyle[5]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: loadingStyle[6]
          })]
        })]
      })
    });
  };
  var BenefitsCard = exports.BenefitsCard = function BenefitsCard(props) {
    var type = props.type;
    var isLoading = type === _benefitsCard.BenefitsCardType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: cardContainer,
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: cardView,
        children: isLoading ? LoadingView() : defaultView(props)
      })
    });
  };
