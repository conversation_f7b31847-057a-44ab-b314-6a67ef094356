  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.VARIANT_MESSAGE_WAY_FINDING = exports.BottomSheetMapUnavailable = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _bottomSheetConfirm = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[8]);
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "BottomSheetMapUnavailable__";
  var iconBottomSheet = {
    width: 70,
    height: 70
  };
  var VARIANT_MESSAGE_WAY_FINDING = exports.VARIANT_MESSAGE_WAY_FINDING = /*#__PURE__*/function (VARIANT_MESSAGE_WAY_FINDING) {
    VARIANT_MESSAGE_WAY_FINDING["UNAVAILABLESERVICE"] = "UNAVAILABLESERVICE";
    return VARIANT_MESSAGE_WAY_FINDING;
  }({});
  var ComponentBottomSheet = function ComponentBottomSheet(_, ref) {
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isShow = _useState2[0],
      showModal = _useState2[1];
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr20 = !(0, _lodash.isEmpty)(messageCommon) && (messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR20";
    }));
    var VARIANT_MESSAGE = {
      UNAVAILABLESERVICE: {
        title: (ehr20 == null ? undefined : ehr20.header) || "Our maps are currently not available ",
        message: (ehr20 == null ? undefined : ehr20.subHeader) || "Way finding is also currently not available.\n Please try again later.",
        firstButton: (ehr20 == null ? undefined : ehr20.buttonLabel) || "Okay"
      }
    };
    var variantSelected = VARIANT_MESSAGE[VARIANT_MESSAGE_WAY_FINDING.UNAVAILABLESERVICE];
    var show = function show() {
      showModal(true);
    };
    var handleAccept = function handleAccept() {
      showModal(false);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        show: show
      };
    });
    var iconComponent = (0, _jsxRuntime.jsx)(_baseImage.default, {
      source: {
        uri: (0, _mediaHelper.handleImageUrl)(ehr20 == null ? undefined : ehr20.icon)
      },
      style: iconBottomSheet
    });
    return (0, _jsxRuntime.jsx)(_bottomSheetConfirm.BottomSheetConfirm, {
      visible: isShow,
      hideOnConfirm: true,
      title: variantSelected.title,
      message: variantSelected.message,
      confirmButtonText: variantSelected.firstButton,
      height: 280,
      onHide: handleAccept,
      onConfirm: handleAccept,
      testID: `${COMPONENT_NAME}BottomSheetMapUnavailable`,
      isClose: true,
      hasCancel: false,
      icon: (0, _utils.handleCondition)(!(0, _lodash.isEmpty)(ehr20 == null ? undefined : ehr20.icon), iconComponent, (0, _jsxRuntime.jsx)(_icons.InfoRed, {}))
    });
  };
  var BottomSheetMapUnavailable = exports.BottomSheetMapUnavailable = (0, _react.forwardRef)(ComponentBottomSheet);
