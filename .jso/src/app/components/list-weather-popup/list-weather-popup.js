  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _listWeatherPopup = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _flyHelper = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  // Dedicated test ID prefix for professional usage
  var TEST_ID_PREFIX = "listWeatherPopup_";
  var ListWeatherPopup = function ListWeatherPopup(props) {
    var visible = props.visible,
      onClosed = props.onClosed,
      onBackPressed = props.onBackPressed,
      onModalHide = props.onModalHide,
      dailyForecasts = props.dailyForecasts,
      destinationName = props.destinationName,
      isDayTime = props.isDayTime;
    var renderItem = function renderItem(_ref) {
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _listWeatherPopup.styles.itemContainer,
        testID: `${TEST_ID_PREFIX}itemContainer_${index}`,
        accessibilityLabel: `${TEST_ID_PREFIX}itemContainer_${index}`,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _listWeatherPopup.styles.containerDate,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _moment.default)(item.date).format("ddd"),
            preset: "caption1Regular",
            style: _listWeatherPopup.styles.textAlmostBackColor,
            testID: `${TEST_ID_PREFIX}item_dateDay_${index}`,
            accessibilityLabel: `${TEST_ID_PREFIX}item_dateDay_${index}`
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _moment.default)(item.date).format("D MMM"),
            preset: "caption1Regular",
            style: _listWeatherPopup.styles.textAlmostBackColor,
            testID: `${TEST_ID_PREFIX}item_dateDate_${index}`,
            accessibilityLabel: `${TEST_ID_PREFIX}item_dateDate_${index}`
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _listWeatherPopup.styles.iconContainer,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: index === 0 && !isDayTime ? item.nightIconUrl : item.dayIconUrl
            },
            style: _listWeatherPopup.styles.img,
            resizeMode: "stretch"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            text: index === 0 && !isDayTime ? item.nightIconPhrase : item.dayIconPhrase,
            style: _listWeatherPopup.styles.statusLabel,
            testID: `${TEST_ID_PREFIX}item_iconPhrase_${index}`,
            accessibilityLabel: `${TEST_ID_PREFIX}item_iconPhrase_${index}`
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _listWeatherPopup.styles.containerTemperature,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: `H: ${(0, _flyHelper.roundTemperature)(item.maximumTemperature)}`,
            style: _listWeatherPopup.styles.maximumTemperature,
            testID: `${TEST_ID_PREFIX}item_high_${index}`,
            accessibilityLabel: `${TEST_ID_PREFIX}item_high_${index}`
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: `L: ${(0, _flyHelper.roundTemperature)(item.minimumTemperature)}`,
            style: _listWeatherPopup.styles.minimumTemperature,
            testID: `${TEST_ID_PREFIX}item_low_${index}`,
            accessibilityLabel: `${TEST_ID_PREFIX}item_low_${index}`
          })]
        })]
      });
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: visible,
      onModalHide: onModalHide,
      onClosedSheet: onClosed,
      containerStyle: _listWeatherPopup.styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onBackPressed,
      animationInTiming: 500,
      animationOutTiming: 500,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _listWeatherPopup.styles.modalContainer,
        testID: `${TEST_ID_PREFIX}modalContainer`,
        accessibilityLabel: `${TEST_ID_PREFIX}modalContainer`,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _listWeatherPopup.styles.topModalContainer,
          onPress: onClosed,
          testID: `${TEST_ID_PREFIX}topModalContainer`,
          accessibilityLabel: `${TEST_ID_PREFIX}topModalContainer`
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _listWeatherPopup.styles.parentContainer,
          testID: `${TEST_ID_PREFIX}parentContainer`,
          accessibilityLabel: `${TEST_ID_PREFIX}parentContainer`,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _listWeatherPopup.styles.containerCloseIcon,
            onPress: onClosed,
            testID: `${TEST_ID_PREFIX}containerCloseIcon`,
            accessibilityLabel: `${TEST_ID_PREFIX}containerCloseIcon`,
            children: (0, _jsxRuntime.jsx)(_icons.CrossBlack, {})
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            text: (0, _i18n.translate)("flightDetailV2.weatherFlightCard.weatherAtDestination", {
              name: destinationName
            }),
            style: _listWeatherPopup.styles.title,
            numberOfLines: 1,
            testID: `${TEST_ID_PREFIX}titleText`,
            accessibilityLabel: `${TEST_ID_PREFIX}titleText`
          }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: dailyForecasts,
            renderItem: renderItem,
            keyExtractor: function keyExtractor(_, index) {
              return index.toString();
            },
            contentContainerStyle: _listWeatherPopup.styles.containerList,
            testID: `${TEST_ID_PREFIX}flatList`,
            accessibilityLabel: `${TEST_ID_PREFIX}flatList`
          })]
        })]
      })
    });
  };
  var _default = exports.default = ListWeatherPopup;
