  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    height = _Dimensions$get.height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.transparent,
      height: height
    },
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end"
    },
    topModalContainer: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1
    },
    parentContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: "auto",
      paddingHorizontal: 24,
      paddingTop: 12,
      paddingBottom: 36
    },
    containerCloseIcon: {
      position: "absolute",
      right: 6,
      top: 9,
      padding: 10
    },
    title: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      width: 200,
      alignSelf: "center",
      marginTop: 9
    },
    itemContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 16
    },
    statusLabel: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left",
      flex: 1
    },
    textAlmostBackColor: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    containerTemperature: {
      flexDirection: "row",
      justifyContent: "space-between",
      width: (0, _reactNativeSizeMatters.scale)(109)
    },
    maximumTemperature: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left",
      width: (0, _reactNativeSizeMatters.scale)(48)
    },
    minimumTemperature: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left",
      width: (0, _reactNativeSizeMatters.scale)(53),
      marginLeft: (0, _reactNativeSizeMatters.scale)(16)
    },
    iconContainer: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
      marginLeft: (0, _reactNativeSizeMatters.scale)(4),
      marginRight: (0, _reactNativeSizeMatters.scale)(8)
    },
    img: {
      width: 53,
      height: 32,
      marginRight: 4
    },
    containerList: {
      marginTop: 5
    },
    containerDate: {
      width: (0, _reactNativeSizeMatters.scale)(42)
    }
  });
