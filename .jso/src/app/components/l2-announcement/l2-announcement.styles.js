  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.listStyles = exports.bannerStyles = exports.MAIN_BANNER_POSITION = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    height = _Dimensions$get.height,
    width = _Dimensions$get.width;
  var MAIN_BANNER_POSITION = exports.MAIN_BANNER_POSITION = -91;
  var bannerStyles = exports.bannerStyles = _reactNative.StyleSheet.create({
    pageBannerContainerStyle: {
      backgroundColor: _theme.color.palette.almostBlackGrey,
      borderRadius: 8,
      padding: 8
    },
    listBannerContainerStyle: {
      backgroundColor: _theme.color.palette.almostBlackGrey,
      borderRadius: 8,
      padding: 8
    },
    contentContainerStyle: {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%"
    },
    iconContainerStyle: {
      height: 36,
      marginLeft: 8,
      marginRight: 4,
      marginVertical: 8,
      width: 36
    },
    messageTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.greyCCCCCC,
      flex: 1,
      marginLeft: 8,
      marginVertical: 8
    }),
    dismissBtnStyle: {
      alignSelf: "flex-start"
    }
  });
  var listStyles = exports.listStyles = _reactNative.StyleSheet.create({
    backdropStyle: {
      alignItems: "center",
      // backgroundColor: "rgba(18, 18, 18, 0.9)",
      height: height,
      left: 0,
      position: "absolute",
      top: 0,
      width: width,
      zIndex: 99999999
    },
    blurContainerStyle: {
      height: _reactNative.Dimensions.get('screen').height,
      left: 0,
      position: "absolute",
      top: 0,
      width: width
    },
    listContainerStyle: {
      height: "auto",
      marginTop: height / 5,
      maxHeight: height - 132,
      width: width - 32
    },
    closeAllBtnStyle: {
      alignItems: "center",
      backgroundColor: "transparent",
      borderColor: _theme.color.palette.whiteGrey,
      borderRadius: 60,
      borderWidth: 1,
      display: "flex",
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 68,
      marginTop: 24,
      minHeight: 40,
      width: 111,
      paddingHorizontal: 14,
      paddingVertical: 10
    },
    closeAllBtnLabelStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      marginRight: 4
    })
  });
