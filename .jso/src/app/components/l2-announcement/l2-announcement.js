  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _useL2Announcement = _$$_REQUIRE(_dependencyMap[8]);
  var _l2Announcement = _$$_REQUIRE(_dependencyMap[9]);
  var _bannerItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _bannerList = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[14]);
  var _navigators = _$$_REQUIRE(_dependencyMap[15]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _native = _$$_REQUIRE(_dependencyMap[18]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[21]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_16371129800070_init_data = {
    code: "function l2AnnouncementTsx1(){const{animatedBottomValue,index,animatedOpacityValue}=this.__closure;animatedBottomValue.value=index*6;return{bottom:animatedBottomValue.value,opacity:animatedOpacityValue.value};}"
  };
  var useBannerAnimations = function useBannerAnimations(params) {
    var bannersConfig = params.bannersConfig,
      index = params.index,
      list = params.list;
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabsPosition = _useContext.bottomTabsPosition,
      bottomTabActualHeight = _useContext.bottomTabActualHeight;
    var calculatePositionValue = function calculatePositionValue() {
      return index * 6;
    };
    var animatedBottomValue = (0, _reactNativeReanimated.useSharedValue)(calculatePositionValue() - 48);
    var animatedOpacityValue = (0, _reactNativeReanimated.useSharedValue)(0);
    var positionStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var l2AnnouncementTsx1 = function l2AnnouncementTsx1() {
        animatedBottomValue.value = index * 6;
        return {
          bottom: animatedBottomValue.value,
          opacity: animatedOpacityValue.value
        };
      };
      l2AnnouncementTsx1.__closure = {
        animatedBottomValue: animatedBottomValue,
        index: index,
        animatedOpacityValue: animatedOpacityValue
      };
      l2AnnouncementTsx1.__workletHash = 16371129800070;
      l2AnnouncementTsx1.__initData = _worklet_16371129800070_init_data;
      return l2AnnouncementTsx1;
    }(), [bottomTabActualHeight == null ? undefined : bottomTabActualHeight.value, bottomTabsPosition == null ? undefined : bottomTabsPosition.value, index]);
    (0, _react.useEffect)(function () {
      var _bannersConfig$index;
      animatedBottomValue.value = (0, _reactNativeReanimated.withTiming)(calculatePositionValue());
      animatedOpacityValue.value = (0, _reactNativeReanimated.withTiming)(bannersConfig == null || (_bannersConfig$index = bannersConfig[index]) == null ? undefined : _bannersConfig$index.opacity);
      return function () {
        animatedBottomValue.value = calculatePositionValue() - 48;
        animatedOpacityValue.value = 0;
      };
    }, [list == null ? undefined : list.length]);
    return {
      animatedBottomValue: animatedBottomValue,
      positionStyle: positionStyle
    };
  };
  var BannerListItem = function BannerListItem(props) {
    var bannersConfig = props.bannersConfig,
      handlePressBanner = props.handlePressBanner,
      index = props.index,
      isListVisible = props.isListVisible,
      item = props.item,
      list = props.list,
      onDismiss = props.onDismiss,
      skipBottomNavigation = props.skipBottomNavigation;
    var _useContext2 = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      l2AnnouncementPosition = _useContext2.l2AnnouncementPosition;
    var itemStyle = {
      display: isListVisible ? "none" : "flex",
      left: 8 + (list.length - index) * 8,
      width: _reactNative.Dimensions.get("window").width - (32 + ((list == null ? undefined : list.length) - index - 1) * 16)
    };
    var _useBannerAnimations = useBannerAnimations({
        bannersConfig: bannersConfig,
        index: index,
        list: list
      }),
      animatedBottomValue = _useBannerAnimations.animatedBottomValue,
      positionStyle = _useBannerAnimations.positionStyle;
    var onBannerLayout = function onBannerLayout(event) {
      var height = event.nativeEvent.layout.height;
      if (index === (list == null ? undefined : list.length) - 1 && !skipBottomNavigation) {
        l2AnnouncementPosition.value = animatedBottomValue.value + height + 8;
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      onLayout: onBannerLayout,
      style: [_l2Announcement.bannerStyles.pageBannerContainerStyle, itemStyle, positionStyle],
      children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        onPress: handlePressBanner,
        children: index === (list == null ? undefined : list.length) - 1 && (0, _jsxRuntime.jsx)(_bannerItem.default, {
          dataLength: list == null ? undefined : list.length,
          index: index,
          item: item,
          onDismissBanner: onDismiss,
          onPressBanner: handlePressBanner
        })
      }, item == null ? undefined : item.id)
    });
  };
  var _worklet_10140649794155_init_data = {
    code: "function l2AnnouncementTsx2(){const{skipBottomNavigation,bottomTabActualHeight,bottomTabsPosition}=this.__closure;var _bottomTabActualHeigh,_bottomTabsPosition;return{position:'absolute',bottom:skipBottomNavigation?20:Math.max(Math.max((_bottomTabActualHeigh=bottomTabActualHeight)===null||_bottomTabActualHeigh===void 0?void 0:_bottomTabActualHeigh.value,80)+((_bottomTabsPosition=bottomTabsPosition)===null||_bottomTabsPosition===void 0?void 0:_bottomTabsPosition.value)+8,24),flexDirection:'column-reverse'};}"
  };
  var L2AnnouncementBanner = function L2AnnouncementBanner(props) {
    var skipBottomNavigation = props.skipBottomNavigation;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _navigationHelper.useNavigation)();
    var announcementPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.announcementPayload);
    var dismissedAnnouncements = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.dismissedAnnouncements);
    var _useL2AnnouncementLis = (0, _useL2Announcement.useL2AnnouncementList)(),
      onCloseAnnouncementList = _useL2AnnouncementLis.onCloseAnnouncementList,
      onOpenAnnouncementList = _useL2AnnouncementLis.onOpenAnnouncementList,
      isListVisible = _useL2AnnouncementLis.visible;
    var _useL2AnnouncementBan = (0, _useL2Announcement.useL2AnnouncementBanner)(),
      onDismiss = _useL2AnnouncementBan.onDismiss;
    var _useContext3 = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      hideBottombar = _useContext3.hideBottombar,
      isL2AnnouncementDisplay = _useContext3.isL2AnnouncementDisplay;
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      refinedAnnouncements = _useState2[0],
      setRefinedAnnouncements = _useState2[1];
    var bannersConfig = (0, _react.useMemo)(function () {
      var _Array$fill$map;
      var configLength = Math.min(refinedAnnouncements == null ? undefined : refinedAnnouncements.length, 3);
      return (_Array$fill$map = Array(configLength).fill(null).map(function (_item, index) {
        var opacity = 0.96;
        var width = _reactNative.Dimensions.get("window").width - 32;
        var zIndex = 101;
        if (index === 1) {
          opacity = 0.6;
          width = _reactNative.Dimensions.get("window").width - 48;
          zIndex = 100;
        }
        if (index === 2) {
          opacity = 0.4;
          width = _reactNative.Dimensions.get("window").width - 64;
          zIndex = 99;
        }
        return {
          opacity: opacity,
          width: width,
          zIndex: zIndex
        };
      })) == null || _Array$fill$map.reverse == null ? undefined : _Array$fill$map.reverse();
    }, [refinedAnnouncements == null ? undefined : refinedAnnouncements.length]);
    var viewBanners = (0, _react.useMemo)(function () {
      return refinedAnnouncements == null || refinedAnnouncements.slice == null ? undefined : refinedAnnouncements.slice(-3);
    }, [JSON.stringify(refinedAnnouncements)]);
    var handlePressBanner = function handlePressBanner(bannerItem, isFromBannerList) {
      var shouldOpenAnnouncementList = !isFromBannerList && (refinedAnnouncements == null ? undefined : refinedAnnouncements.length) > 1;
      if (shouldOpenAnnouncementList) {
        hideBottombar == null || hideBottombar();
        onOpenAnnouncementList();
        return;
      }
      navigation.navigate(_constants.NavigationConstants.l2AnnouncementDetails, {
        notificationItem: bannerItem
      });
      onDismiss == null || onDismiss(bannerItem);
      dispatch(_notificationRedux.default.markAnnouncementAsRead(bannerItem == null ? undefined : bannerItem.id));
      if (!isFromBannerList) {
        var _bannerItem$extraJson, _bannerItem$extraJson2;
        var dataToBeSent = `${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerDefault}${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerSingle}${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerViewMessage}${bannerItem == null || (_bannerItem$extraJson = bannerItem.extraJsonData) == null ? undefined : _bannerItem$extraJson.customLabel} | ${bannerItem == null || (_bannerItem$extraJson2 = bannerItem.extraJsonData) == null ? undefined : _bannerItem$extraJson2.summary}`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.HomeExploreNotificationBanner, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.HomeExploreNotificationBanner, dataToBeSent));
      }
    };
    var checkValidL1Pages = function checkValidL1Pages(announcement) {
      var _announcement$extraJs;
      var currentRouteName = (0, _navigators.getActiveRouteName)(navigation == null || navigation.getState == null ? undefined : navigation.getState());
      var screenToCheck = "";
      switch (currentRouteName) {
        case _constants.NavigationConstants.bottomNavigation:
        case _constants.NavigationConstants.explore:
          screenToCheck = _constants.NotificationL1Page.Explore;
          break;
        case _constants.NavigationConstants.fly:
        case "flights":
          screenToCheck = _constants.NotificationL1Page.Flights;
          break;
        case _constants.NavigationConstants.dineShop:
        case _constants2.DINE_SHOP_TAB_SCREENS.dine:
          screenToCheck = _constants.NotificationL1Page.Dine;
          break;
        case _constants.NavigationConstants.account:
        case _constants.NavigationConstants.forYouScreen:
          screenToCheck = _constants.NotificationL1Page.Account;
          break;
        case _constants.NavigationConstants.parkingLanding:
          screenToCheck = _constants.NotificationL1Page.Parking;
          break;
        default:
          break;
      }
      var validScreens = announcement == null || (_announcement$extraJs = announcement.extraJsonData) == null || (_announcement$extraJs = _announcement$extraJs.screens) == null || _announcement$extraJs.map == null ? undefined : _announcement$extraJs.map(function (item) {
        return item == null ? undefined : item.tagName;
      });
      return validScreens == null || validScreens.some == null ? undefined : validScreens.some(function (screen) {
        return screen === screenToCheck;
      });
    };
    (0, _react.useEffect)(function () {
      ;
      (0, _asyncToGenerator2.default)(function* () {
        setRefinedAnnouncements(function () {
          var list = (announcementPayload == null || announcementPayload.filter == null ? undefined : announcementPayload.filter(function (ann) {
            return !(dismissedAnnouncements != null && dismissedAnnouncements.some != null && dismissedAnnouncements.some(function (idValue) {
              return idValue === (ann == null ? undefined : ann.id);
            })) && checkValidL1Pages(ann);
          })) || [];
          if ((list == null ? undefined : list.length) > 0) {
            isL2AnnouncementDisplay && (isL2AnnouncementDisplay.value = true);
          } else {
            onCloseAnnouncementList();
            isL2AnnouncementDisplay && (isL2AnnouncementDisplay.value = false);
          }
          return list;
        });
      })();
    }, [JSON.stringify(announcementPayload), navigation == null || navigation.getState == null ? undefined : navigation.getState()]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      return function () {
        return onCloseAnnouncementList();
      };
    }, []));
    var _useContext4 = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabsPosition = _useContext4.bottomTabsPosition,
      bottomTabActualHeight = _useContext4.bottomTabActualHeight;
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var l2AnnouncementTsx2 = function l2AnnouncementTsx2() {
        return {
          position: 'absolute',
          bottom: skipBottomNavigation ? 20 : Math.max(Math.max(bottomTabActualHeight == null ? undefined : bottomTabActualHeight.value, 80) + (bottomTabsPosition == null ? undefined : bottomTabsPosition.value) + 8, 24),
          flexDirection: 'column-reverse'
        };
      };
      l2AnnouncementTsx2.__closure = {
        skipBottomNavigation: skipBottomNavigation,
        bottomTabActualHeight: bottomTabActualHeight,
        bottomTabsPosition: bottomTabsPosition
      };
      l2AnnouncementTsx2.__workletHash = 10140649794155;
      l2AnnouncementTsx2.__initData = _worklet_10140649794155_init_data;
      return l2AnnouncementTsx2;
    }());
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: animatedStyle,
        children: viewBanners == null || viewBanners.map == null ? undefined : viewBanners.map(function (item, index, list) {
          return (0, _jsxRuntime.jsx)(BannerListItem, {
            bannersConfig: bannersConfig,
            handlePressBanner: handlePressBanner,
            index: index,
            isListVisible: isListVisible,
            item: item,
            list: list,
            onDismiss: onDismiss,
            skipBottomNavigation: skipBottomNavigation
          }, `${item == null ? undefined : item.id}_${index}`);
        })
      }), (0, _jsxRuntime.jsx)(_bannerList.default, {
        data: refinedAnnouncements,
        onPressBanner: handlePressBanner,
        onClose: onCloseAnnouncementList,
        visible: isListVisible,
        navigation: navigation
      })]
    });
  };
  var _default = exports.default = L2AnnouncementBanner;
