  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.lightGreyLoadingColors = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var WIDTH_ITEM_LISTING = width - 48;
  var styleForName = Object.assign({}, _text.presets.caption1Bold, {
    color: _theme.color.palette.lightPurple,
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 6,
    textAlign: "left",
    textAlignVertical: "top"
  });
  var styles = exports.styles = _reactNative.StyleSheet.create({
    clockIconStyles: {
      right: 0,
      top: 0,
      position: "absolute"
    },
    container: Object.assign({}, _theme.shadow.primaryShadow, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      height: 260,
      marginRight: 12,
      width: 192,
      alignSelf: 'center'
    }),
    date: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18,
      textAlign: "left",
      textAlignVertical: "bottom"
    }),
    imageListingStyle: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 140,
      width: "100%"
    },
    imageStyle: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 120,
      width: "100%"
    },
    listingContainer: Object.assign({}, _theme.shadow.primaryShadow, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      height: 262,
      width: WIDTH_ITEM_LISTING,
      alignSelf: 'center'
    }),
    name: Object.assign({}, styleForName),
    nameWithClock: Object.assign({}, styleForName, {
      width: "70%"
    }),
    nameWithIndicator: Object.assign({}, styleForName, {
      width: "70%"
    }),
    nameWithNewlyAdded: Object.assign({}, styleForName, {
      width: "70%"
    }),
    title: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      lineHeight: 20,
      textAlign: "left",
      textAlignVertical: "top"
    }),
    wrapContentItem: {
      height: 140,
      justifyContent: "space-between",
      padding: 16
    },
    wrapContentListingItem: {
      height: 120,
      justifyContent: "space-between",
      padding: 16
    },
    perkLabelContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lighterOrange,
      borderBottomLeftRadius: 4,
      color: _theme.color.palette.lightOrange,
      flexDirection: "row",
      height: 16,
      paddingHorizontal: 4,
      paddingVertical: 2,
      position: "absolute",
      right: 0,
      top: 0
    },
    perkLabelTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.lightOrange,
      lineHeight: 12,
      marginLeft: 4,
      textTransform: "none"
    })
  });
