  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TenantListingHorizontalType = exports.TenantListingHorizontal = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _color = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _reward = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "TenantListingHorizontal";
  var viewContainer = {
    flexDirection: "row"
  };
  var logoImageContainer = {
    width: 80,
    height: 80,
    borderRadius: 12,
    flexDirection: "column",
    alignSelf: "center"
  };
  var imageViewStyle = {
    marginRight: 12,
    marginTop: 16
  };
  var tenantTextStyle = {
    color: _color.color.palette.almostBlackGrey,
    marginBottom: 4
  };
  var locationTextStyle = {
    color: _color.color.palette.darkestGrey,
    marginBottom: 4
  };
  var dietaryViewStyle = {
    alignItems: 'center',
    borderColor: _color.color.palette.lighterGrey,
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    marginBottom: 4
  };
  var dietaryBoxStyle = {
    alignItems: 'center',
    display: 'flex',
    borderColor: _color.color.palette.lighterGrey,
    borderRadius: 4,
    borderWidth: 1,
    flexDirection: "row",
    paddingBottom: 2,
    paddingLeft: 6,
    paddingRight: 6,
    paddingTop: 1
  };
  var dietaryTextStyle = {
    color: _color.color.palette.darkestGrey,
    marginRight: 3
  };
  var categoryTextStyle = {
    color: _color.color.palette.darkestGrey,
    marginBottom: 4
  };
  var rewardTitleStyle = {
    color: _color.color.palette.gradientColor1Start,
    borderWidth: 1,
    borderColor: _color.color.palette.lightestPurple,
    backgroundColor: _color.color.palette.lightestPurple,
    paddingLeft: 5.27,
    paddingTop: 2,
    paddingBottom: 4,
    paddingRight: 4
  };
  var rewardIconStyle = {
    marginLeft: 8.94,
    alignSelf: "center"
  };
  var container = {
    flexDirection: "row"
  };
  var textContainer = {
    flexBasis: "72%",
    marginLeft: 16,
    flexDirection: "column",
    justifyContent: "center",
    alignSelf: "center"
  };
  var rewardContainer = {
    marginRight: 23,
    flexDirection: "row",
    alignSelf: "flex-start",
    borderWidth: 1,
    borderColor: _color.color.palette.lightestPurple,
    backgroundColor: _color.color.palette.lightestPurple,
    borderRadius: 4,
    height: 24
  };
  var skeletonLayout = [{
    width: 80,
    height: 80,
    borderRadius: 12
  }, {
    width: 74,
    height: 13,
    marginLeft: 16,
    marginTop: 8,
    borderRadius: 4
  }, {
    width: 178,
    height: 13,
    marginLeft: 16,
    marginTop: 13,
    borderRadius: 4
  }, {
    width: 178,
    height: 13,
    marginLeft: 16,
    marginTop: 13,
    borderRadius: 4
  }];
  var lightGreyLoadingColors = [_color.color.palette.lightGrey, _color.color.background, _color.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_color.color.palette.lightGrey, _color.color.background, _color.color.palette.lightGrey];
  var TenantListingHorizontalType = exports.TenantListingHorizontalType = /*#__PURE__*/function (TenantListingHorizontalType) {
    TenantListingHorizontalType["default"] = "default";
    TenantListingHorizontalType["loading"] = "loading";
    return TenantListingHorizontalType;
  }({});
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayout[0]
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayout[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayout[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayout[3]
        })]
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "TenantListingHorizontal" : _props$accessibilityL,
      categoryContent = props.categoryContent,
      dietaryData = props.dietaryData,
      imageUrlArray = props.imageUrlArray,
      itemIndex = props.itemIndex,
      location = props.location,
      logoUrl = props.logoUrl,
      onPressed = props.onPressed,
      rewardTitle = props.rewardTitle,
      tenantName = props.tenantName,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "TenantListingHorizontal" : _props$testID;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      testID: `${testID}__TouchableItemTenant`,
      accessibilityLabel: `${accessibilityLabel}__TouchableItemTenant`,
      accessible: false,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: viewContainer,
        children: [(0, _utils.handleCondition)(itemIndex !== undefined && itemIndex < 20, (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: logoImageContainer,
          source: {
            uri: logoUrl
          },
          testID: `${COMPONENT_NAME}__Logo`,
          accessibilityLabel: logoUrl
        }), (0, _jsxRuntime.jsx)(_reactNative2.Image, {
          style: logoImageContainer,
          source: {
            uri: logoUrl,
            cache: "force-cache"
          },
          testID: `${COMPONENT_NAME}__Logo__${itemIndex}`,
          accessibilityLabel: logoUrl
        })), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: textContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: tenantTextStyle,
            numberOfLines: 2,
            preset: "bodyTextBold",
            textBreakStrategy: "highQuality",
            testID: `${COMPONENT_NAME}__TenantName__${itemIndex}`,
            accessibilityLabel: tenantName,
            children: tenantName
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 2,
            style: locationTextStyle,
            preset: "caption1Regular",
            testID: `${COMPONENT_NAME}__Location__${itemIndex}`,
            accessibilityLabel: location,
            children: location
          }), dietaryData && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: dietaryViewStyle,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: dietaryBoxStyle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                numberOfLines: 1,
                style: dietaryTextStyle,
                preset: "caption1Regular",
                testID: `${COMPONENT_NAME}__Dietary__${itemIndex}`,
                accessibilityLabel: dietaryData == null ? undefined : dietaryData.content,
                children: dietaryData == null ? undefined : dietaryData.content
              }), dietaryData == null ? undefined : dietaryData.icon]
            })
          }), categoryContent && (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 2,
            style: categoryTextStyle,
            preset: "caption1Regular",
            testID: `${COMPONENT_NAME}__Category__${itemIndex}`,
            accessibilityLabel: categoryContent,
            children: categoryContent
          }), rewardTitle ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: rewardContainer,
            children: [(0, _jsxRuntime.jsx)(_reward.default, {
              style: rewardIconStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              style: rewardTitleStyle,
              preset: "caption2Regular",
              testID: `${COMPONENT_NAME}__RewardTitle__${itemIndex}`,
              accessibilityLabel: rewardTitle,
              children: rewardTitle
            })]
          }) : null]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        children: imageUrlArray && imageUrlArray.length ? (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          data: imageUrlArray.slice(0, 3),
          keyExtractor: function keyExtractor(index) {
            return index;
          },
          renderItem: function renderItem(_ref) {
            var item = _ref.item,
              index = _ref.index;
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: imageViewStyle,
              children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                source: {
                  uri: item
                },
                style: logoImageContainer,
                testID: `${COMPONENT_NAME}__Image__${index}`,
                accessibilityLabel: item
              })
            });
          },
          horizontal: true,
          testID: `${testID}__FlatList`,
          accessibilityLabel: `${accessibilityLabel}__FlatList`
        }) : null
      })]
    });
  };
  function _TenantListingHorizontal(props) {
    var isLoading = props.type === TenantListingHorizontalType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: container,
      children: isLoading ? loadingView() : defaultView(props)
    });
  }
  var TenantListingHorizontal = exports.TenantListingHorizontal = React.memo(_TenantListingHorizontal);
