  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactFreeze = _$$_REQUIRE(_dependencyMap[2]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  var Suspend = function Suspend(_ref) {
    var children = _ref.children,
      freeze = _ref.freeze;
    return (0, _jsxRuntime.jsx)(_reactFreeze.Freeze, {
      freeze: freeze,
      children: children
    });
  };
  var _default = exports.default = Suspend;
