  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _text2 = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var BottomSheetEarlyCheckIn = _react.default.memo(function (props) {
    var visible = props.visible,
      onClosedSheet = props.onClosedSheet,
      data = props.data,
      date = props.date;
    var ViewAvailibleCounter = function ViewAvailibleCounter(_ref) {
      var _item$ECIDateTimeHour;
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewItem,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtItemLocation,
            children: item == null ? undefined : item.locationName
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtItemAddress,
            children: item == null ? undefined : item.locationAddress
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewDataDate,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtOpening,
              children: "Opening Hours:"
            }), item == null || (_item$ECIDateTimeHour = item.ECIDateTimeHours) == null ? undefined : _item$ECIDateTimeHour.map(function (itemDate, indexDate) {
              var _itemDate$Date;
              return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: styles.viewRowItem,
                children: [(itemDate == null || (_itemDate$Date = itemDate.Date) == null ? undefined : _itemDate$Date.length) > 0 && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: styles.viewItemDate,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.txtDataDate,
                    children: itemDate == null ? undefined : itemDate.Date
                  })
                }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: styles.viewItemDayOfWeek,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.txtDataDate,
                    children: itemDate == null ? undefined : itemDate.ECIDate
                  })
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: styles.txtDataDate,
                  children: itemDate == null ? undefined : itemDate.ECIHour
                })]
              }, indexDate);
            })]
          })]
        }), index === (data == null ? undefined : data.length) - 1 ? null : (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.divinder
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      isModalVisible: visible,
      onClosedSheet: onClosedSheet,
      containerStyle: styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onClosedSheet,
      animationInTiming: 200,
      animationOutTiming: 200,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.headerFilter,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.rightHeader
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleSheet,
          tx: "departureSection.titleBottomSheetEarlyCheckin"
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onClosedSheet,
          children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {
            width: 24,
            height: 24
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewContent,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.dateCenter,
            children: date
          }), data == null ? undefined : data.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(ViewAvailibleCounter, {
              item: item,
              index: index
            }, index);
          })]
        })
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      maxHeight: _reactNative2.Dimensions.get('window').height - 80,
      minHeight: 272,
      overflow: "hidden",
      width: "100%"
    },
    viewContent: {
      flex: 1,
      paddingBottom: 32,
      paddingHorizontal: 24,
      alignItems: 'center'
    },
    viewDemo: {
      width: '100%',
      height: 50,
      marginTop: 20,
      backgroundColor: 'red'
    },
    headerFilter: {
      display: "flex",
      flexDirection: "row",
      marginVertical: 22,
      width: "100%",
      paddingHorizontal: 16
    },
    rightHeader: {
      width: 24
    },
    titleSheet: Object.assign({
      color: _theme.color.palette.almostBlackGrey
    }, _text2.presets.subTitleBold, {
      flexGrow: 2,
      textAlign: "center"
    }),
    dateCenter: Object.assign({
      color: _theme.color.palette.almostBlackGrey
    }, _text2.presets.bodyTextBlackRegular, {
      marginBottom: 16
    }),
    viewItem: {
      width: '100%',
      marginVertical: 16
    },
    viewRowItem: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    viewDataDate: {
      marginTop: 16
    },
    viewItemDate: {
      width: '20%'
    },
    viewItemDayOfWeek: {
      width: '17%'
    },
    txtItemLocation: Object.assign({}, _text2.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    txtItemAddress: Object.assign({}, _text2.presets.bodyTextBlackRegular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    txtDataDate: Object.assign({}, _text2.presets.bodyTextBlackRegular, {
      color: _theme.color.palette.darkestGrey
    }),
    txtOpening: Object.assign({}, _text2.presets.bodyTextBlackRegular, {
      color: _theme.color.palette.buntingGrey,
      marginBottom: 16
    }),
    divinder: {
      width: '100%',
      height: 1,
      backgroundColor: _theme.color.palette.lighterGrey
    }
  });
  var _default = exports.default = BottomSheetEarlyCheckIn;
