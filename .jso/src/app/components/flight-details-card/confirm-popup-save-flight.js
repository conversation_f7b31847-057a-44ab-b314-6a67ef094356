  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.ConfirmSaveFlight = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _button = _$$_REQUIRE(_dependencyMap[8]);
  var _color = _$$_REQUIRE(_dependencyMap[9]);
  var _confirmPopupSaveFlight = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _lodash = _$$_REQUIRE(_dependencyMap[12]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _modalManagerRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  var ConfirmSaveFlight = exports.ConfirmSaveFlight = function ConfirmSaveFlight(props) {
    var visible = props.visible,
      messageText = props.messageText,
      onButtonPressed = props.onButtonPressed,
      onClose = props.onClose,
      imageUrl = props.imageUrl,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ConfirmSaveFlight" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ConfirmSaveFlight" : _props$accessibilityL,
      title = props.title,
      textButtonCancel = props.textButtonCancel,
      textButtonConfirm = props.textButtonConfirm,
      _onModalHide = props.onModalHide,
      textButtonConnection = props.textButtonConnection,
      isShowButtonConnection = props.isShowButtonConnection,
      onButtonConnectionPressed = props.onButtonConnectionPressed,
      _props$disableCloseBu = props.disableCloseButton,
      disableCloseButton = _props$disableCloseBu === undefined ? false : _props$disableCloseBu,
      onSecondaryBtnPressed = props.onSecondaryBtnPressed,
      openPendingModal = props.openPendingModal;
    var dispatch = (0, _reactRedux.useDispatch)();
    // 310 : 280
    var getHeight = function getHeight() {
      if (!!messageText && isShowButtonConnection) {
        return 358;
      } else if (!!messageText && !isShowButtonConnection || !messageText && isShowButtonConnection) {
        return 310;
      }
      return 280;
    };
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      isVisible: visible,
      onBackdropPress: onClose,
      onSwipeComplete: onClose,
      swipeDirection: "down",
      animationInTiming: 100,
      animationOutTiming: 100,
      style: _confirmPopupSaveFlight.styles.modalStyle,
      hideModalContentWhileAnimating: true,
      onModalHide: function onModalHide() {
        _onModalHide == null || _onModalHide();
        openPendingModal && dispatch(_modalManagerRedux.default.openPendingModal());
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, _confirmPopupSaveFlight.styles.containerStyle, {
          height: getHeight()
        }),
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _confirmPopupSaveFlight.styles.iconStyle,
          children: imageUrl ? (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: imageUrl
            },
            style: _confirmPopupSaveFlight.styles.imageStyle
          }) : (0, _jsxRuntime.jsx)(_icons.FlyIcon, {
            width: 75,
            height: 75
          })
        }), !disableCloseButton ? (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _confirmPopupSaveFlight.styles.dismissIconContainer,
          onPress: function onPress() {
            return onClose == null ? undefined : onClose();
          },
          testID: `${testID}__TouchableClose`,
          accessibilityLabel: `${accessibilityLabel}__TouchableCrossClose`,
          children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {})
        }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _confirmPopupSaveFlight.styles.disableCloseButtonStyles
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _confirmPopupSaveFlight.styles.textContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _confirmPopupSaveFlight.styles.titleStyle,
            text: title || (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.title")
          }), !(0, _lodash.isEmpty)(messageText) && (0, _jsxRuntime.jsx)(_text.Text, {
            style: _confirmPopupSaveFlight.styles.messageStyle,
            text: messageText
          })]
        }), isShowButtonConnection && (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _confirmPopupSaveFlight.styles.buttonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_color.color.palette.gradientColor1Start, _color.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            text: textButtonConnection || (0, _i18n.translate)("flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton"),
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "secondary",
            statePreset: "default",
            backgroundPreset: "light",
            onPress: function onPress() {
              onButtonConnectionPressed == null || onButtonConnectionPressed();
            },
            testID: `${testID}__ButtonExecuteAndClose`,
            accessibilityLabel: `${testID}__ButtonExecuteAndClose`
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _confirmPopupSaveFlight.styles.buttonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_color.color.palette.gradientColor1Start, _color.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            text: textButtonConfirm || (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.saveAndContinue"),
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "secondary",
            statePreset: "default",
            backgroundPreset: "light",
            onPress: function onPress() {
              onClose == null || onClose();
              onButtonPressed == null || onButtonPressed();
            },
            testID: `${testID}__ButtonExecuteAndClose`,
            accessibilityLabel: `${testID}__ButtonExecuteAndClose`
          })
        }), (0, _jsxRuntime.jsx)(_button.Button, {
          text: textButtonCancel || (0, _i18n.translate)("flightDetails.popupConfirmSaveFlight.noThanks"),
          onPress: function onPress() {
            onClose == null || onClose();
            onSecondaryBtnPressed == null || onSecondaryBtnPressed();
          },
          testID: `${testID}__ButtonExecuteAndClose`,
          accessibilityLabel: `${testID}__ButtonExecuteAndClose`,
          textPreset: "buttonLarge",
          textStyle: {
            color: _color.color.palette.lightPurple
          }
        })]
      })
    });
  };
  var _default = exports.default = ConfirmSaveFlight;
