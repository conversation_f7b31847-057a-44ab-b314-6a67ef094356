  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFlightEarlyCheckinV2 = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[4]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _envParams = _$$_REQUIRE(_dependencyMap[6]);
  var _queries = _$$_REQUIRE(_dependencyMap[7]);
  var useFlightEarlyCheckinV2 = exports.useFlightEarlyCheckinV2 = function useFlightEarlyCheckinV2(airlineDetails, params, enableEciDynamicDisplay) {
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loadingData = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isCalled = _useState4[0],
      setCalled = _useState4[1];
    var _useState5 = (0, _react.useState)(null),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      dataEarlyCheckinV2 = _useState6[0],
      setDataEarlyCheckinV2 = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      error = _useState8[0],
      setError = _useState8[1];
    (0, _react.useEffect)(function () {
      if (airlineDetails) {
        getDataEarlyCheckInV2();
      }
    }, [airlineDetails]);
    var getDataEarlyCheckInV2 = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        try {
          if (enableEciDynamicDisplay) {
            var result = yield getData(params);
            setDataEarlyCheckinV2(result);
          }
        } catch (error) {
          console.error(error);
        }
      });
      return function getDataEarlyCheckInV2() {
        return _ref.apply(this, arguments);
      };
    }();
    var getData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (input) {
        setLoading(true);
        try {
          var _env, _env2;
          var response = yield (0, _request.default)({
            url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.getFlightEarlyCheckinV2, {
              input: input
            }),
            parameters: {},
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
            }
          });
          if (response != null && response.data) {
            var _response$data;
            setCalled(true);
            setLoading(false);
            return response == null || (_response$data = response.data) == null ? undefined : _response$data.data;
          } else {
            setLoading(false);
            var _error = response.data.errors || "API failed";
            setError(_error);
            return null;
          }
        } catch (error) {
          setLoading(false);
          setError("API failed");
        }
      });
      return function getData(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    return {
      loadingData: loadingData,
      isCalled: isCalled,
      dataEarlyCheckinV2: dataEarlyCheckinV2,
      error: error
    };
  };
