  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.handleNavigationFlightDetail = exports.FlightDetailsCardV2 = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _text2 = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[10]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[11]);
  var _button = _$$_REQUIRE(_dependencyMap[12]);
  var _baggageFirstbag = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _baggageLastbag = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _baggageNotavailable = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _utils = _$$_REQUIRE(_dependencyMap[20]);
  var _lodash = _$$_REQUIRE(_dependencyMap[21]);
  var _native = _$$_REQUIRE(_dependencyMap[22]);
  var _baggageTracker = _$$_REQUIRE(_dependencyMap[23]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _location_filled = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _icons = _$$_REQUIRE(_dependencyMap[27]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _flightDetailEarlyCheckinV = _$$_REQUIRE(_dependencyMap[29]);
  var _useFlightEarlyCheckinV = _$$_REQUIRE(_dependencyMap[30]);
  var _flightFastCheckin = _$$_REQUIRE(_dependencyMap[31]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[32]));
  var _fly = _$$_REQUIRE(_dependencyMap[33]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[34]);
  var _firebase = _$$_REQUIRE(_dependencyMap[35]);
  var _useFlightDetailClickEvent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[36]));
  var _saveFlightTravelOption = _$$_REQUIRE(_dependencyMap[37]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[38]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[39]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var unSave = "Unsave";
  var notAvailable = "N/A";
  var maxSharecodeFlightNumber = 12;
  var terminalLabel = "Terminal";
  var rowCheckIn = "Check-in row";
  var gateNumber = "Gate";
  var baggageLabel = "Baggage belt";
  var onlineCheckInLabel = "Online check-in";
  var earlyCheckInLabel = "Early check-in";
  var departureDirectionText = "Departure";
  var arrivalDirectionText = "Arrival";
  var outerContainer = Object.assign({
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16,
    width: width - _responsive.default.getFontSize(48)
  }, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 16,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      },
      backgroundColor: _theme.color.palette.whiteGrey
    },
    android: {
      elevation: 3,
      backgroundColor: _theme.color.palette.whiteGrey
    }
  }), {
    alignSelf: "center",
    marginTop: 24,
    marginBottom: 12
  });
  var outerLoadingViewContainer = Object.assign({}, outerContainer);
  var loadingStyle = [{
    width: 84,
    borderRadius: 4,
    height: 26
  }, {
    width: 50,
    height: 22,
    borderRadius: 4,
    marginBottom: 9
  }, {
    width: 94,
    height: 13,
    borderRadius: 4
  }, {
    marginTop: 4,
    width: 24,
    height: 24,
    borderRadius: 16
  }, {
    marginTop: 9,
    width: 43,
    height: 13,
    borderRadius: 4
  }, {
    width: 164,
    height: 13,
    borderRadius: 4,
    marginBottom: 5,
    left: 10
  }, {
    width: 164,
    height: 13,
    left: 10,
    borderRadius: 4
  }, {
    width: 43,
    height: 16,
    borderRadius: 4
  }, {
    marginTop: 18,
    width: 50,
    height: 7,
    borderRadius: 2
  }, {
    marginTop: 12,
    width: 110,
    height: 15,
    borderRadius: 4
  }];
  var secondSectionLoadingContainer = {
    marginTop: 32
  };
  var thirdSectionLoadingContainer = {
    marginTop: 18,
    top: 2
  };
  var firstRowStyle = {
    flexDirection: "row"
  };
  var columnStyle = {
    width: width / 2 - (0, _reactNativeSizeMatters.scale)(40)
  };
  var rightColumnStyle = Object.assign({}, columnStyle, {
    flex: 1,
    alignItems: "flex-end"
  });
  var thirdSectionRightColumnStyle = {
    flex: 3
  };
  var leftColumnStyle = {
    width: width / 2 - (0, _reactNativeSizeMatters.scale)(40)
  };
  var greenBackgroundStyle = {
    borderRadius: 8,
    paddingLeft: 8,
    paddingRight: 8,
    paddingTop: 4,
    paddingBottom: 4,
    justifyContent: "center",
    backgroundColor: _theme.color.palette.lightestGreen,
    marginBottom: 20
  };
  var greenStatusText = {
    paddingBottom: 1,
    color: _theme.color.palette.basegreen
  };
  var redBackgroundStyle = Object.assign({}, greenBackgroundStyle, {
    backgroundColor: _theme.color.palette.lightestRed
  });
  var redStatusText = Object.assign({}, greenStatusText, {
    color: _theme.color.palette.baseRed
  });
  var blackBackgroundStyle = Object.assign({}, greenBackgroundStyle, {
    backgroundColor: _theme.color.palette.lighterGrey
  });
  var blackStatusText = Object.assign({}, greenStatusText, {
    color: _theme.color.palette.almostBlackGrey
  });
  var thirdSectionleftColumnStyle = {
    flex: 0.5
  };
  var innerContainerStyle = {
    marginLeft: 16,
    marginRight: 16,
    marginTop: 16
  };
  var horizontalLineStyle = {
    marginTop: 24,
    width: width - (0, _reactNativeSizeMatters.scale)(76),
    height: 1,
    alignSelf: "center",
    backgroundColor: _theme.color.palette.lighterGrey
  };
  var horizontalDefaultViewLineStyle = Object.assign({}, horizontalLineStyle, {
    width: width - (0, _reactNativeSizeMatters.scale)(75),
    marginTop: 16
  });
  var thirdAndFourthSectionDefaultView = {
    marginTop: 16,
    marginRight: 8,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  };
  var buttonStyle = {
    bottom: 2
  };
  var flexStyle = {
    flex: 1
  };
  var labelTextStyle = {
    color: _theme.color.palette.darkestGrey,
    marginBottom: 4
  };
  var timeTextStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var darkTextStyle = {
    color: _theme.color.palette.darkestGrey
  };
  var darkDateTextStyle = Object.assign({}, _text2.presets.caption2Bold, {
    color: _theme.color.palette.darkestGrey,
    marginBottom: 4
  });
  var rowViewStyle = {
    flexDirection: "row",
    alignItems: "center"
  };
  var rowFlex = {
    flexDirection: "row"
  };
  var wrapImageStyle = {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden"
  };
  var imageStyle = {
    width: 24,
    height: 24,
    borderRadius: 12
  };
  var flightNumberText = {
    alignSelf: "center",
    color: _theme.color.palette.almostBlackGrey
  };
  var verticalLineStyle = {
    alignSelf: "center",
    height: 35,
    borderLeftWidth: 1,
    borderLeftColor: _theme.color.palette.lighterGrey,
    marginLeft: 16,
    marginRight: 16
  };
  var slavesViewStyle = {
    flex: 1,
    justifyContent: "flex-start",
    alignSelf: "center"
  };
  var lasthorizontalDefaultViewLineStyle = Object.assign({}, horizontalDefaultViewLineStyle, {
    marginTop: 16
  });
  var fourthRowLoadingViewStyle = {
    flexDirection: "row",
    marginTop: 27
  };
  var fourthRowSecondColumnStyle = {
    flex: 1,
    alignItems: "center"
  };
  var fourthRowLastColumnStyle = {
    flex: 1,
    alignItems: "flex-end"
  };
  var lastRowLoadingViewStyle = {
    marginBottom: 18
  };
  var labelStyle = {
    color: _theme.color.palette.darkestGrey,
    marginBottom: 4
  };
  var terminalNotAvailableStyle = {
    color: _theme.color.palette.darkestGrey
  };
  var nearestCarPackLabelStyle = {
    color: _theme.color.palette.almostBlackGrey,
    maxWidth: 100,
    alignSelf: 'flex-start'
  };
  var baggageBeltStatusIconStyle = {
    marginRight: 8
  };
  var baggageBeltStatusStyle = {
    paddingTop: 2,
    color: _theme.color.palette.darkestGrey,
    flex: 0.9,
    textAlign: "left"
  };
  var baggageBeltLinkStyle = Object.assign({}, _text2.presets.caption1Bold, {
    paddingTop: 2,
    flex: 1,
    color: _theme.color.palette.lightPurple
  });
  var terminalDetailStyle = {
    flexDirection: "row",
    alignItems: "center",
    flex: 1
  };
  var lastRowDefaultViewStyle = {
    flexDirection: "row",
    marginBottom: 16,
    marginTop: 16
  };
  var darkestGreyColor = {
    color: _theme.color.palette.almostBlackGrey
  };
  var almostBlackGreyColor = {
    color: _theme.color.palette.almostBlackGrey
  };
  var gateTextStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var locationIconStyleActive = {
    alignSelf: "center",
    marginRight: 5,
    marginLeft: 3,
    color: _theme.color.palette.lightPurple
  };
  var locationIconStyleInActive = {
    alignSelf: "center",
    marginLeft: 5,
    color: _theme.color.palette.midGrey
  };
  var pickupIconStyleInActive = {
    alignSelf: "center",
    marginLeft: 0,
    marginRight: 4,
    color: _theme.color.palette.midGrey
  };
  var baggageFlexStyle = {
    flex: 1
  };
  var baggageLeftStyle = {
    width: (0, _reactNativeSizeMatters.scale)(125)
  };
  var timeStampStyle = {
    paddingVertical: 16,
    alignItems: "center"
  };
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var lighterGreyLoadingColorsV2 = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var dotsLoadingStyle = {
    position: "absolute",
    top: -15,
    right: -80,
    width: 200,
    height: 60
  };
  var textLinkStyle = Object.assign({}, _text2.presets.textLink, {
    fontSize: _responsive.default.getFontSize(16),
    lineHeight: _responsive.default.getFontSize(22)
  });
  var wrapShowFlightTime = {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8
  };
  var oldTimeText = {
    marginRight: 7
  };
  var textNumberDaysDiff = {
    color: _theme.color.palette.almostBlackGrey,
    textAlign: "left",
    marginLeft: 4
  };
  var redLineThroughText = {
    backgroundColor: _theme.color.palette.baseRed,
    height: 2,
    position: "absolute",
    top: 10,
    width: 52
  };
  var isShowOnlineAndEarlyCheckIn = function isShowOnlineAndEarlyCheckIn(statusTag) {
    var status = statusTag == null ? undefined : statusTag.toLowerCase();
    var isShowOnlineCheckIn = !/cancelled/gim.test(status) && !/departed/gim.test(status);
    var isShowEarlyCheckIn = !/delayed/gim.test(status) && !/cancelled/gim.test(status) && !/departed/gim.test(status);
    return {
      isShowOnlineCheckIn: isShowOnlineCheckIn,
      isShowEarlyCheckIn: isShowEarlyCheckIn
    };
  };
  var loadingView = function loadingView() {
    var flyDetailP1Flag = (0, _react.useContext)(_fly.FLY_CONTEXT).Handlers.flyDetailP1Flag;
    var isFlightDetailP1 = (0, _remoteConfig.isFlagOnCondition)(flyDetailP1Flag);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: innerContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingStyle[0]
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: secondSectionLoadingContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: firstRowStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: leftColumnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[1]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[2]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: columnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[1]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[2]
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: horizontalLineStyle
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: thirdSectionLoadingContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: firstRowStyle,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: thirdSectionleftColumnStyle,
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: isFlightDetailP1 ? lighterGreyLoadingColorsV2 : lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[3]
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: flexStyle,
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[4]
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: thirdSectionRightColumnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[5]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[6]
            })]
          })]
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: horizontalLineStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: fourthRowLoadingViewStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: flexStyle,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: loadingStyle[7]
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: fourthRowSecondColumnStyle,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: loadingStyle[7]
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: fourthRowLastColumnStyle,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: loadingStyle[7]
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: horizontalLineStyle
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: lastRowLoadingViewStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: rowViewStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: leftColumnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[8]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[9]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: columnStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[8]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: loadingStyle[9]
            })]
          })]
        })
      })]
    });
  };
  var flightStatusTag = function flightStatusTag(statusMapping) {
    var _statusMapping$status;
    var statusTag = statusMapping == null ? undefined : statusMapping.details_status_en;
    var beltStatusMapping = statusMapping == null ? undefined : statusMapping.belt_status_en;
    var statusColor = statusMapping == null || (_statusMapping$status = statusMapping.status_text_color) == null ? undefined : _statusMapping$status.toLowerCase();
    var status = statusTag == null ? undefined : statusTag.toLowerCase();
    if (!(0, _lodash.isEmpty)(beltStatusMapping)) {
      if (["grey", "black"].includes(statusColor)) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: blackBackgroundStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: Object.assign({}, blackStatusText, {
              color: statusColor
            }),
            children: beltStatusMapping
          })
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: greenBackgroundStyle,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Bold",
          style: Object.assign({}, greenStatusText, {
            color: statusColor
          }),
          children: beltStatusMapping
        })
      });
    }
    switch (true) {
      case /gate open/gim.test(status):
      case /boarding/gim.test(status):
      case /landed/gim.test(status):
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: greenBackgroundStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: Object.assign({}, greenStatusText, {
              color: statusColor
            }),
            children: statusTag
          })
        });
      case /gate closed/gim.test(status):
      case /last call/gim.test(status):
      case /gate closing/gim.test(status):
      case /cancelled/gim.test(status):
      case /re-timed/gim.test(status):
      case /delayed/gim.test(status):
      case /diverted/gim.test(status):
      case /new gate/gim.test(status):
      case /go to info counter/gim.test(status):
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: redBackgroundStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: Object.assign({}, redStatusText, {
              color: statusColor
            }),
            children: statusTag
          })
        });
      default:
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: blackBackgroundStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: Object.assign({}, blackStatusText, {
              color: statusColor
            }),
            children: statusTag
          })
        });
    }
  };
  var getValue = function getValue(value, defaultValue) {
    return value || defaultValue;
  };
  var handleNavigationFlightDetail = exports.handleNavigationFlightDetail = function handleNavigationFlightDetail(urlWeb, isSaved, navigation, dispatch) {
    if (isSaved) {
      if ((urlWeb == null ? undefined : urlWeb.length) > 0) {
        navigation == null || navigation.navigate(_constants.NavigationConstants.webview, {
          uri: urlWeb,
          screen: "flightDetails"
        });
      }
      return null;
    }
    dispatch(_flyRedux.FlyCreators.flyShowModalConfirmSaveFly(true));
  };
  var renderSlaves = function renderSlaves(slaves) {
    if (!slaves || (slaves == null ? undefined : slaves.length) === 0) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: slavesViewStyle,
      children: (0, _jsxRuntime.jsx)(_text.Text, {
        style: darkTextStyle,
        preset: "caption1Regular",
        children: slaves == null ? undefined : slaves.slice(0, maxSharecodeFlightNumber).join(", ")
      })
    });
  };
  var _worklet_8043578450132_init_data = {
    code: "function flightDetailsCardV2Tsx1(){const{paddingComponentTerminal}=this.__closure;return{width:paddingComponentTerminal.value};}"
  };
  var defaultView = function defaultView(props) {
    var _onlineCheckIn$link, _earlyCheckIn$link;
    var flyDetailP1Flag = (0, _react.useContext)(_fly.FLY_CONTEXT).Handlers.flyDetailP1Flag;
    var scheduledDate = props.scheduledDate,
      scheduledTime = props.scheduledTime,
      onButtonPressed = props.onButtonPressed,
      status = props.status,
      flightNumber = props.flightNumber,
      isFlightSaved = props.isFlightSaved,
      slaves = props.slaves,
      terminal = props.terminal,
      gate = props.gate,
      onlineCheckIn = props.onlineCheckIn,
      earlyCheckIn = props.earlyCheckIn,
      baggageBelt = props.baggageBelt,
      checkInRow = props.checkInRow,
      showGate = props.showGate,
      state = props.state,
      navigation = props.navigation,
      removeFlightLoading = props.removeFlightLoading,
      timeStamp = props.timeStamp,
      isMSError = props.isMSError,
      onPressFlightCardLinks = props.onPressFlightCardLinks,
      onPressCheckInRow = props.onPressCheckInRow,
      onPressGate = props.onPressGate,
      onPressBaggageBelt = props.onPressBaggageBelt,
      onPressNearestCarPark = props.onPressNearestCarPark,
      onPressPickupOrDropOff = props.onPressPickupOrDropOff,
      onPressAccessiblePickupOrDropOff = props.onPressAccessiblePickupOrDropOff,
      statusMapping = props.statusMapping,
      airlineDetails = props.airlineDetails,
      isSaved = props.isSaved,
      testID = props.testID,
      displayTimestamp = props.displayTimestamp,
      direction = props.direction,
      technicalFlightStatus1 = props.technicalFlightStatus1,
      baggageTracking = props.baggageTracking,
      disclaimerText = props.disclaimerText,
      enableEciDynamicDisplay = props.enableEciDynamicDisplay,
      airportDetails = props.airportDetails,
      saveFlightWhenCheckInOnline = props.saveFlightWhenCheckInOnline,
      selectedTopTravelOption = props.selectedTopTravelOption;
    var getIntoCityOrAirportPayload = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.getIntoCityOrAirportPayload(state);
    });
    var _useFlightEarlyChecki = (0, _useFlightEarlyCheckinV.useFlightEarlyCheckinV2)(airportDetails, {
        "airlineCode": airlineDetails == null ? undefined : airlineDetails.code,
        "airportCode": airportDetails == null ? undefined : airportDetails.code,
        "flightNumber": flightNumber,
        "flightStatus": statusMapping == null ? undefined : statusMapping.details_status_en,
        "scheduledDatetime": `${scheduledDate} ${scheduledTime}`,
        "checkinRow": checkInRow,
        "terminal": terminal
      }, enableEciDynamicDisplay),
      loadingData = _useFlightEarlyChecki.loadingData,
      isCalled = _useFlightEarlyChecki.isCalled,
      dataEarlyCheckinV2 = _useFlightEarlyChecki.dataEarlyCheckinV2;
    var _isShowOnlineAndEarly = isShowOnlineAndEarlyCheckIn(status),
      isShowEarlyCheckIn = _isShowOnlineAndEarly.isShowEarlyCheckIn,
      isShowOnlineCheckIn = _isShowOnlineAndEarly.isShowOnlineCheckIn;
    var flightState = state.toLowerCase();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var isPendingCheckInOnline = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyPendingCheckInOnline);
    var isPendingSaveFlight = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyPendingSaveFlight);
    var isFocused = (0, _native.useIsFocused)();
    var isTravellerLocal = selectedTopTravelOption === _saveFlightTravelOption.TravelOption.iAmTravelling;
    var isDepartureLocal = direction === _flightProps.FlightDirection.departure;
    var _useFlightDetailClick = (0, _useFlightDetailClickEvent.default)({
        isDeparture: isDepartureLocal,
        isTraveller: isTravellerLocal
      }),
      logClickEvent = _useFlightDetailClick.logClickEvent;
    var isFlightDetailP1 = (0, _remoteConfig.isFlagOnCondition)(flyDetailP1Flag);
    var paddingComponentTerminal = (0, _reactNativeReanimated.useSharedValue)(126);
    var dispatch = (0, _reactRedux.useDispatch)();
    var BAGGAGE_HANDLERS = (0, _react.useContext)(_baggageTracker.BAGGAGE_TRACKER_CONTEXT).Handlers;
    var trackYourBaggage = function trackYourBaggage() {
      logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.TRACK_BAGGAGE);
      BAGGAGE_HANDLERS.baggage_tracker_request({
        navigation: navigation,
        sourceSystem: _constants.SOURCE_SYSTEM.OTHERS,
        flight: {
          flightDetailsData: {
            flightNumber: flightNumber,
            terminal: terminal,
            scheduledDate: scheduledDate,
            scheduledTime: scheduledTime
          },
          flyItem: {
            direction: direction
          }
        }
      });
    };
    var animatedStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var flightDetailsCardV2Tsx1 = function flightDetailsCardV2Tsx1() {
        return {
          width: paddingComponentTerminal.value
        };
      };
      flightDetailsCardV2Tsx1.__closure = {
        paddingComponentTerminal: paddingComponentTerminal
      };
      flightDetailsCardV2Tsx1.__workletHash = 8043578450132;
      flightDetailsCardV2Tsx1.__initData = _worklet_8043578450132_init_data;
      return flightDetailsCardV2Tsx1;
    }());
    (0, _react.useEffect)(function () {
      if ((0, _utils.handleCondition)(isPendingCheckInOnline && isLoggedIn && isFocused, true, false)) {
        saveFlightWhenCheckInOnline == null || saveFlightWhenCheckInOnline(isSaved);
        dispatch(_flyRedux.FlyCreators.flyPendingCheckInOnline(false));
      }
    }, [isPendingCheckInOnline, isLoggedIn, isFocused]);
    (0, _react.useEffect)(function () {
      if ((0, _utils.handleCondition)(isPendingSaveFlight && isLoggedIn && isFlightSaved, true, false)) {
        saveFlightWhenCheckInOnline == null || saveFlightWhenCheckInOnline(isFlightSaved);
        dispatch(_flyRedux.FlyCreators.flyPendingSaveFlight(false));
      }
    }, [isPendingSaveFlight, isFlightSaved]);
    var baggageStatusDisplay = (0, _react.useCallback)(function () {
      var defaultBagContent = (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_baggageNotavailable.default, {
          style: baggageBeltStatusIconStyle
        }), (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
          style: baggageBeltStatusStyle,
          numberOfLines: 1,
          preset: "caption1Bold",
          tx: "flightDetails.notAvailableYet"
        }, (0, _utils.accessibility)({
          testID: `${testID}__BaggageTrackerStatus`,
          accessibilityLabel: `${testID}__BaggageTrackerStatus`,
          OS: _reactNative2.Platform.OS
        })))]
      });
      var firstBagContent = (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_baggageFirstbag.default, {
          style: baggageBeltStatusIconStyle
        }), (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
          style: baggageBeltStatusStyle,
          numberOfLines: 1,
          preset: "caption1Bold",
          tx: "flightDetails.baggage.firstBagOnBelt"
        }, (0, _utils.accessibility)({
          testID: `${testID}__BaggageTrackerStatus`,
          accessibilityLabel: `${testID}__BaggageTrackerStatus`,
          OS: _reactNative2.Platform.OS
        })))]
      });
      var lastBagContent = (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_baggageLastbag.default, {
          style: baggageBeltStatusIconStyle
        }), (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
          style: baggageBeltStatusStyle,
          numberOfLines: 1,
          preset: "caption1Bold",
          tx: "flightDetails.baggage.lastBagOnBelt"
        }, (0, _utils.accessibility)({
          testID: `${testID}__BaggageTrackerStatus`,
          accessibilityLabel: `${testID}__BaggageTrackerStatus`,
          OS: _reactNative2.Platform.OS
        })))]
      });
      return (0, _utils.handleCondition)(technicalFlightStatus1 === "FB", firstBagContent, (0, _utils.handleCondition)(technicalFlightStatus1 === "LB", lastBagContent, defaultBagContent));
    }, [technicalFlightStatus1]);
    var baggageTrackingButtonArrival = (0, _react.useCallback)(function () {
      var contentBaggage = (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: trackYourBaggage,
        children: (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
          style: baggageBeltLinkStyle,
          numberOfLines: 1,
          tx: "flightDetails.baggage.trackYourBaggage"
        }, (0, _utils.accessibility)({
          testID: `${testID}__BaggageTrackerButton`,
          accessibilityLabel: `${testID}__BaggageTrackerButton`,
          OS: _reactNative2.Platform.OS
        })))
      });
      return (0, _utils.handleCondition)(baggageTracking, contentBaggage, null);
    }, [baggageTracking]);
    var baggageTrackingButtonDeparture = (0, _react.useCallback)(function () {
      var contentBaggage = (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: lastRowDefaultViewStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallRegular",
            style: labelTextStyle,
            tx: "flightDetails.baggage.baggageTracker"
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: trackYourBaggage,
            children: (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
              preset: "textLink",
              tx: "flightDetails.baggage.trackYourBaggage"
            }, (0, _utils.accessibility)({
              testID: `${testID}__BaggageTrackerButton`,
              accessibilityLabel: `${testID}__BaggageTrackerButton`,
              OS: _reactNative2.Platform.OS
            })))
          })]
        })
      });
      return (0, _utils.handleCondition)(baggageTracking, contentBaggage, null);
    }, [baggageTracking]);
    var baggageSection = function baggageSection() {
      var section = (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: lastRowDefaultViewStyle,
        children: [baggageStatusDisplay(), baggageTrackingButtonArrival()]
      });
      return (0, _utils.handleCondition)(flightState === _flightDetailsCard.FlightDetailsCardState.arrival, section, (0, _utils.handleCondition)(flightState === _flightDetailsCard.FlightDetailsCardState.departure, baggageTrackingButtonDeparture(), null));
    };
    var showFlightTime = function showFlightTime() {
      var mainTime = scheduledTime;
      var reTimeFlag = false;
      var numberDaysDiff = 0;
      if (displayTimestamp) {
        mainTime = displayTimestamp == null ? undefined : displayTimestamp.split(" ")[1];
        var mainDate = displayTimestamp == null ? undefined : displayTimestamp.split(" ")[0];
        if (scheduledDate !== mainDate || scheduledTime !== mainTime) {
          reTimeFlag = true;
          numberDaysDiff = (0, _moment.default)(mainDate).diff((0, _moment.default)(scheduledDate), "days");
        }
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: wrapShowFlightTime,
        children: [(0, _utils.handleCondition)(reTimeFlag, (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: oldTimeText,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: timeTextStyle,
            preset: "h3",
            children: getValue(scheduledTime, notAvailable)
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: redLineThroughText
          })]
        }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})), (0, _jsxRuntime.jsx)(_text.Text, {
          style: timeTextStyle,
          preset: "h3",
          children: getValue(mainTime, notAvailable)
        }), (0, _utils.handleCondition)(reTimeFlag && !!numberDaysDiff, (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption2Bold",
          style: textNumberDaysDiff,
          text: (0, _utils.handleCondition)(numberDaysDiff > 0, `(+${numberDaysDiff})`, `(${numberDaysDiff})`)
        }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {}))]
      });
    };
    var showFlightDate = function showFlightDate() {
      var flightScheduledDate = (0, _dateTime.toDate)(scheduledDate, _dateTime.DateFormats.DayDateMonthYear);
      if ((0, _utils.handleCondition)((statusMapping == null ? undefined : statusMapping.details_status_en) && ((statusMapping == null ? undefined : statusMapping.details_status_en.includes("Re-timed")) || (statusMapping == null ? undefined : statusMapping.details_status_en.includes("Delayed"))), true, false)) {
        var _displayTimestamp$spl;
        var date = (0, _utils.handleCondition)(displayTimestamp, displayTimestamp == null || (_displayTimestamp$spl = displayTimestamp.split(" ")) == null ? undefined : _displayTimestamp$spl[0], scheduledDate);
        flightScheduledDate = (0, _dateTime.toDate)(date, _dateTime.DateFormats.DayDateMonthYear);
      }
      return (0, _jsxRuntime.jsx)(_text.Text, {
        style: darkDateTextStyle,
        preset: "caption2Bold",
        children: flightScheduledDate
      });
    };
    var renderTerminalAndBaggageBelt = function renderTerminalAndBaggageBelt() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: thirdAndFourthSectionDefaultView,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: baggageLeftStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "XSmallRegular",
              style: labelStyle,
              text: terminalLabel
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: terminalDetailStyle,
              children: [isFlightDetailP1 ? (0, _utils.handleCondition)(terminal, (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextBold",
                style: almostBlackGreyColor,
                text: terminal
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: rowViewStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: darkestGreyColor,
                  text: "-"
                })
              })) : (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextBold",
                style: terminalNotAvailableStyle,
                children: getValue(terminal, notAvailable)
              }), disclaimerText]
            })]
          }), flightState === _flightDetailsCard.FlightDetailsCardState.departure ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              onLayout: function onLayout(event) {
                var layout = event.nativeEvent.layout;
                paddingComponentTerminal.value = layout.x;
              },
              style: baggageFlexStyle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "XSmallRegular",
                style: labelStyle,
                text: rowCheckIn
              }), (0, _utils.handleCondition)(checkInRow, (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: rowViewStyle,
                onPress: onPressCheckInRow,
                testID: `${testID}__TouchableCheckInRow`,
                accessibilityLabel: `${testID}__TouchableCheckInRow`,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: almostBlackGreyColor,
                  accessibilityLabel: checkInRow,
                  testID: `flightDetailCard_checkInRowValue`,
                  text: checkInRow
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: locationIconStyleActive
                })]
              }), isFlightDetailP1 ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: rowViewStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: darkestGreyColor,
                  text: "-"
                })
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: rowViewStyle,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: darkestGreyColor,
                  children: notAvailable
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: locationIconStyleInActive
                })]
              }))]
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "XSmallRegular",
                style: labelStyle,
                text: gateNumber
              }), (0, _utils.handleCondition)(showGate, (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: rowViewStyle,
                onPress: onPressGate,
                testID: `${testID}__TouchableGate`,
                accessibilityLabel: `${testID}__TouchableGate`,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: almostBlackGreyColor,
                  text: gate
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: locationIconStyleActive
                })]
              }), isFlightDetailP1 ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: rowViewStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: darkestGreyColor,
                  text: "-"
                })
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: rowViewStyle,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: gateTextStyle,
                  children: notAvailable
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: locationIconStyleInActive
                })]
              }))]
            })]
          }) : null, flightState === _flightDetailsCard.FlightDetailsCardState.arrival ? (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: baggageFlexStyle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "XSmallRegular",
                style: labelStyle,
                children: baggageLabel
              }), (0, _utils.handleCondition)(baggageBelt, (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: rowViewStyle,
                onPress: onPressBaggageBelt,
                testID: `${testID}__TouchableBaggage`,
                accessibilityLabel: `${testID}__TouchableBaggage`,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: almostBlackGreyColor,
                  children: baggageBelt
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: locationIconStyleActive
                })]
              }), isFlightDetailP1 ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: rowViewStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: darkestGreyColor,
                  text: "-"
                })
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: rowViewStyle,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: darkestGreyColor,
                  children: notAvailable
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 15,
                  height: 15,
                  style: locationIconStyleInActive
                })]
              }))]
            })
          }) : null]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: lasthorizontalDefaultViewLineStyle
        })]
      });
    };
    var renderPickupDropOffAndNearestCarPark = function renderPickupDropOffAndNearestCarPark() {
      if (!getIntoCityOrAirportPayload) {
        return null;
      }
      var _getIntoCityOrAirport = getIntoCityOrAirportPayload == null ? undefined : getIntoCityOrAirportPayload.getIntoCityOrAirport,
        text1 = _getIntoCityOrAirport.text1,
        text2 = _getIntoCityOrAirport.text2,
        text3 = _getIntoCityOrAirport.text3;
      if (flightState === _flightDetailsCard.FlightDetailsCardState.arrival) {
        return renderPickupDropOffAndNearestCarParkLayout({
          carpark: text2,
          pickupOrDropOff: text1,
          accessiblePickupOrDropOff: text3,
          type: 'pickup'
        });
      }
      if (flightState === _flightDetailsCard.FlightDetailsCardState.departure) {
        return renderPickupDropOffAndNearestCarParkLayout({
          carpark: text2,
          pickupOrDropOff: text1,
          accessiblePickupOrDropOff: text3,
          type: 'dropoff'
        });
      }
      return null;
    };
    var renderPickupDropOffAndNearestCarParkLayout = function renderPickupDropOffAndNearestCarParkLayout(_ref) {
      var carpark = _ref.carpark,
        pickupOrDropOff = _ref.pickupOrDropOff,
        accessiblePickupOrDropOff = _ref.accessiblePickupOrDropOff,
        type = _ref.type;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: thirdAndFourthSectionDefaultView,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: baggageLeftStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "XSmallRegular",
              style: labelStyle,
              tx: "flightDetails.nearestCarPark"
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: terminalDetailStyle,
              onPress: onPressNearestCarPark,
              disabled: !(carpark && carpark !== "N/A"),
              children: (0, _utils.handleCondition)(carpark && carpark !== "N/A", (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "caption1Regular",
                  style: nearestCarPackLabelStyle,
                  text: carpark
                }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                  width: 16,
                  height: 16,
                  style: [locationIconStyleActive, {
                    alignSelf: 'flex-start'
                  }]
                })]
              }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "caption1Regular",
                  style: darkestGreyColor,
                  text: "-"
                })
              }))
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: baggageFlexStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "XSmallRegular",
              style: labelStyle,
              tx: type == "pickup" ? "flightDetails.airportPickup" : "flightDetails.airportDropOff"
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: {
                rowGap: 4
              },
              children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: rowViewStyle,
                onPress: onPressPickupOrDropOff,
                disabled: !(pickupOrDropOff && pickupOrDropOff !== "N/A"),
                children: [(0, _jsxRuntime.jsx)(_icons.FlightDropOff, {
                  width: 14,
                  height: 14,
                  style: pickupIconStyleInActive
                }), (0, _utils.handleCondition)(pickupOrDropOff && pickupOrDropOff !== "N/A", (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "caption1Regular",
                    style: darkestGreyColor,
                    text: pickupOrDropOff
                  }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                    width: 16,
                    height: 16,
                    style: [locationIconStyleActive, {
                      alignSelf: 'flex-start'
                    }]
                  })]
                }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "caption1Regular",
                    style: darkestGreyColor,
                    text: "-"
                  })
                }))]
              }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: rowViewStyle,
                onPress: onPressAccessiblePickupOrDropOff,
                disabled: !(accessiblePickupOrDropOff && accessiblePickupOrDropOff !== "N/A"),
                children: [(0, _jsxRuntime.jsx)(_icons.FlightToilet, {
                  width: 14,
                  height: 14,
                  style: pickupIconStyleInActive
                }), (0, _utils.handleCondition)(accessiblePickupOrDropOff && accessiblePickupOrDropOff !== "N/A", (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "caption1Regular",
                    style: darkestGreyColor,
                    text: accessiblePickupOrDropOff
                  }), (0, _jsxRuntime.jsx)(_location_filled.default, {
                    width: 16,
                    height: 16,
                    style: [locationIconStyleActive, {
                      alignSelf: 'flex-start'
                    }]
                  })]
                }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "caption1Regular",
                    style: darkestGreyColor,
                    text: "-"
                  })
                }))]
              })]
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: lasthorizontalDefaultViewLineStyle
        })]
      });
    };
    var handleEarlyCheckinV2 = function handleEarlyCheckinV2() {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: loadingData && !isCalled ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: {
            alignItems: 'flex-start'
          },
          children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
            size: "small",
            color: _theme.color.palette.lightPurple
          })
        }) : (0, _jsxRuntime.jsx)(_flightDetailEarlyCheckinV.FlightEarlyCheckInV2, {
          data: dataEarlyCheckinV2,
          sendEventTracking: function sendEventTracking(link) {
            return onPressFlightCardLinks(link);
          },
          trackingForFlightBottomDetails: function trackingForFlightBottomDetails() {
            logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.EARLY_CHECK_IN);
          }
        })
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: innerContainerStyle,
      children: [(0, _utils.handleCondition)(removeFlightLoading, (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: dotsLoadingStyle,
        children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          source: _$$_REQUIRE(_dependencyMap[40]),
          autoPlay: true,
          loop: true,
          style: {
            width: '100%',
            height: '100%'
          }
        })
      }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: firstRowStyle,
        children: [(0, _utils.handleCondition)(!(0, _lodash.isEmpty)(statusMapping == null ? undefined : statusMapping.details_status_en), flightStatusTag(statusMapping), null), (0, _utils.handleCondition)(isFlightSaved && !removeFlightLoading && !isMSError, (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: rightColumnStyle,
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            style: buttonStyle,
            onPress: onButtonPressed,
            sizePreset: "small",
            backgroundPreset: "light",
            textPreset: "buttonSmall",
            text: unSave
          })
        }), null)]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: rowFlex,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: flexStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: labelTextStyle,
            preset: "XSmallRegular",
            children: (0, _utils.handleCondition)(direction === "DEP", departureDirectionText, arrivalDirectionText)
          }), (0, _utils.handleCondition)(direction === "DEP", (0, _jsxRuntime.jsx)(_icons.FlightDepartureIconActive, {}), (0, _jsxRuntime.jsx)(_icons.FlightArrivalIconActive, {}))]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: flexStyle,
          children: [showFlightDate(), showFlightTime()]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: horizontalDefaultViewLineStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: thirdAndFourthSectionDefaultView,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: rowViewStyle,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: wrapImageStyle,
            children: (0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: airlineDetails == null ? undefined : airlineDetails.logo_url
              },
              style: imageStyle
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: flightNumberText,
            preset: "caption1Bold",
            children: flightNumber
          }), (0, _utils.handleCondition)((slaves == null ? undefined : slaves.length) > 0, (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: verticalLineStyle
          }), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {}))]
        }), renderSlaves(slaves)]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: horizontalDefaultViewLineStyle
      }), (0, _utils.handleCondition)(flightState === _flightDetailsCard.FlightDetailsCardState.departure && (airlineDetails == null ? undefined : airlineDetails.eligible_fast_checkin) === true, (0, _jsxRuntime.jsx)(_flightFastCheckin.FlightFastCheckin, {}), null), renderTerminalAndBaggageBelt(), isFlightDetailP1 && renderPickupDropOffAndNearestCarPark(), !isFlightDetailP1 && flightState === _flightDetailsCard.FlightDetailsCardState.departure ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: lastRowDefaultViewStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
          style: animatedStyles,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallRegular",
            style: labelTextStyle,
            children: onlineCheckInLabel
          }), (0, _utils.handleCondition)(isShowOnlineCheckIn && (onlineCheckIn == null || (_onlineCheckIn$link = onlineCheckIn.link) == null ? undefined : _onlineCheckIn$link.length) > 0, (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              onPressFlightCardLinks(onlineCheckIn == null ? undefined : onlineCheckIn.linkText);
              saveFlightWhenCheckInOnline(isFlightSaved);
              logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.ONLINE_CHECK_IN);
            },
            testID: `${testID}__TouchableLinkText1`,
            accessibilityLabel: `${testID}__TouchableLinkText1`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "textLink",
              style: textLinkStyle,
              children: onlineCheckIn == null ? undefined : onlineCheckIn.linkText
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: almostBlackGreyColor,
              preset: "textLink",
              children: notAvailable
            })
          }))]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: flexStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallRegular",
            style: labelTextStyle,
            children: earlyCheckInLabel
          }), !enableEciDynamicDisplay ? (0, _utils.handleCondition)(isShowEarlyCheckIn && (earlyCheckIn == null || (_earlyCheckIn$link = earlyCheckIn.link) == null ? undefined : _earlyCheckIn$link.length) > 0, (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              onPressFlightCardLinks(earlyCheckIn == null ? undefined : earlyCheckIn.linkText);
              navigation == null || navigation.navigate(_constants.NavigationConstants.webview, {
                uri: earlyCheckIn == null ? undefined : earlyCheckIn.link
              });
              logClickEvent(_firebase.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.EARLY_CHECK_IN);
            },
            testID: `${testID}__TouchableLinkText2`,
            accessibilityLabel: `${testID}__TouchableLinkText2`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "textLink",
              style: textLinkStyle,
              children: earlyCheckIn == null ? undefined : earlyCheckIn.linkText
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: almostBlackGreyColor,
              preset: "textLink",
              children: notAvailable
            })
          })) : handleEarlyCheckinV2()]
        })]
      }) : null, !isFlightDetailP1 && baggageSection(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: timeStampStyle,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption2Regular",
          text: timeStamp
        })
      })]
    });
  };
  var FlightDetailsCardV2 = exports.FlightDetailsCardV2 = function FlightDetailsCardV2(props) {
    var isLoading = props.type === _flightDetailsCard.FlightDetailsCardType.loading;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _utils.handleCondition)(isLoading, (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: outerLoadingViewContainer,
        children: loadingView()
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [outerContainer, {
          width: props.isFlightDetailP1 ? width - _responsive.default.getFontSize(32) : width - _responsive.default.getFontSize(48)
        }],
        children: defaultView(props)
      }))
    });
  };
