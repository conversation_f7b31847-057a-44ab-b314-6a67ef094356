  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    buttonStyle: {
      borderRadius: 60,
      marginBottom: 10,
      width: "100%"
    },
    containerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopEndRadius: 16,
      borderTopLeftRadius: 16,
      paddingHorizontal: 24
    },
    disableCloseButtonStyles: {
      alignSelf: "flex-end",
      height: 24
    },
    dismissIconContainer: {
      alignSelf: "flex-end",
      marginTop: 15
    },
    iconConfig: {
      height: 70,
      width: 70
    },
    iconStyle: {
      alignItems: "center",
      borderRadius: 50,
      height: 70,
      justifyContent: "center",
      position: "absolute",
      top: -35,
      width: 70
    },
    imageStyle: {
      height: 75,
      width: 75
    },
    messageStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    modalStyle: {
      justifyContent: "flex-end",
      margin: 0
    },
    textContainerStyle: {
      marginVertical: 30,
      paddingHorizontal: 10
    },
    titleStyle: Object.assign({}, _text.presets.h2, {
      lineHeight: 28,
      marginBottom: 16,
      textAlign: "center"
    })
  });
