  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightStatus = exports.FlightDetailsCardType = exports.FlightDetailsCardState = undefined;
  var FlightDetailsCardType = exports.FlightDetailsCardType = /*#__PURE__*/function (FlightDetailsCardType) {
    FlightDetailsCardType["loading"] = "loading";
    FlightDetailsCardType["default"] = "default";
    return FlightDetailsCardType;
  }({});
  var FlightDetailsCardState = exports.FlightDetailsCardState = /*#__PURE__*/function (FlightDetailsCardState) {
    FlightDetailsCardState["arrival"] = "arrival";
    FlightDetailsCardState["departure"] = "departure";
    return FlightDetailsCardState;
  }({});
  var FlightStatus = exports.FlightStatus = /*#__PURE__*/function (FlightStatus) {
    FlightStatus["Cancelled"] = "cancelled";
    FlightStatus["Delayed"] = "delayed";
    FlightStatus["Landed"] = "landed";
    FlightStatus["Departed"] = "departed";
    return FlightStatus;
  }({});
