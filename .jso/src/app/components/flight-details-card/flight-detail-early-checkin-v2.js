  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightEarlyCheckInV2 = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _text2 = _$$_REQUIRE(_dependencyMap[6]);
  var _bottomSheetEarlyCheckIn = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _utils = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FlightEarlyCheckInV2 = exports.FlightEarlyCheckInV2 = _react.default.memo(function (props) {
    var _dataValue$linkText;
    var navigation = (0, _native.useNavigation)();
    var data = props.data,
      sendEventTracking = props.sendEventTracking,
      trackingForFlightBottomDetails = props.trackingForFlightBottomDetails;
    var dataValue = data == null ? undefined : data.getEarlyCheckin_v2;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showBottomSheet = _useState2[0],
      setShowBottomSheet = _useState2[1];
    var closeBottomSheet = (0, _react.useCallback)(function () {
      setShowBottomSheet(false);
    }, []);
    var onPressEarlyCheckin = function onPressEarlyCheckin() {
      if (dataValue != null && dataValue.openECIPopup) {
        setShowBottomSheet(true);
      } else {
        sendEventTracking(dataValue == null ? undefined : dataValue.linkText);
        navigation == null || navigation.navigate(_constants.NavigationConstants.webview, {
          uri: dataValue == null ? undefined : dataValue.link
        });
      }
      trackingForFlightBottomDetails == null || trackingForFlightBottomDetails(dataValue == null ? undefined : dataValue.linkText);
    };
    var renderStyle = (0, _react.useCallback)(function () {
      if (!(dataValue != null && dataValue.linkEnable)) {
        return styles.textLinkStyleGray;
      } else {
        return styles.textLinkStyle;
      }
    }, [dataValue == null ? undefined : dataValue.linkEnable]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        onPress: onPressEarlyCheckin,
        disabled: !(dataValue != null && dataValue.linkEnable),
        children: [(0, _utils.handleCondition)((dataValue == null || (_dataValue$linkText = dataValue.linkText) == null ? undefined : _dataValue$linkText.length) > 0, (0, _jsxRuntime.jsx)(_text.Text, {
          style: renderStyle(),
          children: dataValue == null ? undefined : dataValue.linkText
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.textNA,
          tx: "common.notAvailable"
        })), (dataValue == null ? undefined : dataValue.earliestEligibleECITime) && (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.txtDateTime,
          children: dataValue == null ? undefined : dataValue.earliestEligibleECITime
        })]
      }), (0, _jsxRuntime.jsx)(_bottomSheetEarlyCheckIn.default, {
        visible: showBottomSheet,
        onClosedSheet: closeBottomSheet,
        data: dataValue == null ? undefined : dataValue.ECILounge,
        date: dataValue == null ? undefined : dataValue.eligibleECIDuration
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    textLinkStyle: Object.assign({}, _text2.newPresets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    textLinkStyleGray: Object.assign({}, _text2.newPresets.caption1Bold, {
      color: _theme.color.palette.midGrey
    }),
    textNA: Object.assign({}, _text2.newPresets.caption1Bold, {
      color: _theme.color.palette.midGrey
    }),
    txtDateTime: Object.assign({}, _text2.newPresets.caption2Regular, {
      color: _theme.color.palette.darkestGrey,
      marginTop: 2
    })
  });
