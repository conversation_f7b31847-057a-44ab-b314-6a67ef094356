  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BOARDING_PASS_ACCEPTED_SCAN_TYPE = undefined;
  Object.defineProperty(exports, "CodeType", {
    enumerable: true,
    get: function get() {
      return _reactNativeVisionCamera.CodeType;
    }
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _analytics = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeVisionCamera = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get('screen'),
    screenWidth = _Dimensions$get.width;
  var BOARDING_PASS_ACCEPTED_SCAN_TYPE = exports.BOARDING_PASS_ACCEPTED_SCAN_TYPE = ['pdf-417', 'aztec', 'data-matrix', 'qr'];

  // Type for MyCamera ref

  var MyCamera = function MyCamera(props, ref) {
    var _props$photo = props.photo,
      isPhoto = _props$photo === undefined ? true : _props$photo,
      _props$video = props.video,
      isVideo = _props$video === undefined ? false : _props$video,
      photoRatio = props.photoRatio,
      _props$flashMode = props.flashMode,
      flashMode = _props$flashMode === undefined ? false : _props$flashMode,
      _props$isActive = props.isActive,
      isActive = _props$isActive === undefined ? true : _props$isActive,
      _props$isCodeScanned = props.isCodeScanned,
      isCodeScanned = _props$isCodeScanned === undefined ? false : _props$isCodeScanned,
      _props$onCodeScanned = props.onCodeScanned,
      onCodeScanned = _props$onCodeScanned === undefined ? function () {} : _props$onCodeScanned,
      _props$onCodeScannedT = props.onCodeScannedTypes,
      onCodeScannedTypes = _props$onCodeScannedT === undefined ? BOARDING_PASS_ACCEPTED_SCAN_TYPE : _props$onCodeScannedT,
      onStarted = props.onStarted;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var SCREEN_HEIGHT = _reactNative.Platform.select({
      android: _reactNative.Dimensions.get('screen').height - insets.bottom,
      ios: _reactNative.Dimensions.get('window').height
    });
    var defaultPhotoRatio = SCREEN_HEIGHT / screenWidth;
    var cameraRef = (0, _react.useRef)(null);
    var _React$useState = _react.default.useState(false),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      isCameraReady = _React$useState2[0],
      setCameraReady = _React$useState2[1];
    var _React$useState3 = _react.default.useState(0),
      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
      cameraKey = _React$useState4[0],
      setCameraKey = _React$useState4[1];
    var _useCameraPermission = (0, _reactNativeVisionCamera.useCameraPermission)(),
      hasPermission = _useCameraPermission.hasPermission,
      requestPermission = _useCameraPermission.requestPermission;
    var device = (0, _reactNativeVisionCamera.useCameraDevice)("back");
    var format = (0, _reactNativeVisionCamera.useCameraFormat)(device, [{
      photoAspectRatio: photoRatio ? photoRatio : defaultPhotoRatio
    }]);
    var codeScanner = (0, _reactNativeVisionCamera.useCodeScanner)({
      codeTypes: onCodeScannedTypes,
      onCodeScanned: onCodeScanned
    });
    var onInitialized = function onInitialized() {
      setCameraReady(true);
    };
    var onError = function onError(error) {
      var dtAction = (0, _analytics.dtManualActionEvent)(`${_analytics.FE_LOG_PREFIX}Camera_error`);
      dtAction.reportStringValue('code', `${error.code}`);
      dtAction.reportStringValue('message', `${(0, _analytics.convertStringValue)(error == null ? undefined : error.message)}`);
      dtAction.leaveAction();
      console.error("MyCamera camera error", {
        code: error.code,
        message: error == null ? undefined : error.message
      });
    };
    var takePicture = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _cameraRef$current;
        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
        var image = yield cameraRef == null || (_cameraRef$current = cameraRef.current) == null || _cameraRef$current.takePhoto == null ? undefined : _cameraRef$current.takePhoto(Object.assign({
          flash: flashMode ? "on" : "off"
        }, options));
        setCameraKey(function (prev) {
          return prev + 1;
        });
        return image;
      });
      return function takePicture() {
        return _ref.apply(this, arguments);
      };
    }();

    // Expose functions to parent
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        takePicture: takePicture,
        requestPermission: requestPermission,
        hasPermission: hasPermission,
        isCameraReady: isCameraReady
      };
    });
    return !!device ? (0, _jsxRuntime.jsx)(_reactNativeVisionCamera.Camera, {
      ref: cameraRef,
      format: format,
      isActive: isActive,
      device: device,
      photo: isPhoto,
      video: isVideo,
      outputOrientation: "preview",
      style: _reactNative.StyleSheet.absoluteFill,
      torch: flashMode ? "on" : "off",
      onError: onError,
      onInitialized: onInitialized,
      codeScanner: !!isCodeScanned ? codeScanner : null,
      onStarted: onStarted
    }, cameraKey) : (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
  };
  var _default = exports.default = _react.default.forwardRef(MyCamera);
