  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ProductOffer = ProductOffer;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var React = _react;
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _ribbon = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _text2 = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _productOffer = _$$_REQUIRE(_dependencyMap[11]);
  var _utils = _$$_REQUIRE(_dependencyMap[12]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var containerStyle = Object.assign({}, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 2,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      }
    },
    android: {
      elevation: 3
    }
  }), {
    borderRadius: 16,
    width: 156,
    backgroundColor: _theme.color.palette.whiteGrey,
    alignSelf: "center",
    flex: 1
  });
  var imageStyle = {
    height: 150,
    width: 156,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16
  };
  var productNameStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var salePriceStyle = Object.assign({}, _text2.presets.subTitleBold, {
    color: _theme.color.palette.almostBlackGrey,
    marginTop: 4
  });
  var originalPriceStyle = Object.assign({}, _text2.presets.caption1Regular, {
    marginTop: 4,
    textDecorationLine: "line-through"
  });
  var textContainerStyle = {
    paddingRight: 12,
    paddingLeft: 12,
    paddingTop: 12,
    height: 109
  };
  var ribbonStyle = {
    position: "absolute",
    top: 8,
    left: 8
  };
  var touchableOpacityStyle = {
    flex: 1
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayout = [{
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 41,
    height: 13,
    borderRadius: 4
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 115,
    height: 13,
    marginTop: 14,
    borderRadius: 4
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 56,
    height: 13,
    marginTop: 13,
    borderRadius: 4
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 56,
    height: 13,
    marginTop: 13,
    borderRadius: 4
  }];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: imageStyle
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: textContainerStyle,
        children: skeletonLayout.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: item
            })
          }, index);
        })
      })]
    });
  };
  function ProductOffer(props) {
    var imageUrl = props.imageUrl,
      productName = props.productName,
      ribbonText = props.ribbonText,
      tenantName = props.tenantName,
      originalPrice = props.originalPrice,
      salePrice = props.salePrice,
      type = props.type,
      onPressed = props.onPressed;
    var isLoading = _productOffer.ProductOfferType.loading === type;
    var containerHeight = (0, _utils.simpleCondition)({
      condition: tenantName || isLoading,
      ifValue: 285,
      elseValue: 262
    });
    var originalPriceText = originalPrice;
    var salePriceText = salePrice;
    var isDiscountAvailable = ribbonText && ribbonText.length > 0 || false;
    var imageSource = imageUrl ? {
      uri: imageUrl.toString()
    } : _$$_REQUIRE(_dependencyMap[15]);
    var _React$useState = React.useState(false),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      marginBottomCheck = _React$useState2[0],
      setMarginBottomCheck = _React$useState2[1];
    var onTextLayout = (0, _react.useCallback)(function (e) {
      if (e.nativeEvent.lines.length >= 2) {
        setMarginBottomCheck(true);
      }
    }, []);
    var originalPricePaddingBottomStyle = Object.assign({}, originalPriceStyle, {
      marginBottom: marginBottomCheck ? 13 : 33
    });
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: !isLoading ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: containerStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: touchableOpacityStyle,
          onPress: onPressed,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            style: imageStyle,
            source: imageSource
          }), isDiscountAvailable && (0, _jsxRuntime.jsx)(_ribbon.Ribbon, {
            text: ribbonText,
            preset: "absolute",
            containerStyle: ribbonStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: textContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: productName,
              preset: "caption1Regular",
              numberOfLines: 2,
              style: productNameStyle,
              onTextLayout: onTextLayout
            }), (0, _utils.handleCondition)(salePrice, (0, _jsxRuntime.jsx)(_text.Text, {
              text: salePriceText,
              preset: "subTitleBold",
              style: salePriceStyle
            }), null), (0, _jsxRuntime.jsx)(_text.Text, {
              text: originalPriceText,
              style: (0, _utils.handleCondition)(salePrice, originalPricePaddingBottomStyle, salePriceStyle)
            })]
          })]
        })
      }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: Object.assign({}, containerStyle, {
          height: containerHeight
        }),
        children: loadingView()
      })
    });
  }
