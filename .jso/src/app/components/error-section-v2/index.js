  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _errorSectionV = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_errorSectionV).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _errorSectionV[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _errorSectionV[key];
      }
    });
  });
