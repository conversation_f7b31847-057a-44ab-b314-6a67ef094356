  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.reloadButtonColors = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: 24
    },
    textContainerStyle: {
      marginBottom: 24,
      width: "100%"
    },
    titleTextStyle: Object.assign({}, _text.presets.caption2BlackBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 16,
      textAlign: "center"
    }),
    messageTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center",
      alignSelf: "center",
      width: "100%"
    }),
    reloadButtonStyle: {
      borderRadius: 60,
      paddingHorizontal: 24
    },
    defaultIconStyle: {
      marginBottom: 16
    }
  });
  var reloadButtonColors = exports.reloadButtonColors = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
