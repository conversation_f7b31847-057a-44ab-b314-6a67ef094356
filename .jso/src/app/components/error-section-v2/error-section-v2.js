  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorSectionV2 = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _errorSectionV = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _utils = _$$_REQUIRE(_dependencyMap[11]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var ErrorSectionV2 = exports.ErrorSectionV2 = function ErrorSectionV2(props) {
    var _props$reload = props.reload,
      reload = _props$reload === undefined ? true : _props$reload,
      onReload = props.onReload,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ErrorSectionV2" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ErrorSectionV2" : _props$accessibilityL,
      textContainerStyle = props.textContainerStyle,
      containerStyle = props.containerStyle,
      reloadButtonStyleOverride = props.reloadButtonStyleOverride,
      reloadButtonTextStyleOverride = props.reloadButtonTextStyleOverride,
      reloadButtonInnerStyleOverride = props.reloadButtonInnerStyleOverride,
      _props$title = props.title,
      title = _props$title === undefined ? (0, _i18n.translate)("errorOverlay.variant3.title") : _props$title,
      _props$message = props.message,
      message = _props$message === undefined ? (0, _i18n.translate)("errorOverlay.variant3.message") : _props$message,
      _props$reloadText = props.reloadText,
      reloadText = _props$reloadText === undefined ? (0, _i18n.translate)("errorOverlay.variant3.retry") : _props$reloadText,
      extendCode = props.extendCode,
      _props$iconSize = props.iconSize,
      iconSize = _props$iconSize === undefined ? 120 : _props$iconSize;
    var handlePressReload = function handlePressReload() {
      onReload == null || onReload();
    };
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var matchedAem = extendCode && errorData != null && errorData.length ? errorData.find(function (el) {
      return el.code === extendCode;
    }) : null;
    var finalTitle = (matchedAem == null ? undefined : matchedAem.header) || title;
    var finalMessage = (matchedAem == null ? undefined : matchedAem.subHeader) || message;
    var finalReloadText = (matchedAem == null ? undefined : matchedAem.buttonLabel) || reloadText;
    var aemIconUri = (0, _utils.mappingUrlAem)(matchedAem == null ? undefined : matchedAem.icon);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [_errorSectionV.styles.containerStyle, containerStyle],
      testID: testID,
      accessibilityLabel: accessibilityLabel,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _errorSectionV.styles.defaultIconStyle,
        children: matchedAem != null && matchedAem.icon ? (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: aemIconUri
          },
          style: {
            width: iconSize,
            height: iconSize
          },
          resizeMode: "contain"
        }) : (0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {
          width: iconSize,
          height: iconSize
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [_errorSectionV.styles.textContainerStyle, textContainerStyle],
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _errorSectionV.styles.titleTextStyle,
          text: finalTitle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _errorSectionV.styles.messageTextStyle,
          text: finalMessage
        })]
      }), reload && (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: [_errorSectionV.styles.reloadButtonStyle, reloadButtonStyleOverride],
        start: {
          x: 0,
          y: 1
        },
        end: {
          x: 1,
          y: 0
        },
        colors: _errorSectionV.reloadButtonColors,
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          onPress: handlePressReload,
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "primary",
          text: finalReloadText,
          backgroundPreset: "light",
          statePreset: "default",
          textStyle: reloadButtonTextStyleOverride,
          style: reloadButtonInnerStyleOverride,
          testID: `${testID}__ButtonReload`,
          accessibilityLabel: `${accessibilityLabel}__ButtonReload`
        })
      })]
    });
  };
