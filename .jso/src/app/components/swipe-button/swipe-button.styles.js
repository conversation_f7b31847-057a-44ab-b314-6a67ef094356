  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.buttonTitleStyle = exports.buttonTitleDisabledStyle = exports.buttonGradientStyle = exports.buttonGradientDisabledColors = exports.buttonGradientColors = exports.buttonContainerStyle = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var buttonGradientStyle = exports.buttonGradientStyle = {
    justifyContent: "center",
    borderRadius: 60,
    borderWidth: 0,
    borderColor: "transparent"
  };
  var buttonGradientColors = exports.buttonGradientColors = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var buttonGradientDisabledColors = exports.buttonGradientDisabledColors = [_theme.color.palette.lightGrey, _theme.color.palette.lightGrey];
  var buttonContainerStyle = exports.buttonContainerStyle = {
    margin: 0,
    padding: 0,
    borderWidth: 0
    // height: 46,
  };
  var buttonTitleStyle = exports.buttonTitleStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.almostWhiteGrey,
    textAlign: "center"
  });
  var buttonTitleDisabledStyle = exports.buttonTitleDisabledStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.darkGrey,
    textAlign: "center"
  });
