  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _swipeButton = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_swipeButton).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _swipeButton[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _swipeButton[key];
      }
    });
  });
  var _swipeButton2 = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_swipeButton2).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _swipeButton2[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _swipeButton2[key];
      }
    });
  });
