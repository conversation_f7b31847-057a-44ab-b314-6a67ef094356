  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SwipeButton = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _rnSwipeButton = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SwipeButton = exports.SwipeButton = function SwipeButton(props) {
    var Icon = function Icon() {
      return props.disabled ? (0, _jsxRuntime.jsx)(_icons.SwipeArrowDisabled, {}) : (0, _jsxRuntime.jsx)(_icons.SwipeArrow, {});
    };
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      swiping = _useState2[0],
      setSwiping = _useState2[1];
    var setStartSwiping = function setStartSwiping() {
      setSwiping(true);
    };
    var setEndSwiping = function setEndSwiping() {
      setSwiping(false);
    };
    return (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
      start: {
        x: 0,
        y: 1
      },
      end: {
        x: 1,
        y: 0
      },
      style: styles.buttonGradientStyle,
      colors: props.disabled ? styles.buttonGradientDisabledColors : styles.buttonGradientColors,
      children: (0, _jsxRuntime.jsx)(_rnSwipeButton.default, Object.assign({
        containerStyles: styles.buttonContainerStyle,
        titleStyles: props.disabled ? styles.buttonTitleDisabledStyle : styles.buttonTitleStyle,
        swipeSuccessThreshold: 70,
        railBorderColor: "transparent",
        railBackgroundColor: "transparent",
        disabledRailBackgroundColor: "transparent",
        thumbIconBorderColor: "transparent",
        disabledThumbIconBorderColor: "transparent",
        thumbIconBackgroundColor: "transparent",
        disabledThumbIconBackgroundColor: "transparent",
        railFillBorderColor: swiping ? "#AB76D5" : "transparent",
        railFillBackgroundColor: swiping ? "#AB76D5" : "transparent",
        thumbIconComponent: Icon,
        onSwipeStart: setStartSwiping,
        onSwipeSuccess: setEndSwiping,
        onSwipeFail: setEndSwiping,
        height: 44,
        disableResetOnTap: true
      }, props, {
        testID: props != null && props.testID ? props == null ? undefined : props.testID : "SwipeButton",
        accessibilityLabel: props != null && props.accessibilityLabel ? props == null ? undefined : props.accessibilityLabel : "SwipeButton",
        shouldResetAfterSuccess: true
      }))
    });
  };
