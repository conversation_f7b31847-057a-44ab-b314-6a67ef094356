  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _marketPlaceStyles = _$$_REQUIRE(_dependencyMap[6]);
  var _marketPlaceType = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayoutImage = [{
    height: 60,
    width: 60,
    borderRadius: 30
  }, {
    height: 17,
    width: "80%",
    borderRadius: 4
  }, {
    height: 17,
    width: "100%",
    borderRadius: 4,
    marginBottom: 12,
    marginTop: 2
  }, {
    height: 17,
    width: "82%",
    borderRadius: 4
  }];
  var MarketPlaceItemDefault = function MarketPlaceItemDefault(props) {
    var icon = props.icon,
      title = props.title,
      description = props.description,
      onItemPress = props.onItemPress,
      testID = props.testID;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: _marketPlaceStyles.styles.container,
      onPress: function onPress() {
        return onItemPress(props);
      },
      testID: `${testID}__MarketPlaceCard`,
      accessibilityLabel: `${testID}__MarketPlaceCard`,
      accessible: false,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _marketPlaceStyles.styles.headerItem,
        accessible: false,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          style: _marketPlaceStyles.styles.iconStyles,
          source: {
            uri: (0, _mediaHelper.handleImageUrl)(icon)
          },
          testID: `${testID}__MarketPlaceCard__Icon`,
          accessibilityLabel: (0, _mediaHelper.handleImageUrl)(icon)
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _marketPlaceStyles.styles.titleContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _marketPlaceStyles.styles.titleStyles,
            text: title,
            numberOfLines: 2,
            testID: `${testID}__MarketPlaceCard__Title`,
            accessibilityLabel: title
          })
        }), (0, _jsxRuntime.jsx)(_icons.ArrowRight, {
          width: 24,
          height: 24
        })]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: description,
        style: _marketPlaceStyles.styles.descriptionStyles,
        testID: `${testID}__MarketPlaceCard__Description`,
        accessibilityLabel: description
      })]
    });
  };
  var MarketPlaceItemLoading = function MarketPlaceItemLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _marketPlaceStyles.styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _marketPlaceStyles.styles.headerItem,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[0]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _marketPlaceStyles.styles.titleContainer,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayoutImage[1]
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _marketPlaceStyles.styles.descriptionStyles,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[3]
        })]
      })]
    });
  };
  var MarketPlaceItem = function MarketPlaceItem(props) {
    return props.type === _marketPlaceType.MarketPlaceType.loading ? (0, _jsxRuntime.jsx)(MarketPlaceItemLoading, {}) : (0, _jsxRuntime.jsx)(MarketPlaceItemDefault, Object.assign({}, props));
  };
  var _default = exports.default = MarketPlaceItem;
