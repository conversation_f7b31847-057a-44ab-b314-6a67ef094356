  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      marginHorizontal: 24,
      padding: 24
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 16,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    descriptionStyles: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      marginTop: 8
    }),
    headerItem: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center"
    },
    iconStyles: {
      borderRadius: 30,
      height: 60,
      width: 60
    },
    titleContainer: {
      flex: 1,
      marginHorizontal: 10
    },
    titleStyles: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
