  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var FacilitiesAndServiceHorizontal = function FacilitiesAndServiceHorizontal(props) {
    var _useState = (0, _react.useState)(1),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      opacity = _useState2[0],
      setOpacity = _useState2[1];
    var imageUrl = props.imageUrl,
      title = props.title,
      locationDisplayText = props.locationDisplayText,
      onPressed = props.onPressed,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "AttractionsFacilitiesServices" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "AttractionsFacilitiesServices" : _props$accessibilityL;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.parentContainer,
      children: (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: onPressed,
        onPressIn: function onPressIn() {
          return setOpacity(0.2);
        },
        onPressOut: function onPressOut() {
          return setOpacity(1);
        },
        testID: `${testID}__TouchableWithoutFeedback`,
        accessibilityLabel: `${accessibilityLabel}__TouchableWithoutFeedback`,
        accessible: false,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.container,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: {
              opacity: opacity
            },
            children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
              resizeMode: "cover",
              source: {
                uri: imageUrl
              },
              style: styles.imageBackground
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.bottomContainer,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                numberOfLines: 2,
                text: title,
                preset: "bodyTextBold",
                style: styles.locationTextTwoRow
              }), locationDisplayText ? (0, _jsxRuntime.jsx)(_text.Text, {
                text: locationDisplayText,
                numberOfLines: 1,
                style: styles.locationTextStyle,
                preset: "caption1Regular"
              }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.locationViewStyle
              })]
            })]
          })
        })
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    bottomContainer: {
      height: 86,
      padding: 12
    },
    container: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      elevation: 5
    }, _reactNative2.Platform.select({
      ios: {
        shadowRadius: 16,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    imageBackground: {
      alignSelf: "center",
      borderRadius: 12,
      height: 80,
      marginTop: -40,
      width: width / 2 - 58
    },
    locationTextStyle: {
      marginTop: 4
    },
    locationTextTwoRow: {
      color: _theme.color.palette.almostBlackGrey
    },
    locationViewStyle: {
      marginTop: 4
    },
    parentContainer: {
      marginBottom: 16,
      marginTop: 41,
      width: width / 2 - 32
    }
  });
  var _default = exports.default = FacilitiesAndServiceHorizontal;
