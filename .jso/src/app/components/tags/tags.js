  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Tags = Tags;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _ramda = _$$_REQUIRE(_dependencyMap[2]);
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _tag = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _tags = _$$_REQUIRE(_dependencyMap[7]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var tagItemStyle = {
    marginRight: 12
  };
  var verticalStyle = Object.assign({}, tagItemStyle, {
    marginBottom: 12
  });
  var skeletonImageStyle = {
    flexDirection: "row"
  };
  var flatListContainerStyle = {
    width: "100%",
    flexWrap: "wrap"
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var skeletonLayoutImage = [{
    height: 28,
    width: 86,
    borderRadius: 8,
    marginRight: 12
  }, {
    height: 28,
    width: 86,
    borderRadius: 8,
    marginRight: 12
  }, {
    height: 28,
    width: 86,
    borderRadius: 8,
    marginRight: 12
  }];
  var renderTagView = function renderTagView(item, index, isHorizontal) {
    var style = isHorizontal ? tagItemStyle : verticalStyle;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: style,
      children: (0, _jsxRuntime.jsx)(_tag.Tag, {
        text: item
      })
    }, index);
  };
  var loadingView = function loadingView(props) {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: props.contentContainerStyle,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: skeletonImageStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[2]
        })]
      })
    });
  };
  var defaultView = function defaultView(dataSource, props) {
    var isHorizontal = _tags.TagsOrientation.horizontal === props.orientation;
    var style = (0, _ramda.mergeAll)((0, _ramda.flatten)([!isHorizontal && flatListContainerStyle, props.contentContainerStyle]));
    return (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      contentContainerStyle: style,
      data: dataSource,
      scrollEnabled: isHorizontal,
      renderItem: function renderItem(_ref) {
        var item = _ref.item,
          index = _ref.index;
        return renderTagView(item, index, isHorizontal);
      },
      horizontal: true,
      showsHorizontalScrollIndicator: false,
      keyExtractor: function keyExtractor(index) {
        return index.toString();
      }
    });
  };
  function Tags(props) {
    var tagsData = props.tagsData;
    var _React$useState = React.useState([]),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      dataSource = _React$useState2[0],
      setDataSource = _React$useState2[1];
    var isLoading = _tags.TagsType.loading === props.type;
    React.useEffect(function () {
      setDataSource(tagsData);
    }, [tagsData]);
    return isLoading ? loadingView(props) : defaultView(dataSource, props);
  }
