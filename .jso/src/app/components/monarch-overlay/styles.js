  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var SEPARATOR_ITEM_WIDTH = 11;
  var HORIZONTAL_PADDING = 24;
  var CARD_WIDTH = (width - 70) / 3;
  var IMAGE_CARD_WIDTH = CARD_WIDTH - 16;
  var styles = _reactNative.StyleSheet.create({
    logo: {
      width: 88,
      height: 88,
      alignSelf: 'center',
      marginTop: 120
    },
    welcomeTitle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.whiteGrey,
      marginTop: 34,
      alignSelf: 'center'
    }),
    subcopy: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      marginTop: 8,
      alignSelf: 'center',
      paddingHorizontal: HORIZONTAL_PADDING,
      textAlign: 'center'
    }),
    wrapPrivilegeSections: {
      marginTop: 32,
      alignItems: 'center',
      paddingHorizontal: HORIZONTAL_PADDING,
      width: '100%'
    },
    cardHeader: Object.assign({}, _text.presets.caption2Bold, {
      letterSpacing: 2.4,
      paddingHorizontal: HORIZONTAL_PADDING,
      textAlign: "center",
      color: "#D9D9D9",
      lineHeight: 16,
      opacity: 0.3,
      textTransform: "uppercase"
    }),
    wrapCardComponentStyle: {
      marginTop: 12
    },
    wrapCardViewComponentStyle: {
      borderWidth: 1,
      borderColor: _theme.color.palette.darkestGrey,
      borderRadius: 8,
      width: CARD_WIDTH,
      backgroundColor: _theme.color.palette.black,
      overflow: 'hidden'
    },
    cardComponentStyle: {
      paddingTop: 20,
      paddingBottom: 8,
      paddingHorizontal: 8,
      borderRadius: 8
    },
    separatorItem: {
      width: SEPARATOR_ITEM_WIDTH
    },
    imageCardStyle: {
      width: IMAGE_CARD_WIDTH,
      minHeight: 40
    },
    cardTitle: Object.assign({
      marginTop: 14
    }, _text.presets.caption2Bold, {
      textAlign: 'center',
      color: "rgba(252,252,252,0.6)"
    }),
    buttonViewAll: {
      alignSelf: 'center',
      marginHorizontal: HORIZONTAL_PADDING,
      width: width - 48,
      borderRadius: 60,
      padding: 1,
      height: 44
    },
    viewContentButtonLabel: {
      backgroundColor: _theme.color.palette.black,
      borderRadius: 60,
      height: 42,
      paddingVertical: 10,
      alignItems: 'center',
      justifyContent: 'center'
    },
    buttonLabel: Object.assign({}, _text.presets.bodyTextBold, {
      lineHeight: 24,
      textAlignVertical: 'center'
    }),
    lottieStyle: {
      width: "100%",
      height: '100%',
      position: 'absolute',
      bottom: 0,
      right: 0,
      transform: [{
        scale: _reactNative.Platform.select({
          android: 1.05,
          ios: 1
        })
      }]
    }
  });
  var _default = exports.default = styles;
