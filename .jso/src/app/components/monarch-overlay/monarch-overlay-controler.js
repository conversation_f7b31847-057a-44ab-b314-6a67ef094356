  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _MonarchOverlayController;
  var MonarchOverlayController = exports.default = /*#__PURE__*/(0, _createClass2.default)(function MonarchOverlayController() {
    (0, _classCallCheck2.default)(this, MonarchOverlayController);
  });
  _MonarchOverlayController = MonarchOverlayController;
  MonarchOverlayController.setAppRef = function (ref) {
    _MonarchOverlayController.appRef = ref;
  };
  MonarchOverlayController.setModalRef = function (ref) {
    _MonarchOverlayController.modalRef = ref;
  };
  MonarchOverlayController.showOverlay = function (nav) {
    var _MonarchOverlayContro;
    (_MonarchOverlayContro = _MonarchOverlayController.modalRef) == null || (_MonarchOverlayContro = _MonarchOverlayContro.current) == null || _MonarchOverlayContro.showOverlay(nav);
  };
  MonarchOverlayController.hideOverlay = function () {
    var _MonarchOverlayContro2;
    (_MonarchOverlayContro2 = _MonarchOverlayController.modalRef) == null || (_MonarchOverlayContro2 = _MonarchOverlayContro2.current) == null || _MonarchOverlayContro2.hideOverlay();
  };
