  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Voucher = Voucher;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _voucherCard = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _add_button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _ribbon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _saved_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _text2 = _$$_REQUIRE(_dependencyMap[12]);
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var containerStyle = {
    height: 126,
    width: 288,
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center"
  };
  var tenantNameTextStyle = Object.assign({}, _text.presets.caption1Regular, {
    marginTop: 5,
    width: 165
  });
  var offerDescriptionTextStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var validTillDateTextStyle = Object.assign({}, _text.presets.caption2Regular, {
    marginTop: 5
  });
  var imageStyle = {
    borderBottomLeftRadius: 20,
    borderTopLeftRadius: 20,
    height: "100%",
    width: 98
  };
  var addImageStyle = {
    position: "absolute",
    right: 12,
    bottom: 12
  };
  var textViewStyle = {
    height: "100%",
    width: 175,
    justifyContent: "center",
    alignItems: "flex-start"
  };
  var cornerShapesViewStyle = {
    overflow: "hidden",
    width: 10,
    height: 15,
    position: "absolute",
    alignSelf: "center",
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var cornerShapeLeft = Object.assign({}, cornerShapesViewStyle, {
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    left: -3
  });
  var cornerShapeRight = Object.assign({}, cornerShapesViewStyle, {
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    right: -3
  });
  var voucherImageStyle = {
    height: "100%"
  };
  var skeletonLayoutImage = [{
    width: 98,
    height: 126,
    borderBottomLeftRadius: 20,
    borderTopLeftRadius: 20
  }];
  var voucherTextStyle = {
    marginLeft: 12
  };
  var voucherTextStyleCaseRibbon = {
    marginLeft: 12,
    marginTop: 25
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayoutText = [{
    width: 107,
    height: 13,
    borderRadius: 4,
    marginLeft: 12
  }, {
    width: 56,
    height: 13,
    borderRadius: 4,
    marginTop: 10,
    marginLeft: 12
  }, {
    width: 112,
    height: 13,
    borderRadius: 4,
    marginTop: 10,
    marginLeft: 12
  }];
  var lockedTagStyle = {
    position: "absolute",
    left: 10,
    top: 9
  };
  var wrapRibbonTextStyle = {
    position: "absolute",
    left: 50,
    top: 9,
    backgroundColor: "#A80055",
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderRadius: 4
  };
  var ribbonTextStyle = {
    color: _theme.color.palette.whiteGrey
  };
  var categoryCodeStyle = Object.assign({}, _text.presets.caption2Bold, {
    color: _theme.color.palette.lightPurple,
    marginBottom: 4
  });
  var pointsContainer = {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: _theme.color.palette.lightestPurple,
    borderRadius: 8,
    marginTop: 6
  };
  var pointsText = Object.assign({}, _text.presets.caption2Bold, {
    color: _theme.color.palette.lightPurple
  });
  var loadingView = function loadingView(width) {
    return (0, _jsxRuntime.jsxs)(_voucherCard.VoucherCard, {
      style: Object.assign({}, containerStyle, {
        width: width
      }),
      isDisabled: true,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[0]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutText[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutText[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutText[2]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: cornerShapeRight
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: cornerShapeLeft
      })]
    });
  };
  function Voucher(props) {
    var tenantName = props.tenantName,
      offerTitleDescription = props.offerTitleDescription,
      validTillDate = props.validTillDate,
      _props$isValidTillDat = props.isValidTillDate,
      isValidTillDate = _props$isValidTillDat === undefined ? false : _props$isValidTillDat,
      addButton = props.addButton,
      savedIcon = props.savedIcon,
      lockedStyle = props.lockedStyle,
      _props$isLoading = props.isLoading,
      isLoading = _props$isLoading === undefined ? false : _props$isLoading,
      _props$isLocked = props.isLocked,
      isLocked = _props$isLocked === undefined ? false : _props$isLocked,
      imageUrl = props.imageUrl,
      onPressed = props.onPressed,
      onClickAddButton = props.onClickAddButton,
      onClickSaveButton = props.onClickSaveButton,
      _props$isDisabled = props.isDisabled,
      isDisabled = _props$isDisabled === undefined ? false : _props$isDisabled,
      _props$width = props.width,
      width = _props$width === undefined ? 288 : _props$width,
      categoryCode = props.categoryCode,
      points = props.points,
      ribbonText = props.ribbonText,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "RedeemNewRewards" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "RedeemNewRewards" : _props$accessibilityL;
    return isLoading ? loadingView(width) : (0, _jsxRuntime.jsxs)(_voucherCard.VoucherCard, {
      style: Object.assign({}, containerStyle, {
        width: width
      }),
      onPressed: onPressed,
      isDisabled: isDisabled,
      testID: `${testID}__VoucherCard`,
      accessibilityLabel: `${accessibilityLabel}__VoucherCard`,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, containerStyle, lockedStyle),
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: voucherImageStyle,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: imageUrl
            },
            style: imageStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: addImageStyle,
            children: [addButton && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onClickAddButton,
              disabled: isDisabled || isLocked,
              testID: `${testID}__TouchableAddButton`,
              accessibilityLabel: `${accessibilityLabel}__TouchableAddButton`,
              children: (0, _jsxRuntime.jsx)(_add_button.default, {})
            }), savedIcon && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onClickSaveButton,
              disabled: isDisabled,
              testID: `${testID}__TouchableSave`,
              accessibilityLabel: `${accessibilityLabel}__TouchableSave`,
              children: (0, _jsxRuntime.jsx)(_saved_icon.default, {})
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: (0, _utils.handleCondition)(ribbonText, voucherTextStyleCaseRibbon, voucherTextStyle),
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: textViewStyle,
            children: [!!categoryCode && (0, _jsxRuntime.jsx)(_text2.Text, {
              style: categoryCodeStyle,
              numberOfLines: 1,
              children: categoryCode
            }), (0, _jsxRuntime.jsx)(_text2.Text, {
              style: offerDescriptionTextStyle,
              numberOfLines: 2,
              children: offerTitleDescription
            }), !!points && (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: pointsContainer,
              children: (0, _jsxRuntime.jsx)(_text2.Text, {
                style: pointsText,
                children: points
              })
            }), !!tenantName && (0, _jsxRuntime.jsx)(_text2.Text, {
              style: tenantNameTextStyle,
              numberOfLines: 1,
              children: tenantName
            }), isValidTillDate && (0, _jsxRuntime.jsx)(_text2.Text, {
              style: validTillDateTextStyle,
              numberOfLines: 1,
              children: validTillDate
            })]
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: cornerShapeRight
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: cornerShapeLeft
      }), isLocked && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: lockedTagStyle,
        children: (0, _jsxRuntime.jsx)(_ribbon.default, {})
      }), ribbonText && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: wrapRibbonTextStyle,
        children: (0, _jsxRuntime.jsx)(_text2.Text, {
          text: ribbonText.toUpperCase(),
          numberOfLines: 1,
          preset: "XSmallBold",
          style: ribbonTextStyle
        })
      })]
    });
  }
