  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.rightContent = exports.marginText = exports.marginOneText = exports.marginDescription = exports.loadingElementsLayout = exports.lineStyle = exports.lightGreyLoadingColors = exports.itemWrapper = exports.imageWrapper = exports.image = exports.greyLightLoadingColors = exports.darkest = exports.colorBold = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var itemWrapper = exports.itemWrapper = {
    flexDirection: "row"
  };
  var imageWrapper = exports.imageWrapper = {
    borderRadius: 12
  };
  var image = exports.image = {
    width: 90,
    height: 90,
    borderRadius: 12
  };
  var rightContent = exports.rightContent = {
    marginLeft: 16,
    alignSelf: "center",
    flex: 1
  };
  var marginDescription = exports.marginDescription = {
    marginTop: 6
  };
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var loadingElementsLayout = exports.loadingElementsLayout = [{
    width: "100%",
    height: 13,
    borderRadius: 4
  }, {
    width: "100%",
    height: 13,
    borderRadius: 4,
    marginTop: 8
  }, {
    width: "80%",
    height: 13,
    borderRadius: 4,
    marginTop: 8
  }, {
    width: "40%",
    height: 13,
    borderRadius: 4,
    marginTop: 8
  }];
  var greyLightLoadingColors = exports.greyLightLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lineStyle = exports.lineStyle = {
    borderStyle: "solid",
    borderBottomWidth: 1,
    borderColor: _theme.color.palette.lighterGrey,
    marginVertical: 24
  };
  var marginText = exports.marginText = {
    marginRight: 24
  };
  var colorBold = exports.colorBold = {
    color: _theme.color.palette.almostBlackGrey
  };
  var darkest = exports.darkest = {
    color: _theme.color.palette.darkestGrey
  };
  var marginOneText = exports.marginOneText = Object.assign({}, darkest, {
    marginRight: 2
  });
