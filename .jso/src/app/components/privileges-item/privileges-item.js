  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PrivilegesItem = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _privilegesItem = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var PrivilegesItemLoading = function PrivilegesItemLoading() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.itemWrapper,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.image,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: styles.lightGreyLoadingColors,
            shimmerStyle: styles.image
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.rightContent,
          children: styles.loadingElementsLayout.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: styles.greyLightLoadingColors,
                shimmerStyle: item
              })
            }, index);
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.lineStyle
      })]
    });
  };
  var PrivilegesItemDefault = function PrivilegesItemDefault(props) {
    var name = props.name,
      description = props.description,
      image = props.image,
      expiry = props.expiry,
      quantity = props.quantity,
      index = props.index;
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [index !== 0 && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.lineStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.itemWrapper,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.imageWrapper,
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: image
            },
            style: styles.image
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.rightContent,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            numberOfLines: 2,
            style: styles.colorBold,
            children: name
          }), !!description && (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            style: styles.marginDescription,
            numberOfLines: 1,
            children: description
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: [styles.itemWrapper, styles.marginDescription],
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: [styles.itemWrapper, styles.marginText],
              children: [(0, _jsxRuntime.jsxs)(_text.Text, {
                preset: "caption2Bold",
                style: styles.marginOneText,
                children: [(0, _i18n.translate)("privilegesScreen.quantity"), ":"]
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption2Regular",
                style: styles.darkest,
                children: quantity
              })]
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.itemWrapper,
              children: [(0, _jsxRuntime.jsxs)(_text.Text, {
                preset: "caption2Bold",
                style: styles.marginOneText,
                children: [(0, _i18n.translate)("privilegesScreen.expiry"), ":"]
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption2Regular",
                style: styles.darkest,
                children: expiry
              })]
            })]
          })]
        })]
      })]
    });
  };
  var PrivilegesItem = exports.PrivilegesItem = function PrivilegesItem(props) {
    return props.type === _privilegesItem.PrivilegesItemType.loading ? (0, _jsxRuntime.jsx)(PrivilegesItemLoading, {}) : (0, _jsxRuntime.jsx)(PrivilegesItemDefault, Object.assign({}, props));
  };
