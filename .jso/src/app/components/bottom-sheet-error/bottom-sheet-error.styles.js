  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleStyle = exports.textContainerStyle = exports.tagStyle = exports.systemFonts = exports.modalStyle = exports.messageStyle = exports.iconStyle = exports.iconConfig = exports.dismissIconContainer = exports.containerStyle = exports.buttonStyle = exports.CONTENT_WIDTH = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeRenderHtml = _$$_REQUIRE(_dependencyMap[5]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var CONTENT_WIDTH = exports.CONTENT_WIDTH = width - 48;
  var systemFonts = exports.systemFonts = [].concat((0, _toConsumableArray2.default)(_reactNativeRenderHtml.defaultSystemFonts), ["Lato-Regular", "Lato-Bold"]);
  var modalStyle = exports.modalStyle = {
    justifyContent: "flex-end",
    margin: 0
  };
  var containerStyle = exports.containerStyle = {
    paddingHorizontal: 24,
    alignItems: "center",
    backgroundColor: _theme.color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopEndRadius: 16,
    paddingBottom: 48
  };
  var iconStyle = exports.iconStyle = {
    position: "absolute",
    top: -35,
    alignItems: "center",
    justifyContent: "center",
    width: 70,
    height: 70,
    borderRadius: 50
  };
  var iconConfig = exports.iconConfig = {
    width: 70,
    height: 70
  };
  var dismissIconContainer = exports.dismissIconContainer = {
    alignSelf: "flex-end",
    marginTop: 15
  };
  var textContainerStyle = exports.textContainerStyle = {
    marginVertical: 30
  };
  var titleStyle = exports.titleStyle = Object.assign({}, _text.presets.h2, {
    lineHeight: 28,
    textAlign: "center",
    marginBottom: 16
  });
  var messageStyle = exports.messageStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    textAlign: "center",
    color: _theme.color.palette.darkestGrey
  });
  var buttonStyle = exports.buttonStyle = {
    width: "100%",
    borderRadius: 60
  };
  var tagStyle = exports.tagStyle = {
    ul: Object.assign({}, _text.newPresets.bodyTextRegular, {
      paddingLeft: 11,
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center",
      textAlign: "center"
    }),
    ol: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center",
      textAlign: "center"
    }),
    li: Object.assign({}, _text.newPresets.bodyTextRegular, {
      marginBottom: 5,
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center",
      textAlign: "center"
    }),
    p: Object.assign({}, _text.newPresets.bodyTextRegular, {
      marginBottom: 10,
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center",
      textAlign: "center"
    })
  };
