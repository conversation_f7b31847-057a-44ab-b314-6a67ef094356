  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _button = _$$_REQUIRE(_dependencyMap[9]);
  var _color = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _isEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[14]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _reactNativeRenderHtml = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _htmlContent = _$$_REQUIRE(_dependencyMap[17]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _constants = _$$_REQUIRE(_dependencyMap[20]);
  var _modalManagerRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BottomSheetError = function BottomSheetError(props) {
    var visible = props.visible,
      title = props.title,
      errorMessage = props.errorMessage,
      errorMessageChildren = props.errorMessageChildren,
      _props$buttonText = props.buttonText,
      buttonText = _props$buttonText === undefined ? (0, _i18n.translate)("popupError.retry") : _props$buttonText,
      _props$icon = props.icon,
      icon = _props$icon === undefined ? (0, _jsxRuntime.jsx)(_icons.Info, {}) : _props$icon,
      onButtonPressed = props.onButtonPressed,
      onClose = props.onClose,
      iconUrl = props.iconUrl,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "BottomSheetError" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "BottomSheetError" : _props$accessibilityL,
      enableHtmlContent = props.enableHtmlContent,
      colorMsg = props.colorMsg,
      animationInTiming = props.animationInTiming,
      animationOutTiming = props.animationOutTiming,
      openPendingModal = props.openPendingModal,
      shouldFitContentHeight = props.shouldFitContentHeight;
    var dispatch = (0, _reactRedux.useDispatch)();
    var initContentHeight = errorMessage ? 310 : 280;
    var _useState = (0, _react.useState)(initContentHeight),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      contentHeight = _useState2[0],
      setContentHeight = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isButtonPressed = _useState4[0],
      setButtonPressed = _useState4[1];
    var authTokenVerifyError = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.authTokenVerifyError);
    var errorMessageContent = (0, _react.useMemo)(function () {
      if ((0, _isEmpty2.default)(errorMessage)) return null;
      if (enableHtmlContent) return (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
        source: {
          html: (0, _htmlContent.formatHtmlContent)(errorMessage)
        },
        tagsStyles: styles.tagStyle,
        contentWidth: styles.CONTENT_WIDTH,
        systemFonts: styles.systemFonts
      });
      return (0, _jsxRuntime.jsx)(_text.Text, {
        style: Object.assign({}, styles.messageStyle, {
          color: colorMsg
        }),
        text: errorMessage
      });
    }, [errorMessage, enableHtmlContent]);
    var isPrevented = (0, _react.useMemo)(function () {
      return (authTokenVerifyError == null ? undefined : authTokenVerifyError.isError) && (authTokenVerifyError == null ? undefined : authTokenVerifyError.kind) === _constants.API_FAILURE_KIND.UNAUTHORIZED;
    }, [authTokenVerifyError == null ? undefined : authTokenVerifyError.isError, authTokenVerifyError == null ? undefined : authTokenVerifyError.kind]);
    var onContentLayout = function onContentLayout(event) {
      var height = event.nativeEvent.layout.height;
      var allContentHeight = height + 60 + 44 + 35 + 52; // 35 for header, 60 for margin vertical, 44 for button, 52 for margin bottom
      if (allContentHeight > initContentHeight || shouldFitContentHeight) {
        setContentHeight(allContentHeight);
      }
    };
    if (isPrevented) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      isVisible: visible,
      onBackdropPress: onClose,
      onSwipeComplete: onClose,
      swipeDirection: "down",
      animationInTiming: animationInTiming || 100,
      animationOutTiming: animationOutTiming || 100,
      style: styles.modalStyle,
      onModalHide: function onModalHide() {
        isButtonPressed && (onButtonPressed == null ? undefined : onButtonPressed());
        setButtonPressed(false);
        if (openPendingModal) {
          dispatch(_modalManagerRedux.default.openPendingModal());
        }
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, styles.containerStyle, {
          height: contentHeight
        }),
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.iconStyle,
          children: iconUrl ? (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: (0, _mediaHelper.handleImageUrl)(iconUrl)
            },
            style: styles.iconConfig
          }) : icon
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.dismissIconContainer,
          onPress: onClose,
          testID: `${testID}__TouchableClose`,
          accessibilityLabel: `${accessibilityLabel}__TouchableCrossClose`,
          children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {})
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          onLayout: onContentLayout,
          style: styles.textContainerStyle,
          children: [!(0, _isEmpty2.default)(title) && (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.titleStyle,
            text: title
          }), errorMessageChildren || errorMessageContent]
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.buttonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_color.color.palette.gradientColor1End, _color.color.palette.gradientColor1Start],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "secondary",
            text: buttonText,
            statePreset: "default",
            backgroundPreset: "light",
            onPress: function onPress() {
              setButtonPressed(true);
              onClose == null || onClose();
            },
            testID: `${testID}__ButtonExecuteAndClose`,
            accessibilityLabel: `${testID}__ButtonExecuteAndClose`
          })
        })]
      })
    });
  };
  var _default = exports.default = _react.default.memo(BottomSheetError);
