  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.whiteGreyLoadingColors = exports.CalloutBanner = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _calloutBanner = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var containerStyle = {
    marginHorizontal: 25,
    flexDirection: "row",
    borderRadius: 8
  };
  var loadingContainerStyle = {
    marginHorizontal: 25,
    flexDirection: "row",
    borderRadius: 8,
    padding: 10,
    backgroundColor: _theme.color.palette.lightGrey
  };
  var loadingCircleStyle = {
    height: 20,
    width: 20,
    borderRadius: 20
  };
  var loadingTextStyle = {
    height: 13,
    borderRadius: 4,
    marginLeft: 12,
    marginTop: 3,
    flex: 1
  };
  var iconStyle = {
    marginVertical: 11,
    marginHorizontal: 14,
    height: 20,
    width: 20
  };
  var bannerTextStyle = {
    alignSelf: "center",
    margin: 12,
    marginLeft: 0,
    flex: 1
  };
  var whiteGreyLoadingColors = exports.whiteGreyLoadingColors = [_theme.color.palette.whiteGrey, _theme.color.background, _theme.color.palette.whiteGrey];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: loadingContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: whiteGreyLoadingColors,
        shimmerStyle: loadingCircleStyle
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: whiteGreyLoadingColors,
        shimmerStyle: loadingTextStyle
      })]
    });
  };
  var getBannerTextStyle = function getBannerTextStyle(textColor) {
    return Object.assign({}, bannerTextStyle, {
      color: textColor
    });
  };
  var getBannerContainerStyle = function getBannerContainerStyle(layoutColor) {
    return Object.assign({}, containerStyle, {
      backgroundColor: layoutColor
    });
  };
  var defaultView = function defaultView(props) {
    var isVisible = props.isVisible,
      iconUrl = props.iconUrl,
      messageBold = props.messageBold,
      messageNormal = props.messageNormal,
      contentColorCode = props.contentColorCode,
      layoutColorCode = props.layoutColorCode;
    return isVisible ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: getBannerContainerStyle(layoutColorCode),
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: iconUrl
        },
        style: iconStyle
      }), (0, _jsxRuntime.jsxs)(_text.Text, {
        style: getBannerTextStyle(contentColorCode),
        children: [messageBold && (0, _jsxRuntime.jsx)(_text.Text, {
          text: `${messageBold} `,
          preset: "caption1BoldItalic",
          style: getBannerTextStyle(contentColorCode)
        }), messageNormal && (0, _jsxRuntime.jsx)(_text.Text, {
          text: messageNormal,
          preset: "caption1Italic",
          style: getBannerTextStyle(contentColorCode)
        })]
      })]
    }) : null;
  };
  var CalloutBanner = exports.CalloutBanner = function CalloutBanner(props) {
    var type = props.type;
    var isLoading = type === _calloutBanner.CalloutBannerType.loading;
    return isLoading ? loadingView() : defaultView(props);
  };
