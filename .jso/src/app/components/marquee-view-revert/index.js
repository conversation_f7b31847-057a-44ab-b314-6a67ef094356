  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var MeasureElement = function MeasureElement(_ref) {
    var _onLayout = _ref.onLayout,
      children = _ref.children;
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.ScrollView, {
      horizontal: true,
      style: marqueeStyles.hidden,
      pointerEvents: "box-none",
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        onLayout: function onLayout(ev) {
          return _onLayout(ev.nativeEvent.layout.width);
        },
        children: children
      })
    });
  };
  var _worklet_16058955970324_init_data = {
    code: "function indexTsx1(){const{index,childrenWidth,offset}=this.__closure;return{left:(index-1)*childrenWidth,transform:[{translateX:offset.value}]};}"
  };
  var TranslatedElement = function TranslatedElement(_ref2) {
    var index = _ref2.index,
      children = _ref2.children,
      offset = _ref2.offset,
      childrenWidth = _ref2.childrenWidth;
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        return {
          left: (index - 1) * childrenWidth,
          transform: [{
            translateX: offset.value
          }]
        };
      };
      indexTsx1.__closure = {
        index: index,
        childrenWidth: childrenWidth,
        offset: offset
      };
      indexTsx1.__workletHash = 16058955970324;
      indexTsx1.__initData = _worklet_16058955970324_init_data;
      return indexTsx1;
    }());
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [styles.animatedStyle, animatedStyle],
      children: children
    });
  };
  var getIndicesArray = function getIndicesArray(length) {
    return Array.from({
      length: length
    }, function (_, i) {
      return i;
    });
  };
  var Cloner = function Cloner(_ref3) {
    var count = _ref3.count,
      renderChild = _ref3.renderChild;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: getIndicesArray(count).map(renderChild)
    });
  };
  var _worklet_1286811357074_init_data = {
    code: "function indexTsx2(i){const{childrenWidth,duration,offset}=this.__closure;var _i$timeSincePreviousF;const delta=((_i$timeSincePreviousF=i.timeSincePreviousFrame)!==null&&_i$timeSincePreviousF!==void 0?_i$timeSincePreviousF:1)*childrenWidth/duration;offset.value-=delta;if(offset.value<0)offset.value+=childrenWidth;}"
  };
  var ChildrenScroller = function ChildrenScroller(_ref4) {
    var duration = _ref4.duration,
      childrenWidth = _ref4.childrenWidth,
      parentWidth = _ref4.parentWidth,
      children = _ref4.children;
    var offset = (0, _reactNativeReanimated.useSharedValue)(0);
    var coeff = (0, _reactNativeReanimated.useSharedValue)(1);
    _react.default.useEffect(function () {
      coeff.value = 1;
    }, []);
    (0, _reactNativeReanimated.useFrameCallback)(function () {
      var indexTsx2 = function indexTsx2(i) {
        var _i$timeSincePreviousF;
        var delta = ((_i$timeSincePreviousF = i.timeSincePreviousFrame) != null ? _i$timeSincePreviousF : 1) * childrenWidth / duration;
        offset.value -= delta;
        if (offset.value < 0) offset.value += childrenWidth;
      };
      indexTsx2.__closure = {
        childrenWidth: childrenWidth,
        duration: duration,
        offset: offset
      };
      indexTsx2.__workletHash = 1286811357074;
      indexTsx2.__initData = _worklet_1286811357074_init_data;
      return indexTsx2;
    }(), true);
    var count = Math.round(parentWidth / childrenWidth) + 2;
    var renderChild = function renderChild(index) {
      return (0, _jsxRuntime.jsx)(TranslatedElement, {
        index: index,
        offset: offset,
        childrenWidth: childrenWidth,
        children: children
      }, `clone-${index}`);
    };
    return (0, _jsxRuntime.jsx)(Cloner, {
      count: count,
      renderChild: renderChild
    });
  };
  var Marquee = function Marquee(_ref5) {
    var _ref5$duration = _ref5.duration,
      duration = _ref5$duration === undefined ? 2000 : _ref5$duration,
      children = _ref5.children,
      style = _ref5.style;
    var _React$useState = _react.default.useState(0),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      parentWidth = _React$useState2[0],
      setParentWidth = _React$useState2[1];
    var _React$useState3 = _react.default.useState(0),
      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
      childrenWidth = _React$useState4[0],
      setChildrenWidth = _React$useState4[1];
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: style,
      onLayout: function onLayout(ev) {
        setParentWidth(ev.nativeEvent.layout.width);
      },
      pointerEvents: "box-none",
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: marqueeStyles.row,
        pointerEvents: "box-none",
        children: [(0, _jsxRuntime.jsx)(MeasureElement, {
          onLayout: setChildrenWidth,
          children: children
        }), childrenWidth > 0 && parentWidth > 0 && (0, _jsxRuntime.jsx)(ChildrenScroller, {
          duration: duration,
          parentWidth: parentWidth,
          childrenWidth: childrenWidth,
          children: children
        })]
      })
    });
  };
  var marqueeStyles = _reactNative.StyleSheet.create({
    hidden: {
      opacity: 0,
      zIndex: -1
    },
    row: {
      flexDirection: 'row',
      overflow: 'hidden'
    }
  });
  var styles = _reactNative.StyleSheet.create({
    animatedStyle: {
      position: 'absolute'
    }
  });
  var _default = exports.default = Marquee;
