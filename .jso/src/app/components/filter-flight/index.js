  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[9]);
  var _flyFiltersCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _filterLocation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _flightFilterRevamp = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[16]);
  var _fly = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var styles = _reactNative2.StyleSheet.create({
    badgeViewStyle: {
      alignSelf: "flex-end",
      position: "absolute",
      right: -3,
      top: -3,
      zIndex: 1
    },
    badgeViewStyleOld: {
      alignSelf: "flex-end",
      position: "absolute",
      right: 3,
      top: -1,
      zIndex: 1
    },
    calendarStyles: {
      color: _theme.color.palette.lightPurple,
      marginRight: 5
    },
    containerViewStyle: {
      flexDirection: "row",
      marginTop: 24
    },
    dateTextStyle: {
      height: 24,
      width: "80%"
    },
    locationCalendarContainerViewStyle: {
      flexDirection: "row",
      justifyContent: "flex-end",
      width: "20%"
    },
    locationIconViewStyle: {
      marginEnd: 16,
      padding: 2
    },
    locationIconViewStyleOld: {
      marginEnd: 16
    }
  });
  var COMPONENT_NAME = "FilterFlight";
  var FilterFlight = function FilterFlight(props, ref) {
    var onFilterFlight = props.onFilterFlight,
      _props$initialDate = props.initialDate,
      initialDate = _props$initialDate === undefined ? new Date() : _props$initialDate,
      _props$disableChangeD = props.disableChangeDate,
      disableChangeDate = _props$disableChangeD === undefined ? false : _props$disableChangeD,
      _props$disableChangeF = props.disableChangeFilterLocation,
      disableChangeFilterLocation = _props$disableChangeF === undefined ? false : _props$disableChangeF,
      containerFilterStyle = props.containerFilterStyle,
      initialFilterLocation = props.initialFilterLocation,
      isDateSelected = props.isDateSelected,
      displayDate = props.displayDate,
      index = props.index,
      isFromSearch = props.isFromSearch,
      direction = props.direction,
      _props$isShowCalendar = props.isShowCalendar,
      isShowCalendar = _props$isShowCalendar === undefined ? true : _props$isShowCalendar,
      _props$isNewDesign = props.isNewDesign,
      isNewDesign = _props$isNewDesign === undefined ? false : _props$isNewDesign,
      _props$locationCalend = props.locationCalendarContainerViewStyle,
      locationCalendarContainerViewStyle = _props$locationCalend === undefined ? {} : _props$locationCalend;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showCalendar = _useState2[0],
      _setShowCalendar = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isModalVisible = _useState4[0],
      setModalVisibleState = _useState4[1];
    var _useState5 = (0, _react.useState)((initialFilterLocation == null ? undefined : initialFilterLocation.length) > 0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isLocationFilterApplied = _useState6[0],
      setIsLocationFilterApplied = _useState6[1];
    var filterDateDeparture = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.filterDateDeparture(state);
    });
    var filterDateArrival = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.filterDateArrival(state);
    });
    var FLY_CONTEXT_HANDLERS = _react.default.useContext(_fly.FLY_CONTEXT).Handlers;
    var _useState7 = (0, _react.useState)(initialDate),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      date = _useState8[0],
      setDate = _useState8[1];
    var filterDateIOS = (0, _react.useRef)(undefined);
    var setModalVisible = function setModalVisible(value) {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(value);
      setModalVisibleState(value);
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        setModalVisible(false);
        _setShowCalendar(false);
      };
    }, []));
    var textDate = (0, _react.useMemo)(function () {
      if (direction === "DEP") {
        return filterDateDeparture ? (0, _dateTime.dateToFromNow)(filterDateDeparture) : displayDate || (0, _dateTime.dateToFromNow)(date);
      }
      if (direction === "ARR") {
        return filterDateArrival ? (0, _dateTime.dateToFromNow)(filterDateArrival) : displayDate || (0, _dateTime.dateToFromNow)(date);
      }
      return displayDate || (0, _dateTime.dateToFromNow)(date);
    }, [filterDateDeparture, filterDateArrival, direction, displayDate]);
    var currentFilterDate = function currentFilterDate() {
      if (direction === "DEP") {
        return filterDateDeparture || initialDate;
      }
      if (direction === "ARR") {
        return filterDateArrival || initialDate;
      }
      return initialDate;
    };
    var onSelectedDateHandle = function onSelectedDateHandle(selectedDate) {
      filterDateIOS.current = selectedDate;
      if (selectedDate !== undefined && _reactNative2.Platform.OS === "android") {
        var newDate = disableChangeDate ? new Date() : selectedDate;
        setDate(newDate);
        onFilterFlight({
          date: selectedDate
        });
        _setShowCalendar(false);
      }
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        setModalVisible: setModalVisible,
        setShowCalendar: function setShowCalendar(visible) {
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(visible);
          _setShowCalendar(visible);
        }
      };
    });
    var filterCalendarOnClose = function filterCalendarOnClose(value) {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      if (value) {
        var filterDate = filterDateIOS.current || currentFilterDate();
        if (!disableChangeDate) {
          setDate(filterDate);
        } else {
          filterDateIOS.current = new Date();
        }
        onFilterFlight({
          date: filterDate
        });
      }
      _setShowCalendar(false);
    };
    var filterCalendar = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_flyFiltersCalendar.default, {
        date: currentFilterDate(),
        visible: showCalendar,
        onClosed: function onClosed(value) {
          return filterCalendarOnClose(value);
        },
        onBackPressed: function onBackPressed() {
          return _setShowCalendar(false);
        },
        setVisibleState: function setVisibleState(value) {
          return _setShowCalendar(value);
        },
        selectedDateCallback: function selectedDateCallback(selectedDate) {
          onSelectedDateHandle(selectedDate);
        }
      });
    }, [showCalendar]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [styles.containerViewStyle, containerFilterStyle],
        children: [!isNewDesign && (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          text: textDate,
          style: styles.dateTextStyle
        }), !index && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.locationCalendarContainerViewStyle, locationCalendarContainerViewStyle],
          children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: isNewDesign ? styles.locationIconViewStyle : styles.locationIconViewStyleOld,
            onPress: function onPress() {
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlightLandingFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlightLandingFilter, "Open Filter"));
              _reactNative2.Keyboard.dismiss();
              setModalVisible(true);
            },
            testID: `${COMPONENT_NAME}__TouchableLocation`,
            accessibilityLabel: `${COMPONENT_NAME}__TouchableLocation`,
            children: [isLocationFilterApplied && initialFilterLocation && (0, _jsxRuntime.jsx)(_icons.Badge, {
              style: isNewDesign ? styles.badgeViewStyle : styles.badgeViewStyleOld,
              height: 8,
              width: 8
            }), isNewDesign ? (0, _jsxRuntime.jsx)(_icons.Filter, {
              color: _theme.color.palette.lightPurple,
              height: 20,
              width: 20
            }) : (0, _jsxRuntime.jsx)(_icons.LocationOutline, {
              color: _theme.color.palette.lightPurple,
              height: 24,
              width: 24
            })]
          }), isShowCalendar && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
              _reactNative2.Keyboard.dismiss();
              _setShowCalendar(true);
            },
            testID: `${COMPONENT_NAME}__TouchableCalendar`,
            accessibilityLabel: `${COMPONENT_NAME}__TouchableCalendar`,
            children: [isDateSelected && (0, _jsxRuntime.jsx)(_icons.Badge, {
              style: isNewDesign ? styles.badgeViewStyle : styles.badgeViewStyleOld,
              height: 8,
              width: 8
            }), (0, _jsxRuntime.jsx)(_icons.CalendarFlight, {
              style: styles.calendarStyles
            })]
          })]
        })]
      }), isFromSearch ? (0, _jsxRuntime.jsx)(_flyFiltersCalendar.default, {
        date: currentFilterDate(),
        visible: showCalendar,
        onClosed: function onClosed(value) {
          return filterCalendarOnClose(value);
        },
        onBackPressed: function onBackPressed() {
          return _setShowCalendar(false);
        },
        setVisibleState: function setVisibleState(value) {
          return _setShowCalendar(value);
        },
        selectedDateCallback: function selectedDateCallback(selectedDate) {
          onSelectedDateHandle(selectedDate);
        }
      }) : filterCalendar, isFromSearch ? (0, _jsxRuntime.jsx)(_filterLocation.default, {
        visible: isModalVisible,
        testID: `${COMPONENT_NAME}__FlyFilters`,
        accessibilityLabel: `${COMPONENT_NAME}__FlyFilters`,
        closeSheet: function closeSheet() {
          return setModalVisible(false);
        },
        handleApplyFilter: function handleApplyFilter(filterLocation) {
          onFilterFlight({
            filterLocation: filterLocation
          });
          setIsLocationFilterApplied((filterLocation == null ? undefined : filterLocation.length) > 0);
          setModalVisible(false);
        },
        disableChangeFilterLocation: disableChangeFilterLocation,
        initialFilterLocation: initialFilterLocation
      }) : (0, _jsxRuntime.jsx)(_flightFilterRevamp.default, {
        visible: isModalVisible,
        direction: direction,
        navigationType: _flightProps.NavigationType.FlightsLanding,
        onClosedSheet: function onClosedSheet() {
          setModalVisible(false);
        },
        handleApplyFilter: function handleApplyFilter(filterOption) {
          onFilterFlight({
            filterLocation: filterOption.terminal,
            direction: filterOption.direction,
            airline: filterOption.airline,
            cityAirport: filterOption.cityAirport
          });
          setModalVisible(false);
        },
        testID: `${COMPONENT_NAME}__FlightFilterRevamp`,
        accessibilityLabel: `${COMPONENT_NAME}__FlightFilterRevamp`
      })]
    });
  };
  var _default = exports.default = (0, _react.forwardRef)(FilterFlight);
