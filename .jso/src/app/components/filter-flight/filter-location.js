  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _checkbox = _$$_REQUIRE(_dependencyMap[12]);
  var _button = _$$_REQUIRE(_dependencyMap[13]);
  var _filterLocation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _theme = _$$_REQUIRE(_dependencyMap[15]);
  var _icons = _$$_REQUIRE(_dependencyMap[16]);
  var _locations = _$$_REQUIRE(_dependencyMap[17]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TAG_NAME_ALL = "all_local";
  var FilterLocation = function FilterLocation(props) {
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "FilterLocation" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "FilterLocation" : _props$accessibilityL,
      closeSheet = props.closeSheet,
      disableChangeFilterLocation = props.disableChangeFilterLocation,
      initialFilterLocation = props.initialFilterLocation,
      visible = props.visible,
      handleApplyFilter = props.handleApplyFilter;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)({}),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      checkedLocationState = _useState2[0],
      setCheckedLocationState = _useState2[1];
    var preCheckedState = (0, _react.useRef)({});
    var isInitialState = (0, _react.useRef)(false);
    (0, _react.useEffect)(function () {
      if (visible && !(0, _lodash.isEmpty)(preCheckedState.current)) {
        if (Object.keys(preCheckedState.current).length === (_locations.listLocation == null ? undefined : _locations.listLocation.length)) {
          setCheckedLocationState(Object.assign({}, preCheckedState.current, (0, _defineProperty2.default)({}, TAG_NAME_ALL, true)));
        } else {
          setCheckedLocationState(preCheckedState.current);
        }
      }
    }, [visible]);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(initialFilterLocation) && !(0, _lodash.isEmpty)(_locations.listLocation)) {
        var data = _locations.listLocation.reduce(function (previousValue, item) {
          if (initialFilterLocation != null && initialFilterLocation.includes(item == null ? undefined : item.tagName)) {
            return Object.assign({}, previousValue, (0, _defineProperty2.default)({}, item == null ? undefined : item.tagName, true));
          }
          return previousValue;
        }, {});
        var isCheckAll = (_locations.listLocation == null ? undefined : _locations.listLocation.length) - 1 === Object.keys(data).length;
        isInitialState.current = true;
        var filterData = Object.assign({}, data, (0, _defineProperty2.default)({}, TAG_NAME_ALL, isCheckAll));
        preCheckedState.current = filterData;
        setCheckedLocationState(filterData);
      }
      if ((0, _lodash.isEmpty)(initialFilterLocation)) {
        setCheckedLocationState({});
        preCheckedState.current = [];
      }
    }, [initialFilterLocation]);
    var handleOnCheckboxChange = function handleOnCheckboxChange(tagName, value) {
      var checkBoxState = Object.assign({}, checkedLocationState, (0, _defineProperty2.default)({}, tagName, value));
      var locationCheckedData = Object.entries(checkBoxState).filter(function (_ref) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
          k = _ref2[0],
          v = _ref2[1];
        return v && k !== TAG_NAME_ALL;
      });
      var checkBoxAllValue = (locationCheckedData == null ? undefined : locationCheckedData.length) === (_locations.listLocation == null ? undefined : _locations.listLocation.length) - 1;
      setCheckedLocationState(Object.assign({}, checkBoxState, (0, _defineProperty2.default)({}, TAG_NAME_ALL, checkBoxAllValue)));
    };
    var handleOnCheckedAll = function handleOnCheckedAll(value) {
      var checkedAll = (0, _toConsumableArray2.default)(_locations.listLocation).reduce(function (currentData, tag) {
        currentData[tag.tagName] = value;
        return currentData;
      }, {});
      setCheckedLocationState(checkedAll);
    };
    var getCheckedState = function getCheckedState(checkedState) {
      return Object.entries(checkedState).reduce(function (previousValue, _ref3) {
        var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),
          key = _ref4[0],
          value = _ref4[1];
        if (value && key !== TAG_NAME_ALL) {
          // Recheck checked item existing filter list
          var isExistFilterList = _locations.listLocation.some(function (item) {
            return item.tagName === key;
          });
          if (isExistFilterList) {
            previousValue.push(key);
          }
        }
        return previousValue;
      }, []);
    };
    var onApplyFilter = function onApplyFilter() {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      var checkedLocation = getCheckedState(checkedLocationState);
      preCheckedState.current = checkedLocationState;
      if (disableChangeFilterLocation) {
        setCheckedLocationState({});
      }
      handleApplyFilter(checkedLocation);
    };
    var onClosedSheet = function onClosedSheet() {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      closeSheet();
    };
    var onClearAllFilter = function onClearAllFilter() {
      setCheckedLocationState({});
    };
    var isFilterApplied = (0, _react.useMemo)(function () {
      var checkedData = Object.entries(checkedLocationState).filter(function (_ref5) {
        var _ref6 = (0, _slicedToArray2.default)(_ref5, 2),
          key = _ref6[0],
          newValue = _ref6[1];
        return newValue && key !== TAG_NAME_ALL;
      });
      return (checkedData == null ? undefined : checkedData.length) > 0;
    }, [checkedLocationState]);
    var clearFilterButton = isFilterApplied ? (0, _jsxRuntime.jsx)(_button.Button, {
      tx: "flyLocationFilter.clearAll",
      typePreset: "secondary",
      onPress: onClearAllFilter,
      statePreset: "default",
      backgroundPreset: "light",
      textPreset: "buttonLarge",
      textStyle: _filterLocation.default.clearFilterButtonStyle,
      style: _filterLocation.default.clearButtonStyle,
      testID: `${testID}__TouchableClearFilterLocations`,
      accessibilityLabel: `${accessibilityLabel}__TouchableClearFilterLocations`
    }) : null;
    var renderLocationItem = function renderLocationItem(item, index) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _filterLocation.default.checkboxContainer,
        children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
          value: (0, _lodash.get)(checkedLocationState, item.tagName, false),
          onToggle: function onToggle(value) {
            if (index === 0) {
              handleOnCheckedAll(value);
              return;
            }
            handleOnCheckboxChange(item.tagName, value);
          },
          testID: `${testID}__CheckBoxFilterLocations__${index}`,
          accessibilityLabel: `${accessibilityLabel}__CheckBoxFilterLocations__${index}`,
          text: item.tagTitle,
          textStyle: Object.assign({}, _filterLocation.default.textCheckboxStyles, (0, _lodash.get)(checkedLocationState, item.tagName, false) ? _filterLocation.default.activeCheckboxStyles : {}),
          outlineStyle: _filterLocation.default.outlineStyle
        })
      });
    };
    var renderContent = function renderContent() {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          style: _filterLocation.default.locationContent,
          showsVerticalScrollIndicator: false,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _filterLocation.default.locationLabel,
            tx: "airportLanding.location"
          }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: _locations.listLocation,
            scrollEnabled: true,
            renderItem: function renderItem(_ref7) {
              var item = _ref7.item,
                index = _ref7.index;
              return renderLocationItem(item, index);
            },
            horizontal: false,
            showsVerticalScrollIndicator: false,
            keyExtractor: function keyExtractor(_, index) {
              return index.toString();
            }
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _filterLocation.default.locationFooter,
          children: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [clearFilterButton, (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              style: _filterLocation.default.containerApplyButtonStyle,
              start: {
                x: 0,
                y: 1
              },
              end: {
                x: 1,
                y: 0
              },
              colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
              children: (0, _jsxRuntime.jsx)(_button.Button, {
                tx: "flyLocationFilter.applyFilters",
                typePreset: "secondary",
                onPress: onApplyFilter,
                statePreset: "default",
                backgroundPreset: "dark",
                textPreset: "buttonLarge",
                textStyle: _filterLocation.default.applyFilterButtonStyle,
                style: _filterLocation.default.applyButtonStyle,
                testID: `${testID}__TouchableApplyFiltersLocations`,
                accessibilityLabel: `${accessibilityLabel}__TouchableAppFiltersLocations`
              })
            })]
          })
        })]
      });
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: visible,
      onClosedSheet: onClosedSheet,
      containerStyle: _filterLocation.default.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onClosedSheet,
      animationInTiming: 200,
      animationOutTiming: 200,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _filterLocation.default.bottomSheetContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _filterLocation.default.headerFilter,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _filterLocation.default.rightHeader
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _filterLocation.default.titleSheet,
            tx: "airportLanding.filter"
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedSheet,
            testID: `${testID}__CloseFilter`,
            accessibilityLabel: `${accessibilityLabel}__CloseFilter`,
            children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {
              width: 24,
              height: 24
            })
          })]
        }), renderContent()]
      })
    });
  };
  var _default = exports.default = FilterLocation;
