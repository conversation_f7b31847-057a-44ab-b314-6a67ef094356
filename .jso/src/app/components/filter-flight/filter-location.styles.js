  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var styles = _reactNative.StyleSheet.create({
    activeCheckboxStyles: {
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      })
    },
    airlineLabel: Object.assign({}, _text.presets.h3, {
      color: _theme.color.palette.almostBlackGrey,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      marginTop: 25
    }),
    applyButtonStyle: {
      borderWidth: 0
    },
    applyFilterButtonStyle: {
      color: _theme.color.palette.almostWhiteGrey,
      fontSize: 16,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      textAlign: "center"
    },
    bottomSheetContainer: {
      flex: 1,
      paddingBottom: 22,
      paddingHorizontal: 24
    },
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 495,
      overflow: "hidden",
      width: "100%"
    },
    checkboxContainer: {
      borderBottomColor: _theme.color.palette.lightGrey,
      borderBottomWidth: 1,
      paddingVertical: 16,
      width: "100%"
    },
    clearButtonStyle: {
      marginRight: 13,
      width: width / 2 - 31
    },
    clearFilterButtonStyle: {
      color: _theme.color.palette.gradientColor1Start,
      fontSize: 16,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      textAlign: "center"
    },
    containerApplyButtonStyle: {
      borderRadius: 60,
      flexGrow: 2
    },
    containerOkButtonStyle: {
      borderRadius: 60,
      width: "100%"
    },
    emptyFilterStyle: {
      marginTop: 20
    },
    headerFilter: {
      display: "flex",
      flexDirection: "row",
      marginVertical: 22,
      width: "100%"
    },
    locationContent: {
      display: "flex",
      flexDirection: "column",
      flex: 1,
      paddingBottom: 22
    },
    locationFooter: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingBottom: _reactNative.Platform.OS === 'android' ? 10 : 30,
      paddingTop: 10
    },
    locationLabel: Object.assign({}, _text.presets.h3, {
      color: _theme.color.palette.almostBlackGrey,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      textAlign: "left"
    }),
    outlineStyle: {
      borderColor: _theme.color.palette.darkGrey
    },
    overlayStyle: {
      height: "100%",
      width: "100%"
    },
    rightHeader: {
      width: 24
    },
    textCheckboxStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: "400",
      maxWidth: width - 84
    }),
    titleSheet: Object.assign({
      color: _theme.color.palette.almostBlackGrey
    }, _text.presets.subTitleBold, {
      flexGrow: 2,
      textAlign: "center"
    })
  });
  var _default = exports.default = styles;
