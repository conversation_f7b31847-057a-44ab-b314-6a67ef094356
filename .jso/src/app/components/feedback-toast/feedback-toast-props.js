  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FeedBackToastType = exports.DURATION = undefined;
  var FeedBackToastType = exports.FeedBackToastType = /*#__PURE__*/function (FeedBackToastType) {
    FeedBackToastType["smallFeedBack"] = "smallFeedBack";
    FeedBackToastType["fullWidthFeedBack"] = "fullWidthFeedBack";
    FeedBackToastType["fullWidthFeedBackWithCTA"] = "fullWidthFeedBackWithCTA";
    return FeedBackToastType;
  }({});
  var DURATION = exports.DURATION = {
    LENGTH_MAX: 5000,
    LENGTH_SHORT: 500,
    LENGTH_MEDIUM: 2000,
    LENGTH_LONG: 3000,
    FOREVER: 0,
    FADE_IN_DURATION: 750,
    FADE_OUT_DURATION: 1000,
    OPACITY: 1
  };
