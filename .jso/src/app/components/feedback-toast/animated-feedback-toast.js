  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AnimatedFeedBackToast = undefined;
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[11]);
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[14]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var cardContainer = {
    backgroundColor: _theme.color.palette.almostBlackGrey,
    elevation: 5,
    borderRadius: (0, _reactNativeSizeMatters.scale)(8),
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.2,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0
    }
  };
  var feedBackToastViewStyle = Object.assign({}, cardContainer, {
    paddingHorizontal: 12,
    paddingVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center"
  });
  var smallFeedBackCardContainer = Object.assign({}, cardContainer, {
    alignSelf: "center"
  });
  var fullWidthFeedBackTextStyle = {
    textAlign: "center"
  };
  var textStyle = {
    color: _theme.color.palette.whiteGrey,
    flex: 2,
    maxHeight: 40,
    marginEnd: 10
  };
  var smallFeedBackTextStyle = {
    minHeight: 18,
    marginHorizontal: 12,
    marginVertical: 16,
    textAlign: "center"
  };
  var textLinkStyle = {
    color: _theme.color.palette.lighterPurple,
    textAlign: "right",
    textAlignVertical: "center"
  };
  var containerStyle = {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    elevation: 5,
    alignItems: "center",
    zIndex: 1
  };
  var AnimatedFeedBackToast = exports.AnimatedFeedBackToast = /*#__PURE__*/function (_PureComponent) {
    function AnimatedFeedBackToast() {
      var _this;
      (0, _classCallCheck2.default)(this, AnimatedFeedBackToast);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, AnimatedFeedBackToast, [].concat(args));
      _this.state = {
        isShow: false,
        opacityValue: new _reactNative2.Animated.Value(_feedbackToastProps.DURATION.OPACITY),
        text: "",
        type: _feedbackToastProps.FeedBackToastType
      };
      _this.renderFeedBackToast = function () {
        var pos;
        switch (_this.props.position) {
          case "top":
            pos = {
              top: 0
            };
            break;
          case "bottom":
            pos = {
              bottom: 0
            };
            break;
          case "custom":
            pos = _this.props.positionValue;
            break;
        }
        var toastView = function toastView(props) {
          var type = props.type,
            text = props.text,
            onPress = props.onPress,
            buttonText = props.buttonText,
            numberOfLines = props.numberOfLines,
            _props$informativeTex = props.informativeTextStyle,
            informativeTextStyle = _props$informativeTex === undefined ? {} : _props$informativeTex,
            _props$buttonTextStyl = props.buttonTextStyle,
            buttonTextStyle = _props$buttonTextStyl === undefined ? {} : _props$buttonTextStyl,
            _props$testID = props.testID,
            testID = _props$testID === undefined ? "FeedBackToast" : _props$testID,
            _props$accessibilityL = props.accessibilityLabel,
            accessibilityLabel = _props$accessibilityL === undefined ? "FeedBackToast" : _props$accessibilityL;
          switch (type) {
            case _feedbackToastProps.FeedBackToastType.fullWidthFeedBackWithCTA:
              return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: feedBackToastViewStyle,
                children: [(0, _jsxRuntime.jsx)(_text.Text, Object.assign({
                  text: text,
                  preset: "bodyTextRegular",
                  style: [textStyle, informativeTextStyle]
                }, numberOfLines && {
                  numberOfLines: numberOfLines
                })), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: onPress,
                  testID: `${testID}__TouchableToastView`,
                  accessibilityLabel: `${accessibilityLabel}__TouchableToastView`,
                  style: buttonTextStyle,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    numberOfLines: numberOfLines || 1,
                    text: buttonText,
                    preset: "textLink",
                    style: textLinkStyle
                  })
                })]
              });
            case _feedbackToastProps.FeedBackToastType.fullWidthFeedBack:
              return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: feedBackToastViewStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  numberOfLines: 2,
                  text: text,
                  preset: "bodyTextRegular",
                  style: fullWidthFeedBackTextStyle
                })
              });
            case _feedbackToastProps.FeedBackToastType.smallFeedBack:
              return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: smallFeedBackCardContainer,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  text: text,
                  preset: "bodyTextRegular",
                  style: smallFeedBackTextStyle
                })
              });
            default:
              return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
          }
        };
        return _this.state.isShow ? (0, _jsxRuntime.jsx)(ContainerView, {
          style: Object.assign({}, containerStyle, pos),
          positionBottom: _this.props.positionBottom,
          children: (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
            style: _this.props.style,
            children: toastView(_this.props)
          })
        }) : null;
      };
      return _this;
    }
    (0, _inherits2.default)(AnimatedFeedBackToast, _PureComponent);
    return (0, _createClass2.default)(AnimatedFeedBackToast, [{
      key: "show",
      value: function show(duration) {
        var _this2 = this;
        this.duration = typeof duration === "number" ? duration : _feedbackToastProps.DURATION.LENGTH_SHORT;
        this.setState({
          isShow: true
        });
        this.animation = _reactNative2.Animated.timing(this.state.opacityValue, {
          toValue: _feedbackToastProps.DURATION.OPACITY,
          duration: _feedbackToastProps.DURATION.FADE_IN_DURATION,
          useNativeDriver: true
        });
        this.animation.start(function () {
          var _this2$props;
          _this2.isShow = true;
          (_this2$props = _this2.props) == null || _this2$props.setDisplayCartToast == null || _this2$props.setDisplayCartToast(true);
          if (duration !== _feedbackToastProps.DURATION.FOREVER) _this2.close(_this2.duration);
        });
      }
    }, {
      key: "closeNow",
      value: function closeNow() {
        var _this$props;
        this.setState({
          isShow: false
        });
        this.isShow = false;
        (_this$props = this.props) == null || _this$props.setDisplayCartToast == null || _this$props.setDisplayCartToast(false);
      }
    }, {
      key: "close",
      value: function close(duration) {
        var _this3 = this;
        var delay = typeof duration === "undefined" ? this.duration : duration;
        if (delay === _feedbackToastProps.DURATION.FOREVER) delay = this.props.defaultCloseDelay || 5000;
        if (!this.isShow && !this.state.isShow) return;
        this.timer && clearTimeout(this.timer);
        this.timer = setTimeout(function () {
          _this3.animation = _reactNative2.Animated.timing(_this3.state.opacityValue, {
            toValue: 0.0,
            duration: _feedbackToastProps.DURATION.FADE_OUT_DURATION,
            useNativeDriver: true
          });
          _this3.animation.start(function () {
            var _this3$props;
            _this3.setState({
              isShow: false
            });
            _this3.isShow = false;
            (_this3$props = _this3.props) == null || _this3$props.setDisplayCartToast == null || _this3$props.setDisplayCartToast(false);
          });
        }, delay);
      }
    }, {
      key: "componentWillUnmount",
      value: function componentWillUnmount() {
        var _this$props2;
        this.animation && this.animation.stop();
        (_this$props2 = this.props) == null || _this$props2.setDisplayCartToast == null || _this$props2.setDisplayCartToast(false);
        this.timer && clearTimeout(this.timer);
      }
    }, {
      key: "render",
      value: function render() {
        return this.renderFeedBackToast();
      }
    }]);
  }(_react.PureComponent);
  var _worklet_10433392672632_init_data = {
    code: "function animatedFeedbackToastTsx1(){const{positionBottom,bottomTabActualHeight,bottom,isL2AnnouncementDisplay,l2AnnouncementPosition}=this.__closure;let bottomValue=Math.max(positionBottom.value+bottomTabActualHeight.value,bottom);if(isL2AnnouncementDisplay.value){var _l2AnnouncementPositi;bottomValue=(_l2AnnouncementPositi=l2AnnouncementPosition)===null||_l2AnnouncementPositi===void 0?void 0:_l2AnnouncementPositi.value;}return{bottom:bottomValue};}"
  };
  var ContainerView = function ContainerView(props) {
    var children = props.children,
      positionBottom = props.positionBottom,
      style = props.style;
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabActualHeight = _useContext.bottomTabActualHeight,
      l2AnnouncementPosition = _useContext.l2AnnouncementPosition,
      isL2AnnouncementDisplay = _useContext.isL2AnnouncementDisplay;
    var _useSafeAreaInsets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var animatedFeedbackToastTsx1 = function animatedFeedbackToastTsx1() {
        var bottomValue = Math.max(positionBottom.value + bottomTabActualHeight.value, bottom);
        if (isL2AnnouncementDisplay.value) {
          bottomValue = l2AnnouncementPosition == null ? undefined : l2AnnouncementPosition.value;
        }
        return {
          bottom: bottomValue
        };
      };
      animatedFeedbackToastTsx1.__closure = {
        positionBottom: positionBottom,
        bottomTabActualHeight: bottomTabActualHeight,
        bottom: bottom,
        isL2AnnouncementDisplay: isL2AnnouncementDisplay,
        l2AnnouncementPosition: l2AnnouncementPosition
      };
      animatedFeedbackToastTsx1.__workletHash = 10433392672632;
      animatedFeedbackToastTsx1.__initData = _worklet_10433392672632_init_data;
      return animatedFeedbackToastTsx1;
    }(), [bottomTabActualHeight, bottom, positionBottom]);
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [style, positionBottom ? animatedStyle : {}],
      children: children
    });
  };
