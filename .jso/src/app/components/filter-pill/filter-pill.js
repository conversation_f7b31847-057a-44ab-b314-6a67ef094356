  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var FilterPill = function FilterPill(props) {
    var accessibilityLabel = props.accessibilityLabel,
      active = props.active,
      containerStyle = props.containerStyle,
      disabled = props.disabled,
      IconComponent = props.IconComponent,
      label = props.label,
      labelColor = props.labelColor,
      labelStyle = props.labelStyle,
      onClearSelection = props.onClearSelection,
      onPress = props.onPress,
      outlineColor = props.outlineColor,
      rightIconComponent = props.rightIconComponent,
      _props$size = props.size,
      size = _props$size === undefined ? "md" : _props$size,
      testID = props.testID,
      _props$variant = props.variant,
      variant = _props$variant === undefined ? "solid" : _props$variant,
      showDot = props.showDot;
    var iconSize = (props == null ? undefined : props.iconSize) || 12;
    var styleConfig = (0, _react.useMemo)(function () {
      var container = [styles.commonContainerStyle];
      var label = [];
      if (variant === "outline") {
        container.push(styles.outlineContainerStyle);
      }
      switch (size) {
        case "lg":
          container.push(styles.lgContainerStyle);
          label.push(styles.labelLgTextStyle);
          break;
        case "sm":
          container.push(styles.smContainerStyle);
          label.push(styles.labelSmTextStyle);
          break;
        case "md":
        default:
          container.push(styles.mdContainerStyle);
          label.push(styles.labelMdTextStyle);
          break;
      }
      if (active) {
        label.push(styles.activeLabelTextStyle);
        if (variant === "outline") {
          container.push(styles.activeOutlineContainerStyle);
        } else {
          container.push(styles.activeContainerStyle);
        }
      }
      if (containerStyle) {
        container.push(containerStyle);
      }
      if ((0, _utils.ifAllTrue)([variant === "outline", !active, outlineColor])) {
        container.push({
          borderColor: outlineColor
        });
      }
      if ((0, _utils.ifAllTrue)([!active, labelColor])) {
        label.push({
          color: labelColor
        });
      }
      if (labelStyle) {
        label.push(labelStyle);
      }
      return {
        container: container,
        label: label
      };
    }, [active, containerStyle, size, variant]);
    var shouldShowCloseOrDot = (0, _utils.ifAllTrue)([active, !rightIconComponent]);
    var showCloseButton = (0, _utils.ifAllTrue)([shouldShowCloseOrDot, !showDot]);
    var handlePressCloseBtn = function handlePressCloseBtn() {
      if (onClearSelection) {
        onClearSelection();
      } else {
        onPress == null || onPress();
      }
    };
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      accessibilityLabel: accessibilityLabel,
      disabled: disabled,
      onPress: onPress,
      style: [styleConfig.container, showCloseButton && styles.containerWithPaddingStyle],
      testID: testID,
      children: [IconComponent && (0, _jsxRuntime.jsx)(IconComponent, {
        color: (0, _utils.handleCondition)(active, _theme.color.palette.lightPurple, _theme.color.palette.darkestGrey),
        height: iconSize,
        width: iconSize
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styleConfig.label,
        text: label
      }), showCloseButton && (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        onPress: handlePressCloseBtn,
        style: styles.closeBtnStyle,
        children: (0, _jsxRuntime.jsx)(_icons.Cross, {
          color: _theme.color.palette.lighterPurple,
          height: 20,
          style: {
            marginRight: -6
          },
          width: 20
        })
      }), (0, _utils.ifAllTrue)([shouldShowCloseOrDot, showDot]) && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.dotStyle
      }), (0, _utils.handleCondition)(rightIconComponent, rightIconComponent, null)]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    commonContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.halfLighterGrey,
      flexDirection: "row",
      gap: 2
    },
    smContainerStyle: {
      borderRadius: 14,
      height: 28,
      paddingHorizontal: 10,
      paddingVertical: 4
    },
    mdContainerStyle: {
      borderRadius: 16,
      height: 32,
      paddingHorizontal: 10,
      paddingVertical: 6
    },
    lgContainerStyle: {
      borderRadius: 21,
      height: 42,
      paddingHorizontal: 10,
      paddingVertical: 6
    },
    activeContainerStyle: {
      backgroundColor: _theme.color.palette.lightestPurple
    },
    outlineContainerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderColor: _theme.color.palette.greyCCCCCC,
      borderWidth: 1
    },
    activeOutlineContainerStyle: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderColor: _theme.color.palette.purpleD5BBEA,
      borderWidth: 1
    },
    labelSmTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.darkestGrey,
      textTransform: "none"
    }),
    labelMdTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    labelLgTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    activeLabelTextStyle: {
      color: _theme.color.palette.lightPurple
    },
    containerWithPaddingStyle: {
      paddingRight: 25
    },
    closeBtnStyle: {
      position: "absolute",
      right: 15,
      width: 10
    },
    dotStyle: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.lightPurple,
      borderWidth: 1,
      borderColor: _theme.color.palette.whiteGrey,
      position: "absolute",
      right: 0,
      top: 0
    }
  });
  var _default = exports.default = FilterPill;
