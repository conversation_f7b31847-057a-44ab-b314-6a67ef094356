  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.loadingTextBase = exports.loadingText = exports.loadingImage = exports.loadingHeaderInnerContainer = exports.loadingHeaderContainer = exports.loadingContainer = exports.contentContainer = exports.container = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var container = exports.container = {
    flexDirection: "row",
    alignItems: "center"
  };
  var contentContainer = exports.contentContainer = Object.assign({}, _theme.shadow.primaryShadow, {
    flex: 1,
    marginStart: 12,
    borderTopStartRadius: 16,
    borderBottomStartRadius: 16,
    backgroundColor: _theme.color.palette.whiteGrey,
    marginBottom: 16
  });
  var loadingContainer = exports.loadingContainer = {
    paddingTop: 16,
    paddingStart: 24,
    paddingEnd: 37,
    paddingBottom: 23
  };
  var loadingHeaderContainer = exports.loadingHeaderContainer = {
    flexDirection: "row"
  };
  var loadingHeaderInnerContainer = exports.loadingHeaderInnerContainer = {
    flex: 1
  };
  var loadingImage = exports.loadingImage = {
    width: 60,
    height: 60,
    borderRadius: 12,
    marginEnd: 12
  };
  var loadingTextBase = exports.loadingTextBase = {
    height: 13,
    borderRadius: 4,
    marginTop: 9
  };
  var loadingText = exports.loadingText = [Object.assign({}, loadingTextBase, {
    width: "100%",
    marginTop: 13
  }), Object.assign({}, loadingTextBase, {
    width: "50.5%"
  }), Object.assign({}, loadingTextBase, {
    width: "94.5%"
  }), Object.assign({}, loadingTextBase, {
    width: "34.67%"
  })];
