  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _timelineFlyTiles2 = _$$_REQUIRE(_dependencyMap[8]);
  var _variations = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TimelineFlyContainer = function TimelineFlyContainer(_ref) {
    var children = _ref.children,
      noShadow = _ref.noShadow;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_icons.Dot, {
        width: "16",
        height: "16"
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: Object.assign({}, styles.contentContainer, noShadow ? _theme.shadow.noShadow : {}),
        children: children
      })]
    });
  };
  var TimelineFlyTilesLoadingView = function TimelineFlyTilesLoadingView() {
    return (0, _jsxRuntime.jsx)(TimelineFlyContainer, {
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.loadingContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.loadingHeaderContainer,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: styles.loadingImage
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.loadingHeaderInnerContainer,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: styles.loadingText[0]
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: styles.loadingText[1]
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: styles.loadingText[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: styles.loadingText[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: styles.loadingText[3]
        })]
      })
    });
  };
  var TimelineFlyTilesDefaultView = function TimelineFlyTilesDefaultView(props) {
    var TimelineFlyVariation = _variations.default[props.type];
    var noShadow = _variations.NoShadowVariations.includes(TimelineFlyVariation);
    return (0, _jsxRuntime.jsx)(TimelineFlyContainer, {
      noShadow: noShadow,
      children: (0, _jsxRuntime.jsx)(TimelineFlyVariation, Object.assign({}, props))
    });
  };
  var TimelineFlyTiles = function TimelineFlyTiles(props) {
    return props.type === _timelineFlyTiles2.TimelineFlyTilesDefaultTypes.loading ? (0, _jsxRuntime.jsx)(TimelineFlyTilesLoadingView, {}) : (0, _jsxRuntime.jsx)(TimelineFlyTilesDefaultView, Object.assign({}, props));
  };
  var _default = exports.default = TimelineFlyTiles;
