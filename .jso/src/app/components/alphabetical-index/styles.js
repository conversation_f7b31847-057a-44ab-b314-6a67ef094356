  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    alphabeticalIndexSearchContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      position: "absolute",
      right: 0,
      top: "50%",
      transform: [{
        translateY: -150
      }],
      // Center vertically
      width: 15,
      zIndex: 10
    },
    indexContainerStyle: {
      alignItems: "center",
      height: 14,
      justifyContent: "center",
      width: 14
    },
    indexTextStyle: Object.assign({}, _text.newPresets.bold, {
      fontSize: 10,
      lineHeight: 12
    })
  });
