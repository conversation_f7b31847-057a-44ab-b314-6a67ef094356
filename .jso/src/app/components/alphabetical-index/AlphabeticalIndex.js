  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.AlphabeticalIndex = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[5]);
  var _isNumber2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _styles = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var INDEX_ITEM_HEIGHT = 14;
  var DEFAULT_ALPHABET_LIST = "#ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("").map(function (letter) {
    return {
      label: letter,
      value: letter.toLowerCase()
    };
  });
  var AlphabeticalIndex = exports.AlphabeticalIndex = function AlphabeticalIndex(_ref) {
    var data = _ref.data,
      _ref$nameField = _ref.nameField,
      nameField = _ref$nameField === undefined ? "tenantName" : _ref$nameField,
      _ref$alphabetList = _ref.alphabetList,
      alphabetList = _ref$alphabetList === undefined ? DEFAULT_ALPHABET_LIST : _ref$alphabetList,
      onIndexPress = _ref.onIndexPress,
      _ref$itemAccumulateHe = _ref.itemAccumulateHeights,
      itemAccumulateHeights = _ref$itemAccumulateHe === undefined ? [] : _ref$itemAccumulateHe,
      _ref$visible = _ref.visible,
      visible = _ref$visible === undefined ? true : _ref$visible,
      onTrackingAction = _ref.onTrackingAction,
      containerStyle = _ref.containerStyle,
      indexItemStyle = _ref.indexItemStyle,
      indexTextStyle = _ref.indexTextStyle;
    // Calculate index data from items list
    var indexData = (0, _react.useMemo)(function () {
      if (!(data != null && data.length)) return {};
      var result = {};
      for (var i = 0; i < data.length; i++) {
        var _data$i, _itemName$;
        var itemName = (_data$i = data[i]) == null ? undefined : _data$i[nameField];
        var firstLetter = itemName == null || (_itemName$ = itemName[0]) == null || _itemName$.toLowerCase == null ? undefined : _itemName$.toLowerCase();
        if (!firstLetter) continue;
        if (!/[a-z]/.test(firstLetter)) firstLetter = "#";
        if (result[firstLetter] || result[firstLetter] === 0) continue;
        result[firstLetter] = i;
      }

      // Handle letters without data
      var alphabetValues = alphabetList.map(function (item) {
        return item.value;
      });
      var missingIndexes = [];
      for (var _i = 0; _i < alphabetValues.length; _i++) {
        var letter = alphabetValues[_i];
        if (!(0, _isNumber2.default)(result[letter])) {
          missingIndexes.push(letter);
        } else {
          // Assign previous missing indexes with current index
          for (var j = 0; j < missingIndexes.length; j++) {
            result[missingIndexes[j]] = result[letter];
          }
          missingIndexes = [];
        }
      }

      // Assign final missing indexes with last index
      if (missingIndexes.length) {
        for (var _i2 = 0; _i2 < missingIndexes.length; _i2++) {
          result[missingIndexes[_i2]] = data.length - 1;
        }
      }
      return result;
    }, [data, nameField, alphabetList]);
    var handlePressIndexSearch = (0, _react.useCallback)(function (letter) {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        // Custom tracking
        if (onTrackingAction) {
          onTrackingAction(letter);
        }
        var indexToScroll = indexData[letter];
        if (!indexToScroll && indexToScroll !== 0) return;
        onIndexPress(letter, indexToScroll);
      });
    }, [indexData, onIndexPress, onTrackingAction]);
    var handleIndexGesture = (0, _react.useCallback)(function (e) {
      var index = Math.floor(e.y / INDEX_ITEM_HEIGHT);
      if (index >= 0 && index < alphabetList.length) {
        var _alphabetList$index;
        (0, _reactNativeReanimated.runOnJS)(handlePressIndexSearch)((_alphabetList$index = alphabetList[index]) == null ? undefined : _alphabetList$index.value);
      }
    }, [handlePressIndexSearch, alphabetList]);
    var gesture = _reactNativeGestureHandler.Gesture.Pan().onBegin(handleIndexGesture).onUpdate(handleIndexGesture).runOnJS(true);
    if (!visible || !(data != null && data.length)) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureDetector, {
      gesture: _reactNativeGestureHandler.Gesture.Simultaneous(gesture, _reactNativeGestureHandler.Gesture.Native()),
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_styles.styles.alphabeticalIndexSearchContainerStyle, containerStyle],
        children: alphabetList.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: [_styles.styles.indexContainerStyle, indexItemStyle],
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: [_styles.styles.indexTextStyle, indexTextStyle],
              children: item.label
            })
          }, index);
        })
      })
    });
  };
  var _default = exports.default = AlphabeticalIndex;
