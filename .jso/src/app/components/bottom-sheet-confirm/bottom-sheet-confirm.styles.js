  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleStyle = exports.textContainerStyle = exports.rightContent = exports.nonIcon = exports.modalStyle = exports.messageStyle = exports.leftContent = exports.iconStyle = exports.hasCancelStyle = exports.contentStyle = exports.contentHeaderWithClose = exports.contentHeader = exports.containerStyle = exports.containerCloseStyle = exports.cancelButtonStyle = exports.buttonStyle = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var modalStyle = exports.modalStyle = {
    justifyContent: "flex-end",
    margin: 0
  };
  var leftContent = exports.leftContent = {
    width: "10%"
  };
  var hasCancelStyle = exports.hasCancelStyle = {
    marginTop: 10
  };
  var rightContent = exports.rightContent = {
    width: "10%"
  };
  var contentHeaderWithClose = exports.contentHeaderWithClose = {
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "space-between",
    width: "100%",
    marginTop: -35
  };
  var contentHeader = exports.contentHeader = {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginTop: -35
  };
  var containerStyle = exports.containerStyle = {
    alignItems: "center",
    backgroundColor: _theme.color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopEndRadius: 16,
    width: "100%"
  };
  var containerCloseStyle = exports.containerCloseStyle = {
    alignItems: "center",
    backgroundColor: _theme.color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopEndRadius: 16,
    flexDirection: "row",
    justifyContent: "space-between"
  };
  var contentStyle = exports.contentStyle = {
    alignItems: "center",
    overflow: "hidden",
    paddingTop: 39,
    flex: 1,
    borderTopLeftRadius: 16,
    borderTopEndRadius: 16
  };
  var nonIcon = exports.nonIcon = {
    height: 32,
    borderRadius: 50,
    zIndex: 10
  };
  var iconStyle = exports.iconStyle = {
    width: 70,
    height: 70,
    borderRadius: 50,
    zIndex: 10,
    backgroundColor: _theme.color.palette.lightPurple
  };
  var textContainerStyle = exports.textContainerStyle = {
    marginBottom: 24
  };
  var titleStyle = exports.titleStyle = Object.assign({}, _text.presets.h2, {
    lineHeight: 28,
    textAlign: "center",
    marginBottom: 16,
    paddingHorizontal: 15
  });
  var messageStyle = exports.messageStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    textAlign: "center",
    color: _theme.color.palette.almostBlackGrey,
    paddingHorizontal: 30
  });
  var buttonStyle = exports.buttonStyle = {
    width: 300,
    borderRadius: 60,
    marginBottom: 5
  };
  var cancelButtonStyle = exports.cancelButtonStyle = {
    color: _theme.color.palette.lightPurple
  };
