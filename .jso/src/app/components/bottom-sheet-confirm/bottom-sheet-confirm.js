  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BottomSheetConfirm = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _button = _$$_REQUIRE(_dependencyMap[9]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[10]);
  var _color = _$$_REQUIRE(_dependencyMap[11]);
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _lodash = _$$_REQUIRE(_dependencyMap[13]);
  var _bottomSheetConfirm = _$$_REQUIRE(_dependencyMap[14]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BottomSheetConfirm = exports.BottomSheetConfirm = function BottomSheetConfirm(props) {
    var visible = props.visible,
      title = props.title,
      message = props.message,
      _props$height = props.height,
      height = _props$height === undefined ? 300 : _props$height,
      _props$icon = props.icon,
      icon = _props$icon === undefined ? (0, _jsxRuntime.jsx)(_icons.Info, {}) : _props$icon,
      _props$confirmButtonT = props.confirmButtonText,
      confirmButtonText = _props$confirmButtonT === undefined ? (0, _i18n.translate)("popupConfirm.confirm") : _props$confirmButtonT,
      _props$cancelButtonTe = props.cancelButtonText,
      cancelButtonText = _props$cancelButtonTe === undefined ? (0, _i18n.translate)("popupConfirm.cancel") : _props$cancelButtonTe,
      isLoading = props.isLoading,
      _props$hideOnConfirm = props.hideOnConfirm,
      hideOnConfirm = _props$hideOnConfirm === undefined ? true : _props$hideOnConfirm,
      onHide = props.onHide,
      onConfirm = props.onConfirm,
      onCancel = props.onCancel,
      _props$hasCancel = props.hasCancel,
      hasCancel = _props$hasCancel === undefined ? true : _props$hasCancel,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "BottomSheetConfirm" : _props$testID,
      _props$isClose = props.isClose,
      isClose = _props$isClose === undefined ? false : _props$isClose,
      _props$customMessageS = props.customMessageStyle,
      customMessageStyle = _props$customMessageS === undefined ? {} : _props$customMessageS,
      _props$visibleIcon = props.visibleIcon,
      visibleIcon = _props$visibleIcon === undefined ? false : _props$visibleIcon,
      _props$customTitleSty = props.customTitleStyle,
      customTitleStyle = _props$customTitleSty === undefined ? {} : _props$customTitleSty,
      _props$customContaine = props.customContainerStyle,
      customContainerStyle = _props$customContaine === undefined ? {} : _props$customContaine,
      _props$customContentS = props.customContentStyle,
      customContentStyle = _props$customContentS === undefined ? {} : _props$customContentS;
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      action = _useState2[0],
      setAction = _useState2[1];
    var onCancelPress = function onCancelPress() {
      setAction(_bottomSheetConfirm.BottomSheetConfirmAction.CANCEL);
      onHide == null || onHide();
    };
    var onConfirmPress = function onConfirmPress() {
      // Default behaviour -> hide modal -> after modal hidden -> call onConfirm
      if (hideOnConfirm) {
        setAction(_bottomSheetConfirm.BottomSheetConfirmAction.CONFIRM);
        onHide == null || onHide();
        return;
      }

      // Custom behaviour
      onConfirm == null || onConfirm();
    };
    var onModalHide = function onModalHide() {
      action === _bottomSheetConfirm.BottomSheetConfirmAction.CONFIRM && (onConfirm == null ? undefined : onConfirm());
      action === _bottomSheetConfirm.BottomSheetConfirmAction.CANCEL && (onCancel == null ? undefined : onCancel());
      setAction(null);
    };
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      isVisible: visible,
      swipeDirection: "down",
      animationInTiming: 300,
      animationOutTiming: 300,
      animationIn: "slideInUp",
      animationOut: "slideOutDown",
      style: styles.modalStyle,
      onBackdropPress: function onBackdropPress() {
        return !isLoading && onCancelPress();
      },
      onSwipeComplete: function onSwipeComplete() {
        return !isLoading && onCancelPress();
      },
      onBackButtonPress: function onBackButtonPress() {
        return !isLoading && onCancelPress();
      },
      onModalHide: onModalHide,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [styles.containerStyle, {
          height: height
        }, customContainerStyle],
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: !isClose ? styles.contentHeader : styles == null ? undefined : styles.contentHeaderWithClose,
          children: [isClose && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.leftContent
          }), visibleIcon && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.nonIcon
          }), !visibleIcon && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.iconStyle,
            children: icon
          }), isClose && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.rightContent,
            children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onHide,
              children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {})
            })
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.contentStyle, customContentStyle],
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.textContainerStyle,
            children: [!(0, _lodash.isEmpty)(title) && (0, _jsxRuntime.jsx)(_text.Text, {
              style: [styles.titleStyle, customTitleStyle],
              text: title
            }), !(0, _lodash.isEmpty)(message) && (0, _jsxRuntime.jsx)(_text.Text, {
              style: [styles.messageStyle, customMessageStyle],
              text: message
            })]
          }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: styles.buttonStyle,
            start: {
              x: 1,
              y: 0
            },
            end: {
              x: 0,
              y: 1
            },
            colors: [_color.color.palette.gradientColor1Start, _color.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              sizePreset: "large",
              textPreset: "buttonLarge",
              typePreset: "secondary",
              text: confirmButtonText,
              statePreset: "default",
              backgroundPreset: "light",
              onPress: onConfirmPress,
              testID: `${testID}__ButtonConfirm`,
              accessibilityLabel: `${testID}__ButtonConfirm`
            })
          }), hasCancel && (0, _jsxRuntime.jsx)(_button.Button, {
            sizePreset: "large",
            textPreset: "buttonLarge",
            text: cancelButtonText,
            textStyle: styles.cancelButtonStyle,
            onPress: onCancelPress,
            testID: `${testID}__ButtonCancel`,
            accessibilityLabel: `${testID}__ButtonCancel`,
            style: styles.hasCancelStyle
          }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
            visible: isLoading
          })]
        })]
      })
    });
  };
