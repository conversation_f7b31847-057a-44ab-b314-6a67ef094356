  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.BACKGROUND_IMAGE_HEIGHT = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _dealsPromosCategoryListing = _$$_REQUIRE(_dependencyMap[10]);
  var _filterBottomSheet = _$$_REQUIRE(_dependencyMap[11]);
  var _alphabeticalIndex = _$$_REQUIRE(_dependencyMap[12]);
  var _dealsPromosLoadingContent = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var _excluded = ["headerImageSource", "headerTitle", "renderItem", "renderFilter", "navigation", "filterHeight", "sortBy", "listData", "perkItemOffsetListRef", "rootItemOffsetRef", "rootListRef", "customComponentLoading", "nameField", "isScrollToIndex"];
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedFlatList = _reactNativeReanimated.default.createAnimatedComponent(_reactNative2.FlatList);
  var AnimatedPath = _reactNativeReanimated.default.createAnimatedComponent(_reactNativeSvg.Path);
  var AnimatedView = _reactNativeReanimated.default.createAnimatedComponent(_reactNative2.View);
  var AnimatedText = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.Text);
  var AnimatedBackArrow = function AnimatedBackArrow(_ref) {
    var animatedProps = _ref.animatedProps;
    return (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
      width: 24,
      height: 24,
      viewBox: "0 0 24 24",
      children: (0, _jsxRuntime.jsx)(AnimatedPath, {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M4.29289 11.2929C4.10536 11.4804 4 11.7348 4 12C4 12.2652 4.10536 12.5196 4.29289 12.7071L9.29289 17.7071C9.68342 18.0976 10.3166 18.0976 10.7071 17.7071C11.0976 17.3166 11.0976 16.6834 10.7071 16.2929L7.41421 13L18 13C18.5523 13 19 12.5523 19 12C19 11.4477 18.5523 11 18 11L7.41421 11L10.7071 7.70711C11.0976 7.31658 11.0976 6.68342 10.7071 6.29289C10.3166 5.90237 9.68342 5.90237 9.29289 6.29289L4.29289 11.2929Z",
        animatedProps: animatedProps
      })
    });
  };
  var BACKGROUND_IMAGE_HEIGHT = exports.BACKGROUND_IMAGE_HEIGHT = 115;
  var _worklet_7507005047621_init_data = {
    code: "function CollapsibleHeaderTsx1(){const{scrollY,ANIMATION_SCROLL_DISTANCE}=this.__closure;return scrollY.value>ANIMATION_SCROLL_DISTANCE;}"
  };
  var _worklet_7475073774140_init_data = {
    code: "function CollapsibleHeaderTsx2(result,previous){const{runOnJS,setBarStyle}=this.__closure;if(result!==previous){runOnJS(setBarStyle)(result?\"dark-content\":\"light-content\");}}"
  };
  var _worklet_17069058376736_init_data = {
    code: "function CollapsibleHeaderTsx3(event){const{isScrollingUp,prevScrollY,scrollY}=this.__closure;const currentScrollY=event.contentOffset.y;isScrollingUp.value=currentScrollY<prevScrollY.value;prevScrollY.value=currentScrollY;scrollY.value=currentScrollY;}"
  };
  var _worklet_6024599111793_init_data = {
    code: "function CollapsibleHeaderTsx4(){const{interpolate,scrollY,headerMaxHeight,Extrapolate,ANIMATION_SCROLL_DISTANCE}=this.__closure;const height=interpolate(scrollY.value,[-headerMaxHeight,0],[headerMaxHeight*2,headerMaxHeight],Extrapolate.CLAMP);const translateY=interpolate(scrollY.value,[0,ANIMATION_SCROLL_DISTANCE],[0,-ANIMATION_SCROLL_DISTANCE],Extrapolate.CLAMP);return{height:height,transform:[{translateY:translateY}]};}"
  };
  var _worklet_12228044525114_init_data = {
    code: "function CollapsibleHeaderTsx5(){const{interpolateColor,scrollY,ANIMATION_SCROLL_DISTANCE,color,interpolate,Platform,Extrapolate}=this.__closure;const backgroundColor=interpolateColor(scrollY.value,[0,ANIMATION_SCROLL_DISTANCE],[\"transparent\",color.palette.whiteGrey]);const shadowOpacity=interpolate(scrollY.value,[0,ANIMATION_SCROLL_DISTANCE],[0,Platform.OS===\"android\"?0.16:1],Extrapolate.CLAMP);const elevation=interpolate(scrollY.value,[-1,0,ANIMATION_SCROLL_DISTANCE],[0,0,2],Extrapolate.CLAMP);return{backgroundColor:backgroundColor,elevation:elevation,shadowOpacity:shadowOpacity};}"
  };
  var _worklet_15292373476427_init_data = {
    code: "function CollapsibleHeaderTsx6(){const{interpolateColor,scrollY,ANIMATION_SCROLL_DISTANCE,color}=this.__closure;const textColor=interpolateColor(scrollY.value,[0,ANIMATION_SCROLL_DISTANCE],[color.palette.whiteGrey,color.palette.darkestGrey]);return{color:textColor};}"
  };
  var _worklet_7019521846234_init_data = {
    code: "function CollapsibleHeaderTsx7(){const{interpolate,scrollY,ANIMATION_SCROLL_DISTANCE,headerMaxHeight,Extrapolate,isScrollingUp,filterHeight}=this.__closure;const translateY=interpolate(scrollY.value,[0,ANIMATION_SCROLL_DISTANCE],[headerMaxHeight,headerMaxHeight-ANIMATION_SCROLL_DISTANCE],Extrapolate.CLAMP);let paddingTop=0;let paddingBottom=0;if(!isScrollingUp.value){paddingTop=scrollY.value>=ANIMATION_SCROLL_DISTANCE?24:0;paddingBottom=scrollY.value>=ANIMATION_SCROLL_DISTANCE?8:0;}else{paddingTop=scrollY.value<32?0:24;paddingBottom=scrollY.value<32?0:8;}return{height:filterHeight+paddingTop+paddingBottom,paddingTop:paddingTop,paddingBottom:paddingBottom,transform:[{translateY:translateY}]};}"
  };
  var _worklet_3835583022103_init_data = {
    code: "function CollapsibleHeaderTsx8(){const{interpolateColor,scrollY,ANIMATION_SCROLL_DISTANCE,color}=this.__closure;const fill=interpolateColor(scrollY.value,[0,ANIMATION_SCROLL_DISTANCE],[color.palette.whiteGrey,color.palette.darkestGrey]);return{fill:fill};}"
  };
  var CollapsibleHeader = function CollapsibleHeader(_ref2) {
    var headerImageSource = _ref2.headerImageSource,
      headerTitle = _ref2.headerTitle,
      renderItem = _ref2.renderItem,
      renderFilter = _ref2.renderFilter,
      navigation = _ref2.navigation,
      _ref2$filterHeight = _ref2.filterHeight,
      filterHeight = _ref2$filterHeight === undefined ? 60 : _ref2$filterHeight,
      sortBy = _ref2.sortBy,
      listData = _ref2.listData,
      perkItemOffsetListRef = _ref2.perkItemOffsetListRef,
      rootItemOffsetRef = _ref2.rootItemOffsetRef,
      rootListRef = _ref2.rootListRef,
      customComponentLoading = _ref2.customComponentLoading,
      nameField = _ref2.nameField,
      _ref2$isScrollToIndex = _ref2.isScrollToIndex,
      isScrollToIndex = _ref2$isScrollToIndex === undefined ? false : _ref2$isScrollToIndex,
      rest = (0, _objectWithoutProperties2.default)(_ref2, _excluded);
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var prevScrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var isScrollingUp = (0, _reactNativeReanimated.useSharedValue)(false);
    var hasError = rest.hasError,
      renderError = rest.renderError,
      isLoading = rest.isLoading;
    var ANIMATION_SCROLL_DISTANCE = 45;
    var headerMaxHeight = BACKGROUND_IMAGE_HEIGHT + _styles.FILTER_BORDER_RADIUS;
    var headerMinHeight = 100;
    var perkItemAccumulateHeights = (0, _react.useMemo)(function () {
      if (!(listData != null && listData.length) || !(perkItemOffsetListRef != null && perkItemOffsetListRef.current)) return [];
      return listData.reduce(function (result, _item, index) {
        var totalHeights = 0;
        for (var j = 0; j < index; j++) {
          totalHeights += perkItemOffsetListRef.current[j] || 0;
        }
        return result.concat(totalHeights);
      }, []);
    }, [JSON.stringify(listData), perkItemOffsetListRef == null ? undefined : perkItemOffsetListRef.current, Object.keys((perkItemOffsetListRef == null ? undefined : perkItemOffsetListRef.current) || {}).length]);
    var handleAlphabetIndexPress = (0, _react.useCallback)(function (letter, indexToScroll) {
      if (indexToScroll === 0) {
        var _rootListRef$current;
        rootListRef == null || (_rootListRef$current = rootListRef.current) == null || _rootListRef$current.scrollToOffset({
          animated: false,
          offset: 0
        });
        return;
      }
      if (isScrollToIndex) {
        var _rootListRef$current2;
        var index = indexToScroll > 1 ? indexToScroll - 1 : 0;
        rootListRef == null || (_rootListRef$current2 = rootListRef.current) == null || _rootListRef$current2.scrollToIndex({
          index: index,
          animated: false
        });
      } else {
        var _rootListRef$current3;
        var totalPerkItemHeights = perkItemAccumulateHeights[indexToScroll] || 0;

        // Calculate the correct offset considering the contentContainerStyle padding
        var contentPaddingTop = headerMaxHeight + filterHeight - 4;

        // Account for the filter padding to ensure item is not covered by sticky filter bar
        // Filter bar has padding (24 top + 8 bottom = 32) when in sticky mode
        var dynamicFilterPadding = 32;

        // headerMinHeight is the height of the header when scrolled
        // filterHeight is the base height of the filter bar
        // dynamicFilterPadding accounts for the extra padding added when scrolled
        var stickyHeaderHeight = headerMinHeight + filterHeight + dynamicFilterPadding;
        // Add extra padding to ensure the item is fully visible below the sticky header
        var extraPadding = 16;
        var offset = totalPerkItemHeights + contentPaddingTop - stickyHeaderHeight + extraPadding;
        rootListRef == null || (_rootListRef$current3 = rootListRef.current) == null || _rootListRef$current3.scrollToOffset({
          animated: false,
          offset: offset,
          viewPosition: 0
        });
      }
    }, [perkItemAccumulateHeights, rootListRef, headerMaxHeight, filterHeight, headerMinHeight]);
    var handleAlphabetTracking = (0, _react.useCallback)(function (letter) {}, []);
    var setBarStyle = (0, _react.useCallback)(function (style) {
      _reactNative2.StatusBar.setBarStyle(style, true);
    }, []);
    (0, _reactNativeReanimated.useAnimatedReaction)(function () {
      var CollapsibleHeaderTsx1 = function CollapsibleHeaderTsx1() {
        return scrollY.value > ANIMATION_SCROLL_DISTANCE;
      };
      CollapsibleHeaderTsx1.__closure = {
        scrollY: scrollY,
        ANIMATION_SCROLL_DISTANCE: ANIMATION_SCROLL_DISTANCE
      };
      CollapsibleHeaderTsx1.__workletHash = 7507005047621;
      CollapsibleHeaderTsx1.__initData = _worklet_7507005047621_init_data;
      return CollapsibleHeaderTsx1;
    }(), function () {
      var CollapsibleHeaderTsx2 = function CollapsibleHeaderTsx2(result, previous) {
        if (result !== previous) {
          (0, _reactNativeReanimated.runOnJS)(setBarStyle)(result ? "dark-content" : "light-content");
        }
      };
      CollapsibleHeaderTsx2.__closure = {
        runOnJS: _reactNativeReanimated.runOnJS,
        setBarStyle: setBarStyle
      };
      CollapsibleHeaderTsx2.__workletHash = 7475073774140;
      CollapsibleHeaderTsx2.__initData = _worklet_7475073774140_init_data;
      return CollapsibleHeaderTsx2;
    }(), []);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      setBarStyle("light-content");
      if (_reactNative2.Platform.OS === "android") {
        _reactNative2.StatusBar.setTranslucent(true);
        _reactNative2.StatusBar.setBackgroundColor("transparent", true);
      }
      return function () {
        setBarStyle("dark-content");
        if (_reactNative2.Platform.OS === "android") {
          _reactNative2.StatusBar.setTranslucent(false);
          _reactNative2.StatusBar.setBackgroundColor(_theme.color.background, true);
        }
      };
    }, [setBarStyle]));
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var CollapsibleHeaderTsx3 = function CollapsibleHeaderTsx3(event) {
          var currentScrollY = event.contentOffset.y;
          isScrollingUp.value = currentScrollY < prevScrollY.value;
          prevScrollY.value = currentScrollY;
          scrollY.value = currentScrollY;
        };
        CollapsibleHeaderTsx3.__closure = {
          isScrollingUp: isScrollingUp,
          prevScrollY: prevScrollY,
          scrollY: scrollY
        };
        CollapsibleHeaderTsx3.__workletHash = 17069058376736;
        CollapsibleHeaderTsx3.__initData = _worklet_17069058376736_init_data;
        return CollapsibleHeaderTsx3;
      }()
    });
    var headerImageAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var CollapsibleHeaderTsx4 = function CollapsibleHeaderTsx4() {
        var height = (0, _reactNativeReanimated.interpolate)(scrollY.value, [-headerMaxHeight, 0], [headerMaxHeight * 2, headerMaxHeight], _reactNativeReanimated.Extrapolate.CLAMP);
        var translateY = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, ANIMATION_SCROLL_DISTANCE], [0, -45], _reactNativeReanimated.Extrapolate.CLAMP);
        return {
          height: height,
          transform: [{
            translateY: translateY
          }]
        };
      };
      CollapsibleHeaderTsx4.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        headerMaxHeight: headerMaxHeight,
        Extrapolate: _reactNativeReanimated.Extrapolate,
        ANIMATION_SCROLL_DISTANCE: ANIMATION_SCROLL_DISTANCE
      };
      CollapsibleHeaderTsx4.__workletHash = 6024599111793;
      CollapsibleHeaderTsx4.__initData = _worklet_6024599111793_init_data;
      return CollapsibleHeaderTsx4;
    }());
    var headerBarAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var CollapsibleHeaderTsx5 = function CollapsibleHeaderTsx5() {
        var backgroundColor = (0, _reactNativeReanimated.interpolateColor)(scrollY.value, [0, ANIMATION_SCROLL_DISTANCE], ["transparent", _theme.color.palette.whiteGrey]);
        var shadowOpacity = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, ANIMATION_SCROLL_DISTANCE], [0, _reactNative2.Platform.OS === "android" ? 0.16 : 1], _reactNativeReanimated.Extrapolate.CLAMP);
        var elevation = (0, _reactNativeReanimated.interpolate)(scrollY.value, [-1, 0, ANIMATION_SCROLL_DISTANCE], [0, 0, 2], _reactNativeReanimated.Extrapolate.CLAMP);
        return {
          backgroundColor: backgroundColor,
          elevation: elevation,
          shadowOpacity: shadowOpacity
        };
      };
      CollapsibleHeaderTsx5.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        scrollY: scrollY,
        ANIMATION_SCROLL_DISTANCE: ANIMATION_SCROLL_DISTANCE,
        color: _theme.color,
        interpolate: _reactNativeReanimated.interpolate,
        Platform: _reactNative2.Platform,
        Extrapolate: _reactNativeReanimated.Extrapolate
      };
      CollapsibleHeaderTsx5.__workletHash = 12228044525114;
      CollapsibleHeaderTsx5.__initData = _worklet_12228044525114_init_data;
      return CollapsibleHeaderTsx5;
    }());
    var titleAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var CollapsibleHeaderTsx6 = function CollapsibleHeaderTsx6() {
        var textColor = (0, _reactNativeReanimated.interpolateColor)(scrollY.value, [0, ANIMATION_SCROLL_DISTANCE], [_theme.color.palette.whiteGrey, _theme.color.palette.darkestGrey]);
        return {
          color: textColor
        };
      };
      CollapsibleHeaderTsx6.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        scrollY: scrollY,
        ANIMATION_SCROLL_DISTANCE: ANIMATION_SCROLL_DISTANCE,
        color: _theme.color
      };
      CollapsibleHeaderTsx6.__workletHash = 15292373476427;
      CollapsibleHeaderTsx6.__initData = _worklet_15292373476427_init_data;
      return CollapsibleHeaderTsx6;
    }());
    var filterContainerAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var CollapsibleHeaderTsx7 = function CollapsibleHeaderTsx7() {
        var translateY = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, ANIMATION_SCROLL_DISTANCE], [headerMaxHeight, headerMaxHeight - ANIMATION_SCROLL_DISTANCE], _reactNativeReanimated.Extrapolate.CLAMP);
        var paddingTop = 0;
        var paddingBottom = 0;
        if (!isScrollingUp.value) {
          // Scroll down
          paddingTop = scrollY.value >= ANIMATION_SCROLL_DISTANCE ? 24 : 0;
          paddingBottom = scrollY.value >= ANIMATION_SCROLL_DISTANCE ? 8 : 0;
        } else {
          // Scroll up
          paddingTop = scrollY.value < 32 ? 0 : 24;
          paddingBottom = scrollY.value < 32 ? 0 : 8;
        }
        return {
          height: filterHeight + paddingTop + paddingBottom,
          paddingTop: paddingTop,
          paddingBottom: paddingBottom,
          transform: [{
            translateY: translateY
          }]
        };
      };
      CollapsibleHeaderTsx7.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        ANIMATION_SCROLL_DISTANCE: ANIMATION_SCROLL_DISTANCE,
        headerMaxHeight: headerMaxHeight,
        Extrapolate: _reactNativeReanimated.Extrapolate,
        isScrollingUp: isScrollingUp,
        filterHeight: filterHeight
      };
      CollapsibleHeaderTsx7.__workletHash = 7019521846234;
      CollapsibleHeaderTsx7.__initData = _worklet_7019521846234_init_data;
      return CollapsibleHeaderTsx7;
    }());
    var backArrowProps = (0, _reactNativeReanimated.useAnimatedProps)(function () {
      var CollapsibleHeaderTsx8 = function CollapsibleHeaderTsx8() {
        var fill = (0, _reactNativeReanimated.interpolateColor)(scrollY.value, [0, ANIMATION_SCROLL_DISTANCE], [_theme.color.palette.whiteGrey, _theme.color.palette.darkestGrey]);
        return {
          fill: fill
        };
      };
      CollapsibleHeaderTsx8.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        scrollY: scrollY,
        ANIMATION_SCROLL_DISTANCE: ANIMATION_SCROLL_DISTANCE,
        color: _theme.color
      };
      CollapsibleHeaderTsx8.__workletHash = 3835583022103;
      CollapsibleHeaderTsx8.__initData = _worklet_3835583022103_init_data;
      return CollapsibleHeaderTsx8;
    }(), [scrollY.value]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [_styles.styles.headerImageContainer, headerImageAnimatedStyle],
        children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
          source: headerImageSource,
          style: [_styles.styles.backgroundImage, {
            height: headerMaxHeight
          }],
          resizeMode: "cover"
        })
      }), isLoading ? (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: customComponentLoading ? customComponentLoading : (0, _jsxRuntime.jsx)(_dealsPromosLoadingContent.DealsPromosLoading, {
          containerStyle: {
            marginTop: BACKGROUND_IMAGE_HEIGHT,
            paddingTop: 80,
            borderTopLeftRadius: _styles.FILTER_BORDER_RADIUS,
            borderTopRightRadius: _styles.FILTER_BORDER_RADIUS,
            height: "100%",
            overflow: "hidden",
            backgroundColor: _theme.color.palette.whiteGrey
          }
        })
      }) : hasError && renderError ? renderError() : (0, _jsxRuntime.jsx)(AnimatedFlatList, Object.assign({}, rest, {
        ref: rootListRef,
        onScroll: scrollHandler,
        scrollEventThrottle: 16,
        renderItem: renderItem,
        contentContainerStyle: {
          paddingTop: headerMaxHeight + filterHeight - 4
        }
      })), (0, _jsxRuntime.jsxs)(AnimatedView, {
        style: [_styles.styles.headerBar, {
          height: headerMinHeight,
          paddingTop: 45
        }, headerBarAnimatedStyle],
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return navigation == null ? undefined : navigation.goBack();
          },
          style: _styles.styles.backBtn,
          children: (0, _jsxRuntime.jsx)(AnimatedBackArrow, {
            animatedProps: backArrowProps
          })
        }), (0, _jsxRuntime.jsx)(AnimatedText, {
          style: [_styles.styles.title, titleAnimatedStyle],
          children: headerTitle
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.placeholder
        })]
      }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [_styles.styles.filterContainer, filterContainerAnimatedStyle],
        children: renderFilter()
      }), (0, _jsxRuntime.jsx)(_alphabeticalIndex.AlphabeticalIndex, {
        data: listData || [],
        nameField: nameField ? nameField : "tenantName",
        alphabetList: _dealsPromosCategoryListing.ALPHABETICAL_INDEX_LIST,
        onIndexPress: handleAlphabetIndexPress,
        itemAccumulateHeights: perkItemAccumulateHeights,
        visible: sortBy === _filterBottomSheet.SortBy.AZ && !!(listData != null && listData.length),
        onTrackingAction: handleAlphabetTracking
      })]
    });
  };
  var _default = exports.default = CollapsibleHeader;
