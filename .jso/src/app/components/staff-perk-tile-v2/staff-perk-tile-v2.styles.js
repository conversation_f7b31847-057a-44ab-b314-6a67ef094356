  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey,
      flexDirection: "row",
      paddingBottom: 16,
      marginHorizontal: 20,
      marginTop: 0,
      paddingTop: 12,
      width: width - 40
    },
    firstItemContainerStyle: {
      paddingTop: 0
    },
    lastItemContainerStyle: {
      borderBottomWidth: 0
    },
    imageStyle: {
      borderRadius: 4,
      height: 80,
      marginRight: 16,
      width: 120
    },
    contentContainerStyle: {
      flex: 1
    },
    tenantNameTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.lightPurple,
      marginBottom: 4
    }),
    perkLabelContainerStyle: {
      alignItems: "center",
      color: _theme.color.palette.lightOrange,
      flexDirection: "row",
      marginBottom: 4,
      paddingVertical: 1
    },
    perkLabelTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.lightOrange,
      marginLeft: 4.5,
      textTransform: "none"
    }),
    titleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginBottom: 4
    }),
    startDateTextStyle: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.darkestGrey
    })
  });
