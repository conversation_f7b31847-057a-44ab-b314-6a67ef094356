  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _textGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var CardItem = function CardItem(_ref) {
    var cardItem = _ref.cardItem,
      index = _ref.index,
      itemCardOnpress = _ref.itemCardOnpress;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.itemCardContainer,
      onPress: function onPress() {
        return itemCardOnpress(cardItem);
      },
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _screenHelper.getUriImage)(cardItem == null ? undefined : cardItem.icon)
        },
        style: styles.cardIconStyles
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.rightContentContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: cardItem == null ? undefined : cardItem.title,
          preset: "bodyTextBold",
          numberOfLines: 2
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: cardItem == null ? undefined : cardItem.caption,
          preset: "caption1Regular",
          style: styles.captionTextStyles,
          numberOfLines: 2
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: {
            flexDirection: "row",
            justifyContent: "center"
          },
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.discountContainer,
            children: (0, _jsxRuntime.jsx)(_textGradient.default, {
              style: styles.discountTextStyles,
              locations: [0, 1],
              colors: ["#018FBB", "#CDD52D"],
              start: {
                x: 0,
                y: 0
              },
              end: {
                x: 1,
                y: 0
              },
              text: cardItem == null ? undefined : cardItem.discountPill,
              numberOfLines: 2
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.flex1
          })]
        })]
      })]
    }, index);
  };
  var styles = _reactNative2.StyleSheet.create({
    flex1: {
      flex: 1
    },
    itemCardContainer: {
      flexDirection: "row",
      borderRadius: 12,
      marginTop: 12,
      marginHorizontal: 4,
      borderTopWidth: 1,
      borderRightWidth: 1,
      borderLeftWidth: 1,
      backgroundColor: "rgba(255, 255, 255, 0.15)",
      borderColor: "rgba(255, 255, 255, 0.5)",
      shadowOffset: {
        width: 0,
        height: 6
      },
      shadowOpacity: 0.08,
      shadowRadius: 20,
      padding: 16,
      marginVertical: 8,
      alignItems: "center"
    },
    rightContentContainer: {
      flex: 1,
      marginLeft: 12
    },
    captionTextStyles: {
      color: _theme.color.palette.almostWhiteGrey80,
      marginVertical: 4
    },
    discountContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      alignItems: "center"
    },
    discountTextStyles: {
      paddingHorizontal: 12,
      paddingVertical: 4,
      fontFamily: _theme.typography.bold,
      fontSize: 12,
      lineHeight: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      })
    },
    cardIconStyles: {
      width: 80,
      height: 80
    }
  });
  var _default = exports.default = CardItem;
