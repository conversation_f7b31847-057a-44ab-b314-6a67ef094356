  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TabBar = TabBar;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _tabBarButton = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _tabBarButton2 = _$$_REQUIRE(_dependencyMap[6]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var containerStyle = {
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "flex-start",
    flexDirection: "row",
    marginTop: 16
  };
  var itemStyle = {
    marginRight: 28
  };
  var renderTabBarButton = function renderTabBarButton(_ref) {
    var item = _ref.item,
      index = _ref.index,
      onItemSelected = _ref.onItemSelected,
      testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: itemStyle,
      children: (0, _jsxRuntime.jsx)(_tabBarButton.default, {
        text: item.text,
        type: item.type,
        onPressed: function onPressed() {
          return onItemSelected(item);
        },
        testID: `${testID}__TabBarButton`,
        accessibilityLabel: `${accessibilityLabel}__TabBarButton`
      })
    }, index);
  };
  function TabBar(props) {
    var tabBarData = props.tabBarData,
      onTabBarItemSelected = props.onTabBarItemSelected,
      tabStyle = props.tabStyle,
      contentStyle = props.contentStyle,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "TabBar" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "TabBar" : _props$accessibilityL,
      _props$scrollEnabled = props.scrollEnabled,
      scrollEnabled = _props$scrollEnabled === undefined ? true : _props$scrollEnabled;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _React$useState = React.useState(tabBarData || []),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      dataSource = _React$useState2[0],
      setDataSource = _React$useState2[1];
    var _React$useState3 = React.useState(0),
      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
      sIndex = _React$useState4[0],
      setSIndex = _React$useState4[1];
    var categorySelected = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreCategorySelected);
    var selectedExploreCategoryItem = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreCategoriesData);
    var flatListRef = React.useRef(null);
    React.useEffect(function () {
      if (categorySelected && !(0, _lodash.isEmpty)(dataSource)) {
        var _selectedExploreCateg, _flatListRef$current;
        var indexCategory = selectedExploreCategoryItem == null || (_selectedExploreCateg = selectedExploreCategoryItem.data) == null ? undefined : _selectedExploreCateg.findIndex(function (e) {
          return (e == null ? undefined : e.text) === categorySelected;
        });
        var newIndexCategory = indexCategory < 0 ? 0 : indexCategory;
        setSIndex(newIndexCategory);
        flatListRef == null || (_flatListRef$current = flatListRef.current) == null || _flatListRef$current.scrollToIndex({
          index: newIndexCategory,
          animated: true,
          viewPosition: 0.5
        });
        dispatch(_exploreRedux.default.resetExploreCategorySelected());
      }
    }, [categorySelected]);
    React.useEffect(function () {
      setDataSource(tabBarData);
    }, [tabBarData]);
    var onItemSelected = function onItemSelected(item) {
      if (item.type === _tabBarButton2.TabBarButtonType.selected) return;
      var clonedDataSource = (0, _toConsumableArray2.default)(dataSource);
      item.type = _tabBarButton2.TabBarButtonType.selected;
      clonedDataSource.forEach(function (element, idx) {
        clonedDataSource[idx] = Object.assign({}, element, {
          type: _tabBarButton2.TabBarButtonType.unSelected
        });
      });
      var index = clonedDataSource.findIndex(function (data) {
        return data.id === item.id;
      });
      clonedDataSource[index] = item;
      setDataSource((0, _toConsumableArray2.default)(clonedDataSource));
      onTabBarItemSelected == null || onTabBarItemSelected(item);
    };
    return (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      contentContainerStyle: [containerStyle, contentStyle],
      style: tabStyle,
      data: dataSource,
      scrollEnabled: scrollEnabled,
      renderItem: function renderItem(_ref2) {
        var item = _ref2.item,
          index = _ref2.index;
        return renderTabBarButton({
          item: item,
          index: index,
          onItemSelected: onItemSelected,
          testID: testID,
          accessibilityLabel: accessibilityLabel
        });
      },
      horizontal: true,
      showsHorizontalScrollIndicator: false,
      keyExtractor: function keyExtractor(_, index) {
        return `RenderTabBarButton-${index.toString()}`;
      },
      testID: `${testID}__FlatList`,
      accessibilityLabel: `${accessibilityLabel}__FlatList`,
      ref: flatListRef,
      initialScrollIndex: sIndex,
      onScrollToIndexFailed: function onScrollToIndexFailed(_ref3) {
        var index = _ref3.index,
          averageItemLength = _ref3.averageItemLength;
        var wait = new Promise(function (resolve) {
          return setTimeout(resolve, 500);
        });
        wait.then(function () {
          var _flatListRef$current2;
          (_flatListRef$current2 = flatListRef.current) == null || _flatListRef$current2.scrollToOffset({
            offset: index * averageItemLength,
            animated: true
          });
        });
      }
    });
  }
