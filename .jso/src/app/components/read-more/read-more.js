  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var ReadMore = exports.default = /*#__PURE__*/function (_React$Component) {
    function ReadMore() {
      var _this;
      (0, _classCallCheck2.default)(this, ReadMore);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, ReadMore, [].concat(args));
      _this.state = {
        measured: false,
        shouldShowReadMore: false,
        showAllText: false
      };
      _this._handlePressReadMore = function () {
        _this.setState({
          showAllText: true
        });
      };
      _this._handlePressReadLess = function () {
        _this.setState({
          showAllText: false
        });
      };
      return _this;
    }
    (0, _inherits2.default)(ReadMore, _React$Component);
    return (0, _createClass2.default)(ReadMore, [{
      key: "componentDidMount",
      value: function () {
        var _componentDidMount = (0, _asyncToGenerator2.default)(function* () {
          var _this2 = this;
          this._isMounted = true;
          yield nextFrameAsync();
          if (!this._isMounted) {
            return;
          }

          // Get the height of the text with no restriction on number of lines
          var fullHeight = yield measureHeightAsync(this._text);
          this.setState({
            measured: true
          });
          yield nextFrameAsync();
          if (!this._isMounted) {
            return;
          }

          // Get the height of the text now that number of lines has been set
          var limitedHeight = yield measureHeightAsync(this._text);
          if (fullHeight > limitedHeight) {
            this.setState({
              shouldShowReadMore: true
            }, function () {
              _this2.props.onReady && _this2.props.onReady();
            });
          } else {
            this.props.onReady && this.props.onReady();
          }
        });
        function componentDidMount() {
          return _componentDidMount.apply(this, arguments);
        }
        return componentDidMount;
      }()
    }, {
      key: "componentWillUnmount",
      value: function componentWillUnmount() {
        this._isMounted = false;
      }
    }, {
      key: "render",
      value: function render() {
        var _this3 = this;
        var _this$state = this.state,
          measured = _this$state.measured,
          showAllText = _this$state.showAllText;
        var numberOfLines = this.props.numberOfLines;
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
            numberOfLines: measured && !showAllText ? numberOfLines : 0,
            style: this.props.textStyle,
            ref: function ref(text) {
              _this3._text = text;
            },
            allowFontScaling: false,
            children: this.props.children
          }), this._maybeRenderReadMore()]
        });
      }
    }, {
      key: "_maybeRenderReadMore",
      value: function _maybeRenderReadMore() {
        var _this$state2 = this.state,
          shouldShowReadMore = _this$state2.shouldShowReadMore,
          showAllText = _this$state2.showAllText;
        if (shouldShowReadMore && !showAllText) {
          if (this.props.renderTruncatedFooter) {
            return this.props.renderTruncatedFooter(this._handlePressReadMore);
          }
          return (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.button,
            onPress: this._handlePressReadMore,
            allowFontScaling: false,
            children: "Read more"
          });
        } else if (shouldShowReadMore && showAllText) {
          if (this.props.renderRevealedFooter) {
            return this.props.renderRevealedFooter(this._handlePressReadLess);
          }
          return (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.button,
            onPress: this._handlePressReadLess,
            allowFontScaling: false,
            children: "Hide"
          });
        }
      }
    }]);
  }(_react.default.Component);
  function measureHeightAsync(component) {
    return new Promise(function (resolve) {
      component.measure(function (_x, _y, _w, h) {
        resolve(h);
      });
    });
  }
  function nextFrameAsync() {
    return new Promise(function (resolve) {
      return requestAnimationFrame(function () {
        return resolve();
      });
    });
  }
  var styles = _reactNative2.StyleSheet.create({
    button: {
      color: "#888",
      marginTop: 5
    }
  });
