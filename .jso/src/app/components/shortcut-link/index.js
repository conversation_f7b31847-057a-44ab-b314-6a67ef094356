  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _shortcutLink = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_shortcutLink).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _shortcutLink[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _shortcutLink[key];
      }
    });
  });
  var _shortcutLink2 = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_shortcutLink2).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _shortcutLink2[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _shortcutLink2[key];
      }
    });
  });
