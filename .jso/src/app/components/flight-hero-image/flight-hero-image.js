  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightHeroImage = FlightHeroImage;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _color = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _flightHeroImage = _$$_REQUIRE(_dependencyMap[10]);
  var _flight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _multiLegFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNativeDashedLine = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _envParams = _$$_REQUIRE(_dependencyMap[14]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[15]);
  var _utils = _$$_REQUIRE(_dependencyMap[16]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var gradientColors = ["rgba(83, 83, 83, 0.2)", "rgba(0, 0, 0, 0.66)"];
  var width = _reactNative.Dimensions.get("window").width;
  var containerStyle = {
    flex: 1,
    height: 250
  };
  var twoLegViewContainer = {
    flex: 1,
    flexDirection: "row",
    marginLeft: 40,
    marginRight: 40,
    marginTop: 100
  };
  var twoLegSourceView = {
    flex: 1,
    width: width / 3
  };
  var cityTextColor = {
    color: _color.color.palette.whiteGrey
  };
  var sourceCityText = {
    color: _color.color.palette.whiteGrey
  };
  var flightIconView = {};
  var leftDashView = {
    width: 32,
    right: 30,
    top: 10
  };
  var rightDashView = {
    width: 32,
    left: 30,
    bottom: 10
  };
  var iconContainer = {
    alignItems: "center",
    marginTop: 24,
    width: 90
  };
  var twoLegDestinationView = Object.assign({}, twoLegSourceView, {
    alignItems: "flex-end"
  });
  var dashLineStyle = {
    borderRadius: 1
  };
  var twoLegcityTextStyle = {
    color: _color.color.palette.whiteGrey,
    textAlign: "right"
  };
  var threeLegViewContainer = {
    flex: 1,
    flexDirection: "row",
    marginLeft: 15,
    marginRight: 15,
    marginTop: 100
  };
  var threeLegSourceView = {
    flex: 1,
    width: width / 3,
    flexDirection: "column"
  };
  var threeLegCenterView = Object.assign({}, threeLegSourceView, {
    width: width / 3
  });
  var threeLegDestinationView = {
    flex: 1,
    alignItems: "flex-end",
    flexDirection: "column",
    width: width / 3
  };
  var smallFlightIconView = {
    marginTop: 12,
    width: width / 8
  };
  var smallFlightRightIconView = {
    marginTop: 12,
    marginLeft: 15
  };
  var fourLegViewContainer = {
    flex: 1,
    flexDirection: "row",
    marginLeft: 16,
    marginRight: 16,
    marginTop: 120
  };
  var fourLegSourceView = {
    flex: 1,
    width: width / 4,
    flexDirection: "column"
  };
  var leftFourLegFlightIconView = {
    marginTop: 12,
    marginRight: 12
  };
  var fourLegFlightIconView = Object.assign({}, leftFourLegFlightIconView);
  var rightFourLegFlightIconView = {
    marginTop: 12
  };
  var loadingContainerStyle = {
    flexDirection: "row",
    height: 274,
    backgroundColor: _color.color.palette.midGrey
  };
  var fourLegFirstStageView = {
    flex: 1,
    width: width / 4,
    flexDirection: "column"
  };
  var fourLegSecondStageView = Object.assign({}, fourLegFirstStageView);
  var fourLegDestinationView = Object.assign({}, fourLegFirstStageView, {
    alignItems: "flex-end"
  });
  var lighterGreyLoadingColors = [_color.color.palette.lighterGrey, _color.color.background, _color.color.palette.lighterGrey];
  var loadingFlightIconContainer = {
    flexDirection: "column",
    alignItems: "center",
    marginTop: 131
  };
  var headerStyle = {
    color: _color.color.palette.whiteGrey
  };
  var leftLoadingElementsLayout = [{
    width: 48,
    height: 22,
    borderRadius: 4,
    marginLeft: 35
  }, {
    width: 62,
    height: 13,
    borderRadius: 4,
    marginLeft: 35,
    marginTop: 10
  }, {
    width: 48,
    height: 22,
    borderRadius: 4,
    marginRight: 40,
    left: 14
  }, {
    width: 62,
    height: 13,
    borderRadius: 4,
    marginTop: 10,
    marginRight: 40,
    left: 0
  }];
  var loadingViewFirstColumn = {
    flex: 1,
    marginTop: 120
  };
  var loadingViewLastColumn = {
    marginTop: 120
  };
  var loadingViewLastColumnContainer = {
    flex: 1,
    alignItems: "flex-end"
  };
  var combinationDashWithFlightIcon = function combinationDashWithFlightIcon() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: leftDashView,
        children: dashView()
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: flightIconView,
        children: (0, _jsxRuntime.jsx)(_flight.default, {})
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: rightDashView,
        children: dashView()
      })]
    });
  };
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: loadingContainerStyle,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: loadingViewFirstColumn,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: leftLoadingElementsLayout[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: leftLoadingElementsLayout[1]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: loadingFlightIconContainer,
        children: combinationDashWithFlightIcon()
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: loadingViewLastColumnContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: loadingViewLastColumn,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: leftLoadingElementsLayout[2]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: leftLoadingElementsLayout[3]
          })]
        })
      })]
    });
  };
  var defaultView = function defaultView(imageUrl, travelInfo, flightNumber, wrapHeaderStyles) {
    return (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
      source: {
        uri: imageUrl
      },
      imageStyle: containerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: wrapHeaderStyles,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "subTitleBold",
          style: headerStyle,
          children: `Flight ${flightNumber}`
        })
      }), travelInfo ? (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
        colors: gradientColors,
        style: containerStyle,
        children: [(0, _utils.handleCondition)(travelInfo.length === 2, twoLegFlightInfo(travelInfo), null), (0, _utils.handleCondition)(travelInfo.length === 3, threeLegFlightInfo(travelInfo), null), (0, _utils.handleCondition)(travelInfo.length === 4, fourLegFlightInfo(travelInfo), null)]
      }) : null]
    });
  };
  var dashView = function dashView() {
    return (0, _jsxRuntime.jsx)(_reactNativeDashedLine.default, {
      dashLength: 3,
      dashThickness: 2,
      dashGap: 2,
      dashColor: _color.color.palette.whiteGrey,
      dashStyle: dashLineStyle
    });
  };
  var twoLegFlightInfo = function twoLegFlightInfo(travelInfo) {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: twoLegViewContainer,
      children: [travelInfo[0] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: twoLegSourceView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[0].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: sourceCityText,
          preset: "caption1Regular",
          children: travelInfo[0].city
        })]
      }) : null, (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: iconContainer,
        children: combinationDashWithFlightIcon()
      }), travelInfo[1] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: twoLegDestinationView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[1].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: twoLegcityTextStyle,
          preset: "caption1Regular",
          children: travelInfo[1].city
        })]
      }) : null]
    });
  };
  var threeLegFlightInfo = function threeLegFlightInfo(travelInfo) {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: threeLegViewContainer,
      children: [travelInfo[0] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: threeLegSourceView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[0].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: cityTextColor,
          preset: "caption1Regular",
          children: travelInfo[0].city
        })]
      }) : null, (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: smallFlightIconView,
        children: (0, _jsxRuntime.jsx)(_multiLegFlight.default, {})
      }), travelInfo[1] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: threeLegCenterView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[1].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: cityTextColor,
          preset: "caption1Regular",
          children: travelInfo[1].city
        })]
      }) : null, (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: smallFlightRightIconView,
        children: (0, _jsxRuntime.jsx)(_multiLegFlight.default, {})
      }), travelInfo[2] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: threeLegDestinationView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[2].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: twoLegcityTextStyle,
          preset: "caption1Regular",
          children: travelInfo[2].city
        })]
      }) : null]
    });
  };
  var fourLegFlightInfo = function fourLegFlightInfo(travelInfo) {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: fourLegViewContainer,
      children: [travelInfo[0] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: fourLegSourceView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[0].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: cityTextColor,
          preset: "caption1Regular",
          children: travelInfo[0].city
        })]
      }) : null, (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: leftFourLegFlightIconView,
        children: (0, _jsxRuntime.jsx)(_multiLegFlight.default, {})
      }), travelInfo[1] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: fourLegFirstStageView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[1].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: cityTextColor,
          preset: "caption1Regular",
          children: travelInfo[1].city
        })]
      }) : null, (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: fourLegFlightIconView,
        children: (0, _jsxRuntime.jsx)(_multiLegFlight.default, {})
      }), travelInfo[2] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: fourLegSecondStageView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[2].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: cityTextColor,
          preset: "caption1Regular",
          children: travelInfo[2].city
        })]
      }) : null, (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: rightFourLegFlightIconView,
        children: (0, _jsxRuntime.jsx)(_multiLegFlight.default, {})
      }), travelInfo[3] ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: fourLegDestinationView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h1",
          children: travelInfo[3].airportCode
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 4,
          style: twoLegcityTextStyle,
          preset: "caption1Regular",
          children: travelInfo[3].city
        })]
      }) : null]
    });
  };
  function FlightHeroImage(props) {
    var imageUrl = props.imageUrl,
      travelInfo = props.travelInfo,
      airportCode = props.airportCode,
      flightNumber = props.flightNumber,
      state = props.state,
      wrapHeaderStyles = props.wrapHeaderStyles;
    var _useState = (0, _react.useState)({
        imageUrl: imageUrl,
        type: _flightHeroImage.FlightHeroImageType.loading
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      dataHeroImage = _useState2[0],
      setDataHeroImage = _useState2[1];
    (0, _react.useEffect)(function () {
      var requestData = /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)(function* () {
          var _env;
          var res = yield fetch(((_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL) + `/bin/ichangi/fly/flight-hero-image?airportCode=${airportCode}&direction=${state}`);
          var data = yield res.json();
          if (data != null && data.image) {
            setDataHeroImage({
              imageUrl: (0, _mediaHelper.handleImageUrl)(data == null ? undefined : data.image),
              type: _flightHeroImage.FlightHeroImageType.default
            });
          } else {
            setDataHeroImage({
              imageUrl: imageUrl,
              type: _flightHeroImage.FlightHeroImageType.default
            });
          }
        });
        return function requestData() {
          return _ref.apply(this, arguments);
        };
      }();
      requestData();
    }, [airportCode]);
    var isLoading = (dataHeroImage == null ? undefined : dataHeroImage.type) === _flightHeroImage.FlightHeroImageType.loading;
    return isLoading ? loadingView() : defaultView(dataHeroImage == null ? undefined : dataHeroImage.imageUrl, travelInfo, flightNumber, wrapHeaderStyles);
  }
