  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MasonryListing = MasonryListing;
  exports.defaultView = defaultView;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _masonryListing = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var shortImageStyle = {
    height: (0, _reactNativeSizeMatters.scale)(110),
    width: (0, _reactNativeSizeMatters.scale)(147),
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16
  };
  var longImageStyle = {
    height: (0, _reactNativeSizeMatters.scale)(150),
    width: (0, _reactNativeSizeMatters.scale)(147),
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16
  };
  var textContainerStyle = {
    backgroundColor: _theme.color.palette.whiteGrey,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    height: 68,
    width: (0, _reactNativeSizeMatters.scale)(147)
  };
  var textLoadingContainerStyle = Object.assign({}, textContainerStyle, {
    height: 70
  });
  var titleStyle = {
    color: _theme.color.palette.almostBlackGrey,
    marginBottom: 4,
    marginTop: 12,
    marginRight: 12,
    marginLeft: 12
  };
  var locationStyle = {
    color: _theme.color.palette.darkestGrey,
    marginLeft: 12,
    marginRight: 12
  };
  var skeletonLayout = [{
    backgroundColor: _theme.color.palette.lighterGrey,
    width: (0, _reactNativeSizeMatters.scale)(121),
    height: 13,
    borderRadius: 4,
    marginLeft: 12,
    marginTop: 16,
    marginBottom: 13
  }, {
    backgroundColor: _theme.color.palette.lighterGrey,
    width: 65,
    height: 13,
    borderRadius: 4,
    marginLeft: 12
  }];
  var loadingView = function loadingView(id) {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: id === 1 || id % 3 === 1 ? longImageStyle : shortImageStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: textLoadingContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayout[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayout[1]
        })]
      })]
    });
  };

  /**
   * Describe your component here
   */
  var COMPONENT_NAME = "MasonryListing";
  function defaultView(props) {
    var imageUrl = props.imageUrl,
      titleName = props.titleName,
      location = props.location,
      onPressed = props.onPressed,
      id = props.id;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      accessible: false,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        style: id === 1 || id % 3 === 1 ? longImageStyle : shortImageStyle,
        source: {
          uri: imageUrl
        },
        testID: `${COMPONENT_NAME}__Image__${id}`,
        accessibilityLabel: imageUrl
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: textContainerStyle,
        accessible: false,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: titleName,
          preset: "bodyTextBold",
          numberOfLines: 1,
          style: titleStyle,
          testID: `${COMPONENT_NAME}__TitleName__${id}`,
          accessibilityLabel: titleName
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: location,
          preset: "caption1Regular",
          numberOfLines: 1,
          style: locationStyle,
          testID: `${COMPONENT_NAME}__Location__${id}`,
          accessibilityLabel: location
        })]
      })]
    });
  }
  function MasonryListing(props) {
    var isLoading = props.state === _masonryListing.MasonryListingState.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      children: isLoading ? loadingView(props.id) : defaultView(props)
    });
  }
