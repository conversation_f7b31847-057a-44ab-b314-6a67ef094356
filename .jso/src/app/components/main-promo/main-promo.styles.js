  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.loadingElementsLayout = exports.lightGreyLoadingColors = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var PADDING_HORIZONTAL_CAROUSEL = 24;
  var WIDTH_IMAGE = width - 48;
  var RATIO = 1.5; // Follow base ratio from figma: Width: 375 and Height: 218
  var HEIGHT_IMAGE = WIDTH_IMAGE / RATIO;
  var loadingElementsLayout = exports.loadingElementsLayout = [{
    borderRadius: 16,
    height: 218,
    width: "100%"
  }, {
    width: "100%",
    height: 14,
    marginTop: 8
  }, {
    width: "40%",
    height: 14,
    marginTop: 8
  }];
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var styles = exports.styles = _reactNative.StyleSheet.create({
    backgroundImageStyle: {
      borderRadius: 16,
      height: HEIGHT_IMAGE,
      width: WIDTH_IMAGE
    },
    carouselContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingHorizontal: PADDING_HORIZONTAL_CAROUSEL
    },
    containerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    contentCarouselContainer: {
      marginTop: 16,
      width: "100%"
    },
    contentContainerLayout: {
      justifyContent: "center",
      marginEnd: 24,
      marginStart: 24,
      marginTop: -102
    },
    descriptionStyle: {
      color: _theme.color.palette.almostBlackGrey,
      textTransform: "none"
    },
    lowerCaseTitle: {
      letterSpacing: 0,
      textTransform: "none"
    },
    pagniationStyle: {
      bottom: 12,
      position: "absolute",
      right: 16
    },
    subCopyStyle: {
      color: _theme.color.palette.almostBlackGrey
    },
    titleStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 8,
      textTransform: "none"
    }
  });
