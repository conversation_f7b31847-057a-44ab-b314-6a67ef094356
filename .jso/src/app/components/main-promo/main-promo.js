  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MainPromo = MainPromo;
  exports.NavigationType = exports.MainPromoType = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSnapCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _paginationIndicator = _$$_REQUIRE(_dependencyMap[10]);
  var _mainPromo = _$$_REQUIRE(_dependencyMap[11]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var COMPONENT_NAME = "MainPromo";
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _mainPromo.styles.carouselContainer,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: _mainPromo.lightGreyLoadingColors,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerStyle: _mainPromo.loadingElementsLayout[0]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _mainPromo.styles.contentCarouselContainer,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: _mainPromo.lightGreyLoadingColors,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerStyle: _mainPromo.loadingElementsLayout[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: _mainPromo.lightGreyLoadingColors,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerStyle: _mainPromo.loadingElementsLayout[2]
        })]
      })]
    });
  };
  var contentView = function contentView(sortedPromotions, onPressed, testID, accessibilityLabel) {
    var _React$useState = React.useState(0),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      index = _React$useState2[0],
      setIndex = _React$useState2[1];
    var pagingText = index + 1 + "/" + sortedPromotions.length;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _mainPromo.styles.carouselContainer,
      children: [(0, _jsxRuntime.jsx)(_reactNativeSnapCarousel.default, {
        data: sortedPromotions,
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            indexs = _ref.index;
          return (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
            onPress: function onPress() {
              return onPressed == null ? undefined : onPressed(item);
            },
            testID: `${testID}__PressablePromotions__${indexs}`,
            accessibilityLabel: `${accessibilityLabel}__PressablePromotions__${indexs}`,
            accessible: false,
            children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
              style: _mainPromo.styles.backgroundImageStyle,
              source: {
                uri: item.backgroundImageUrl
              },
              resizeMode: "cover",
              testID: `${COMPONENT_NAME}__Image`,
              accessibilityLabel: item.backgroundImageUrl
            }), sortedPromotions.length > 1 && (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _mainPromo.styles.pagniationStyle,
              children: (0, _jsxRuntime.jsx)(_paginationIndicator.PaginationIndicator, {
                label: pagingText
              })
            })]
          }, item.orderId);
        },
        sliderWidth: width - 48,
        itemWidth: width - 48,
        layout: "default",
        onSnapToItem: function onSnapToItem(ind) {
          return setIndex(ind);
        },
        testID: `${testID}__Carousel`,
        accessibilityLabel: `${accessibilityLabel}__Carousel`
      }), sortedPromotions.length > 0 && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _mainPromo.styles.contentCarouselContainer,
        onPress: function onPress() {
          return onPressed == null ? undefined : onPressed(sortedPromotions[index]);
        },
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "subTitleBold",
          style: _mainPromo.styles.titleStyle,
          numberOfLines: 2,
          text: sortedPromotions[index].title,
          testID: `${COMPONENT_NAME}__Title`,
          accessibilityLabel: sortedPromotions[index].title
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          style: _mainPromo.styles.descriptionStyle,
          numberOfLines: 1,
          text: sortedPromotions[index].subcopy,
          testID: `${COMPONENT_NAME}__SubCopy`,
          accessibilityLabel: sortedPromotions[index].subcopy
        })]
      })]
    });
  };
  var NavigationType = exports.NavigationType = /*#__PURE__*/function (NavigationType) {
    NavigationType["inapp"] = "in-app";
    NavigationType["external"] = "external";
    return NavigationType;
  }({});
  var MainPromoType = exports.MainPromoType = /*#__PURE__*/function (MainPromoType) {
    MainPromoType["default"] = "default";
    MainPromoType["loading"] = "loading";
    return MainPromoType;
  }({});
  function MainPromo(props) {
    var promotions = props.promotions,
      type = props.type,
      onPressed = props.onPressed,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "MainPromo" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "MainPromo" : _props$accessibilityL;
    var isLoading = type === MainPromoType.loading;
    var sortedPromotions = Array.isArray(promotions) ? (0, _toConsumableArray2.default)(promotions).sort(function (a, b) {
      return a.orderId - b.orderId;
    }) : [];
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _mainPromo.styles.containerStyle,
      children: isLoading ? loadingView() : contentView(sortedPromotions, onPressed, testID, accessibilityLabel)
    });
  }
