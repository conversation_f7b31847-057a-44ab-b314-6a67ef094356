  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.lableView = exports.labelFieldContainer = exports.errorWrapper = exports.errorTextStyle = exports.customePickerInput = exports.clearDataTextStyles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var labelFieldContainer = exports.labelFieldContainer = {
    marginTop: 24
  };
  var errorWrapper = exports.errorWrapper = {
    marginTop: 5,
    flexDirection: "row",
    alignItems: "center"
  };
  var errorTextStyle = exports.errorTextStyle = Object.assign({}, _text.presets.caption1Regular, {
    marginLeft: 4,
    color: _theme.color.palette.baseRed
  });
  var customePickerInput = exports.customePickerInput = {
    marginTop: 9,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: _theme.color.palette.lightGrey,
    backgroundColor: _theme.color.palette.whiteGrey,
    minHeight: 44
  };
  var lableView = exports.lableView = {
    flexDirection: "row",
    alignItems: "center"
  };
  var clearDataTextStyles = exports.clearDataTextStyles = Object.assign({}, _text.presets.textLink, {
    fontSize: 14,
    lineHeight: 18
  });
