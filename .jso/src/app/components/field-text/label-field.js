  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LabelFieldInput = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _utils = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LabelFieldInput = exports.LabelFieldInput = function LabelFieldInput(_ref) {
    var textTx = _ref.textTx,
      isTouched = _ref.isTouched,
      errorMessage = _ref.errorMessage,
      children = _ref.children,
      disabled = _ref.disabled,
      hintAbove = _ref.hintAbove,
      hintBottom = _ref.hintBottom,
      rightClearDataText = _ref.rightClearDataText,
      clearDataOnPress = _ref.clearDataOnPress,
      _ref$borderColor = _ref.borderColor,
      borderColor = _ref$borderColor === undefined ? _theme.color.palette.lightGrey : _ref$borderColor,
      testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.labelFieldContainer,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.lableView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold"
          // eslint-disable-next-line react-native/no-inline-styles
          ,
          style: {
            color: (0, _utils.simpleCondition)({
              condition: isTouched && errorMessage,
              ifValue: _theme.color.palette.baseRed,
              elseValue: {
                condition: disabled,
                ifValue: _theme.color.palette.lightGrey,
                elseValue: _theme.color.palette.almostBlackGrey
              }
            }),
            flex: 1
          },
          tx: textTx,
          numberOfLines: 1
        }), rightClearDataText && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: clearDataOnPress,
          testID: `${testID}__ClearDate`,
          accessibilityLabel: `${accessibilityLabel}__ClearDate`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: rightClearDataText,
            preset: "bodyTextBold",
            style: styles.clearDataTextStyles
          })
        })]
      }), hintAbove, (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: Object.assign({}, styles.customePickerInput, {
          borderColor: (0, _utils.simpleCondition)({
            condition: isTouched && errorMessage,
            ifValue: _theme.color.palette.baseRed,
            elseValue: {
              condition: disabled,
              ifValue: _theme.color.palette.lighterGrey,
              elseValue: borderColor
            }
          }),
          backgroundColor: disabled ? _theme.color.palette.lightestGrey : _theme.color.palette.whiteGrey
        }),
        children: children
      }), !errorMessage && hintBottom && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.errorWrapper,
        children: hintBottom
      }), isTouched && errorMessage && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.errorWrapper,
        children: typeof errorMessage === "string" && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_icons.ErrorOutlined, {
            width: 20,
            height: 20
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            style: styles.errorTextStyle,
            text: errorMessage
          })]
        })
      })]
    });
  };
