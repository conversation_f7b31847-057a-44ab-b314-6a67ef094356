  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TenantOffer = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var Progress = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[5]);
  var _button = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _tenantOffer = _$$_REQUIRE(_dependencyMap[10]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "TenantOffer";
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var container = {
    backgroundColor: _theme.color.palette.whiteGrey,
    marginVertical: 10,
    flex: 1,
    flexDirection: "column",
    elevation: 5,
    width: width / 2,
    borderWidth: 0,
    borderRadius: 16,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.16,
    shadowRadius: 3,
    shadowOffset: {
      height: 1,
      width: 0
    }
  };
  var imageContainer = {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: _theme.color.transparent,
    height: (0, _reactNativeSizeMatters.verticalScale)(110)
  };
  var imageCustomStyle = Object.assign({}, imageContainer, {
    backgroundColor: _theme.color.palette.lightGrey,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16
  });
  var bottomContainer = {
    backgroundColor: _theme.color.palette.whiteGrey,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16
  };
  var imageBackground = {
    width: width / 2,
    height: (0, _reactNativeSizeMatters.verticalScale)(110),
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderRadius: 16
  };
  var ribbonContainerStyle = {
    left: -14,
    top: (0, _reactNativeSizeMatters.verticalScale)(12)
  };
  var content = {
    flexDirection: "row",
    flex: 1,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16
  };
  var bottomContent = {
    backgroundColor: _theme.color.transparent,
    justifyContent: "center",
    padding: 12,
    alignSelf: "center"
  };
  var validityDateTextStyle = {
    paddingTop: 4
  };
  var qtyLeftContainer = {
    flexDirection: "row",
    paddingTop: 4,
    flex: 1,
    flexWrap: "wrap"
  };
  var qtyLeftTextContainer = {
    marginRight: 10
  };
  var qtyLeftProgressBarContainer = {
    paddingBottom: 10
  };
  var progressBarStyle = {
    top: 8,
    height: 6
  };
  var offerTitleStyle = {
    paddingTop: 8,
    color: _theme.color.palette.almostBlackGrey
  };
  var freeTextStyle = {
    paddingTop: 4
  };
  var ctaContainer = {
    alignItems: "flex-start",
    alignContent: "flex-start",
    paddingTop: 8
  };
  var ctaButtonStyle = {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 3,
    paddingBottom: 6
  };
  var savedContainer = {
    alignContent: "flex-start",
    alignItems: "flex-start",
    paddingTop: 8
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var loadingElementsLayout = [{
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    height: 116,
    width: width / 2
  }, {
    width: 41,
    height: 13,
    marginTop: 14,
    marginLeft: 12,
    borderRadius: 4
  }, {
    width: 115,
    height: 13,
    marginTop: 14,
    marginLeft: 12,
    borderRadius: 4
  }, {
    width: 56,
    height: 13,
    marginTop: 13,
    marginBottom: 18,
    marginLeft: 12,
    borderRadius: 4
  }];
  var component = {
    validityDate: ValidityDate,
    qtyLeft: qtyLeft,
    freeText: freeText,
    CTA: CTA,
    saved: saved
  };
  function ValidityDate(props) {
    var textValidityDate = `${(0, _i18n.translate)("notToBeMissed.validTill")} ${(0, _utils.handleCondition)(props.validityDate, props.validityDate, new Date().getUTCFullYear())}`;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: props.validityDate ? (0, _jsxRuntime.jsx)(_text.Text, {
        style: validityDateTextStyle,
        preset: "caption2Regular",
        numberOfLines: 1,
        text: textValidityDate,
        testID: `${COMPONENT_NAME}__textValidityDate`,
        accessibilityLabel: textValidityDate
      }) : null
    });
  }
  function qtyLeft(props) {
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: qtyLeftContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: qtyLeftTextContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            text: `Only ${props.remainingQuantity} left!`,
            preset: "caption1Regular",
            testID: `${COMPONENT_NAME}__qtyLeft`,
            accessibilityLabel: `Only ${props.remainingQuantity} left!`
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: qtyLeftProgressBarContainer,
          children: (0, _jsxRuntime.jsx)(Progress.Bar, {
            style: progressBarStyle,
            borderWidth: 0.1,
            borderRadius: 50,
            borderColor: _theme.color.transparent,
            animated: true,
            width: 70,
            unfilledColor: _theme.color.palette.lighterGrey,
            progress: props.remainingQuantity !== 0 ? props.remainingQuantity / 100 : 0,
            color: _theme.color.palette.lightPurple
          })
        })]
      }), (0, _jsxRuntime.jsx)(ValidityDate, Object.assign({}, props))]
    });
  }
  function freeText(props) {
    return (0, _jsxRuntime.jsx)(_text.Text, {
      text: props.freeText,
      preset: "caption2Regular",
      style: freeTextStyle,
      numberOfLines: 2,
      testID: `${COMPONENT_NAME}__freeText`,
      accessibilityLabel: props.freeText
    });
  }
  function CTA(props) {
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsx)(ValidityDate, Object.assign({}, props)), props.isRedeemable && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: ctaContainer,
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          style: ctaButtonStyle,
          text: (0, _i18n.translate)("notToBeMissed.redeem"),
          typePreset: "secondary",
          sizePreset: "small",
          statePreset: "default",
          backgroundPreset: "light",
          textPreset: "buttonSmall",
          onPress: props.onSubmit
        })
      })]
    });
  }
  function saved(props) {
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsx)(ValidityDate, Object.assign({}, props)), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: savedContainer,
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          text: "Saved",
          icon: true,
          typePreset: "secondary",
          sizePreset: "small",
          statePreset: "disabled",
          backgroundPreset: "light",
          textPreset: "buttonSmall"
        })
      })]
    });
  }
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: lightGreyLoadingColors,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerStyle: loadingElementsLayout[0]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: lighterGreyLoadingColors,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerStyle: loadingElementsLayout[1]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: lighterGreyLoadingColors,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerStyle: loadingElementsLayout[2]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        shimmerColors: lighterGreyLoadingColors,
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerStyle: loadingElementsLayout[3]
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var type = props.type,
      tenantName = props.tenantName,
      offerText = props.offerText,
      offerTitle = props.offerTitle,
      imageUrl = props.imageUrl,
      linkURL = props.linkURL;
    var TagName = component[props.offerMechanic];
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: props.onPressed,
      disabled: !linkURL || type === _tenantOffer.TenantTypeEnum.loading,
      accessible: false,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: imageCustomStyle,
        accessible: false,
        children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
          resizeMode: "cover",
          source: {
            uri: imageUrl
          },
          imageStyle: imageBackground,
          testID: `${COMPONENT_NAME}__ImageBackground`,
          accessibilityLabel: imageUrl,
          children: !!offerText && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: ribbonTextStyles.ribbonStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "XSmallBold",
              text: offerText,
              style: ribbonTextStyles.textRibbonStyle,
              testID: `${COMPONENT_NAME}__OfferText`,
              accessibilityLabel: offerText
            })
          })
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: bottomContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: content,
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: bottomContent,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: {
                  color: _theme.color.palette.lightPurple
                },
                preset: "XSmallBold",
                text: tenantName,
                numberOfLines: 1,
                testID: `${COMPONENT_NAME}__TenantName`,
                accessibilityLabel: tenantName
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                numberOfLines: 2,
                style: offerTitleStyle,
                preset: "bodyTextBold",
                text: offerTitle,
                testID: `${COMPONENT_NAME}__OfferTitle`,
                accessibilityLabel: offerTitle
              }), (0, _jsxRuntime.jsx)(TagName, Object.assign({}, props))]
            })
          })
        })
      })]
    });
  };
  var TenantOffer = exports.TenantOffer = function TenantOffer(props) {
    var isLoading = props.type === _tenantOffer.TenantTypeEnum.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: container,
      children: isLoading ? loadingView() : defaultView(props)
    });
  };
  var ribbonTextStyles = _reactNative2.StyleSheet.create({
    ribbonStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.baseBlue,
      borderRadius: 4,
      height: 24,
      justifyContent: "center",
      left: 9,
      minWidth: 61,
      paddingHorizontal: 8,
      position: "absolute",
      top: 8
    },
    textRibbonStyle: {
      color: _theme.color.palette.whiteGrey,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      textAlign: "center"
    }
  });
