  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LoadingV2 = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _color = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var LOADING_COLORS = [_color.color.palette.lighterGrey, _color.color.background, _color.color.palette.lighterGrey];
  var LoadingV2 = exports.LoadingV2 = _react.default.memo(function (props) {
    var navigationRoute = props.navigationRoute,
      onClose = props.onClose;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.viewImageLoading,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.buttonClose,
          onPress: onClose,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {})
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContent,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          navigationRoute: navigationRoute,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: LOADING_COLORS,
          shimmerStyle: styles.loadingTitle
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          navigationRoute: navigationRoute,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: LOADING_COLORS,
          shimmerStyle: styles.loadingSubTitle
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          navigationRoute: navigationRoute,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: LOADING_COLORS,
          shimmerStyle: styles.loadingContent
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          navigationRoute: navigationRoute,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: LOADING_COLORS,
          shimmerStyle: styles.loadingContent
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          navigationRoute: navigationRoute,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: LOADING_COLORS,
          shimmerStyle: styles.loadingSubContent
        })]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flex: 1
    },
    viewImageLoading: {
      width: '100%',
      height: 250,
      backgroundColor: _color.color.palette.lighterGrey
    },
    viewContent: {
      padding: 24
    },
    loadingTitle: {
      width: '100%',
      height: 20,
      borderRadius: 4
    },
    loadingSubTitle: {
      width: 160,
      height: 20,
      borderRadius: 4,
      marginTop: 12,
      marginBottom: 16
    },
    loadingContent: {
      height: 12,
      width: '100%',
      marginBottom: 12,
      borderRadius: 4
    },
    loadingSubContent: {
      height: 12,
      width: 80,
      borderRadius: 4
    },
    buttonClose: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: "#FCFCFC99",
      position: 'absolute',
      top: 18,
      right: 14,
      justifyContent: 'center',
      alignItems: 'center'
    }
  });
