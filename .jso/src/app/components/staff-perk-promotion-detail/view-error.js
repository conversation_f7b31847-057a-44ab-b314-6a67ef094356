  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewError = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _button = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _utils = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var ViewError = exports.ViewError = _react.default.memo(function (props) {
    var _renderContent, _renderContent2, _renderContent3, _renderContent4, _renderContent5, _renderContent6;
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr42 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR42";
    });
    var handlePressReload = props.handlePressReload,
      isNoInternet = props.isNoInternet,
      onCloseModal = props.onCloseModal;
    var renderContent = function renderContent() {
      var titleTx = ehr42 != null && ehr42.header ? ehr42 == null ? undefined : ehr42.header : "errorOverlay.variant1.title";
      var messageTx = ehr42 != null && ehr42.subHeader ? ehr42 == null ? undefined : ehr42.subHeader : "errorOverlay.variant1.message";
      var reloadTx = ehr42 != null && ehr42.buttonLabel ? ehr42 == null ? undefined : ehr42.buttonLabel : "errorOverlay.variant1.reload";
      return {
        titleTx: titleTx,
        messageTx: messageTx,
        reloadTx: reloadTx
      };
    };
    if (isNoInternet) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          tx: "errorOverlay.variant3.title"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          tx: "errorOverlay.variant3.message"
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.reloadButtonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            onPress: handlePressReload,
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "primary",
            tx: "errorOverlay.variant3.retry",
            backgroundPreset: "light",
            statePreset: "default"
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.buttonClose,
          onPress: onCloseModal,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {
            width: 30,
            height: 30
          })
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [ehr42 != null && ehr42.icon ? (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _utils.mappingUrlAem)(ehr42 == null ? undefined : ehr42.icon)
        },
        style: styles.image,
        resizeMode: "contain"
      }) : (0, _jsxRuntime.jsx)(_icons.ErrorCloudV2, {}), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.titleTextStyle,
        tx: ehr42 ? null : (_renderContent = renderContent()) == null ? undefined : _renderContent.titleTx,
        text: ehr42 ? (_renderContent2 = renderContent()) == null ? undefined : _renderContent2.titleTx : null
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.messageTextStyle,
        tx: ehr42 ? null : (_renderContent3 = renderContent()) == null ? undefined : _renderContent3.messageTx,
        text: ehr42 ? (_renderContent4 = renderContent()) == null ? undefined : _renderContent4.messageTx : null
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: styles.reloadButtonStyle,
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          onPress: handlePressReload,
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "primary",
          tx: ehr42 ? null : (_renderContent5 = renderContent()) == null ? undefined : _renderContent5.reloadTx,
          text: ehr42 ? (_renderContent6 = renderContent()) == null ? undefined : _renderContent6.reloadTx : null,
          backgroundPreset: "light",
          statePreset: "default"
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.buttonClose,
        onPress: onCloseModal,
        children: (0, _jsxRuntime.jsx)(_icons.Cross, {
          width: 30,
          height: 30
        })
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      paddingTop: 88,
      height: "100%",
      overflow: "hidden",
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingHorizontal: 24,
      alignItems: 'center'
    },
    titleTextStyle: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22,
      marginBottom: 16,
      marginTop: 40,
      textAlign: "center"
    },
    messageTextStyle: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      textAlign: "center"
    },
    reloadButtonStyle: {
      width: '100%',
      borderRadius: 60,
      paddingHorizontal: 24,
      marginTop: 24
    },
    image: {
      width: 120,
      height: 120
    },
    buttonClose: {
      borderRadius: 12,
      position: 'absolute',
      top: 14,
      right: 10
    }
  });
