  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.QR_SIZE = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var RANGE_DOTS = 9;
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var WRAP_BENZEL_SIZE = Math.floor(width * 0.9);
  var WRAP_BENZEL_SIZE_FOR_MONARCH = WRAP_BENZEL_SIZE - 15;
  var WRAP_QR_SIZE = Math.floor(WRAP_BENZEL_SIZE / 1.35);
  var WRAP_QR_SIZE_FOR_MONARCH = WRAP_QR_SIZE - 15;
  var QR_SIZE = exports.QR_SIZE = Math.floor(WRAP_QR_SIZE / 1.55);
  var WRAP_BENZEL_SIZE_LEVEL_2 = Math.floor(WRAP_BENZEL_SIZE / 1.155);
  var WRAP_BENZEL_SIZE_LEVEL_2_FOR_MONARCH = WRAP_BENZEL_SIZE_LEVEL_2 - 15;
  var WRAP_IMAGE_WIDTH = Math.floor(QR_SIZE / 29) * RANGE_DOTS;
  var WRAP_IMAGE_HEIGHT = Math.floor(QR_SIZE / 29) * RANGE_DOTS;
  var IMAGE_POSITION_TOP = Math.floor(QR_SIZE / 2) - Math.floor(WRAP_IMAGE_HEIGHT / 2);
  var IMAGE_POSITION_LEFT = Math.floor(QR_SIZE / 2) - Math.floor(WRAP_IMAGE_WIDTH / 2);
  var IMAGE_WIDTH = 53.5;
  var IMAGE_HEIGHT = 40;
  var TOP_BACKGROUND_COLOR_BAR = "#DADADA";
  var scalePoint = width / 414;
  var CONCIERGE_WIDTH = width - 48;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    accountTextName: Object.assign({}, _text.presets.h1, {
      color: _theme.color.palette.whiteGrey,
      fontSize: _responsive.default.getFontSize(28),
      lineHeight: _responsive.default.getFontSize(36),
      paddingHorizontal: 25,
      textAlign: "center"
    }),
    cardNoText: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.whiteGrey,
      fontSize: 12,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      letterSpacing: 1.2,
      lineHeight: 16
    }),
    contentScroll: {
      paddingBottom: 100
    },
    copyStyle: {
      marginLeft: 4
    },
    gradientStyle: {
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      flex: 1,
      height: height
    },
    headerText: {
      color: _theme.color.palette.whiteGrey,
      textAlign: "center"
    },
    imageStyle: {
      height: IMAGE_HEIGHT,
      width: IMAGE_WIDTH,
      zIndex: 999999
    },
    leftPart: {
      width: "10%"
    },
    overlayHiddenCorner: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: WRAP_QR_SIZE / 2,
      height: WRAP_QR_SIZE,
      justifyContent: "center",
      overflow: "hidden",
      transform: [{
        scale: 1
      }],
      width: WRAP_QR_SIZE
    },
    overlayHiddenCornerForMonarch: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: WRAP_QR_SIZE_FOR_MONARCH / 2,
      height: WRAP_QR_SIZE_FOR_MONARCH,
      justifyContent: "center",
      overflow: "hidden",
      transform: [{
        scale: 1
      }],
      width: WRAP_QR_SIZE_FOR_MONARCH
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      height: "100%",
      width: "100%"
    },
    rightPart: {
      width: "10%"
    },
    safeAreaViewStyle: {
      alignItems: "center"
    },
    scaleQR: {
      transform: [{
        scale: scalePoint
      }]
    },
    textScanToPay: {
      color: _theme.color.palette.whiteGrey,
      fontSize: _responsive.default.getFontSize(16),
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: _responsive.default.getFontSize(16),
      marginLeft: 11
    },
    textStaff: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.whiteGrey,
      fontSize: 24,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      letterSpacing: _responsive.default.getFontSize(1.6),
      lineHeight: 24
    }),
    tierCodeText: {
      color: _theme.color.palette.whiteGrey,
      fontSize: _responsive.default.getFontSize(12),
      fontWeight: _reactNative.Platform.select({
        ios: "800",
        android: "normal"
      }),
      letterSpacing: _responsive.default.getFontSize(1.2),
      lineHeight: _responsive.default.getFontSize(16),
      textAlign: "center",
      textTransform: "uppercase",
      marginTop: 8
    },
    topContentView: {
      alignItems: "center",
      width: width
    },
    touchableScanToPay: {
      alignItems: "center",
      borderColor: _theme.color.palette.whiteGrey,
      borderRadius: 60,
      borderWidth: 2,
      flexDirection: "row",
      height: 44,
      marginBottom: 10,
      paddingHorizontal: 24
    },
    touchableScanToPayForMonarch: {
      alignItems: "center",
      borderRadius: 60,
      backgroundColor: "rgba(252,252,252,0.3)",
      flexDirection: "row",
      height: 44,
      marginBottom: 10,
      paddingHorizontal: 24
    },
    underContentView: {
      backgroundColor: TOP_BACKGROUND_COLOR_BAR,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8,
      height: 10,
      width: width * 0.9
    },
    wrapAccountName: {},
    wrapBannerRewardCatalog: {
      marginHorizontal: 25,
      marginBottom: 8
    },
    wrapBenzelSize: {
      alignItems: "center",
      height: WRAP_BENZEL_SIZE,
      justifyContent: "center",
      marginBottom: 15,
      width: WRAP_BENZEL_SIZE
    },
    wrapBenzelSizeLevel2: {
      alignItems: "center",
      height: WRAP_BENZEL_SIZE_LEVEL_2,
      justifyContent: "center",
      width: WRAP_BENZEL_SIZE_LEVEL_2
    },
    wrapBenzelSizeForMonarch: {
      alignItems: "center",
      height: WRAP_BENZEL_SIZE_FOR_MONARCH,
      justifyContent: "center",
      marginBottom: 15,
      width: WRAP_BENZEL_SIZE_FOR_MONARCH
    },
    wrapBenzelSizeLevel2ForMonarch: {
      alignItems: "center",
      height: WRAP_BENZEL_SIZE_LEVEL_2_FOR_MONARCH,
      justifyContent: "center",
      width: WRAP_BENZEL_SIZE_LEVEL_2_FOR_MONARCH
    },
    wrapCardNo: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginTop: 4
    },
    wrapHeader: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 10,
      paddingBottom: 20,
      paddingHorizontal: 16,
      paddingTop: 20,
      width: "100%"
    },
    wrapImageStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      height: WRAP_IMAGE_HEIGHT,
      justifyContent: "center",
      left: IMAGE_POSITION_LEFT,
      position: "absolute",
      top: IMAGE_POSITION_TOP,
      width: WRAP_IMAGE_WIDTH
    },
    wrapQR: {
      alignItems: "center",
      height: QR_SIZE,
      justifyContent: "center",
      transform: [{
        scale: 1
      }],
      width: QR_SIZE
    },
    wrapQRSize: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.black,
      borderRadius: WRAP_QR_SIZE / 2,
      height: WRAP_QR_SIZE,
      justifyContent: "center",
      transform: [{
        scale: 0.95
      }],
      width: WRAP_QR_SIZE
    },
    wrapStaffText: {
      alignItems: "center",
      borderRadius: 999,
      bottom: 10,
      height: 44,
      justifyContent: "center",
      overflow: 'hidden',
      paddingVertical: 12,
      position: "absolute",
      width: 125
    },
    wrapStaffTextForMonarch: {
      alignItems: "center",
      borderRadius: 999,
      bottom: 18,
      height: 44,
      justifyContent: "center",
      overflow: 'hidden',
      paddingVertical: 12,
      position: "absolute",
      width: 125
    },
    touchableConciergeSection: {
      width: CONCIERGE_WIDTH,
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderRadius: 16,
      borderColor: "rgba(171, 171, 171, 0.3)",
      borderWidth: 1,
      marginVertical: 24
    },
    wrapTextConciergeSection: {
      width: CONCIERGE_WIDTH - 110
    },
    imageConciergeSection: {
      width: 70,
      height: 70,
      borderRadius: 8
    },
    titleConciergeSection: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      marginLeft: 16,
      width: CONCIERGE_WIDTH - 110
    }),
    subTextConciergeSection: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.whiteGrey,
      marginLeft: 16,
      marginTop: 4,
      width: CONCIERGE_WIDTH - 110
    }),
    wrapTierCodeText: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 8
    },
    tierMonarchCodeText: {
      color: _theme.color.palette.whiteGrey,
      fontSize: _responsive.default.getFontSize(12),
      fontWeight: _reactNative.Platform.select({
        ios: "800",
        android: "normal"
      }),
      letterSpacing: _responsive.default.getFontSize(1.2),
      lineHeight: _responsive.default.getFontSize(16),
      textAlign: "center",
      textTransform: "uppercase",
      marginRight: 4
    }
  });
