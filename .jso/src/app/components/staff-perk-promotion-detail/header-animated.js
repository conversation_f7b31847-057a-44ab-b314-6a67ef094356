  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.HeaderAnimated = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _text2 = _$$_REQUIRE(_dependencyMap[6]);
  var _palette = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var HeaderAnimated = exports.HeaderAnimated = _react.default.memo(function (props) {
    var animatedHeaderStyle = props.animatedHeaderStyle,
      title = props.title;
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [styles.container, animatedHeaderStyle],
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.viewTitle,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          style: styles.offerTitle,
          numberOfLines: 1
        })
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      height: 64,
      position: 'absolute',
      top: 0,
      backgroundColor: _theme.color.palette.whiteGrey,
      justifyContent: 'center',
      alignItems: 'center'
    },
    viewTitle: {
      width: '70%',
      justifyContent: 'center',
      alignItems: 'center'
    },
    offerTitle: Object.assign({}, _text2.presets.bodyTextBold, {
      textAlign: 'center',
      color: _palette.palette.almostBlackGrey
    })
  });
