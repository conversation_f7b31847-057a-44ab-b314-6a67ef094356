  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _changiRewardsRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _staffPerkPromotionDetailModal = _$$_REQUIRE(_dependencyMap[12]);
  var _icons = _$$_REQUIRE(_dependencyMap[13]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[15]);
  var _adobe = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _utils = _$$_REQUIRE(_dependencyMap[19]);
  var _staffPerkPromotionDetailController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _staffPerkRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[22]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[23]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _collapsible = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _reactNativeRenderHtml = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _htmlContent = _$$_REQUIRE(_dependencyMap[28]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[29]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[30]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[31]);
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[32]);
  var _changiRewardCardInsidePromo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[34]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[35]);
  var _deeplinkParameter = _$$_REQUIRE(_dependencyMap[36]);
  var _enum = _$$_REQUIRE(_dependencyMap[37]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[38]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[39]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[40]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[41]);
  var _authentication = _$$_REQUIRE(_dependencyMap[42]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[43]));
  var _headerAnimated = _$$_REQUIRE(_dependencyMap[44]);
  var _changipay = _$$_REQUIRE(_dependencyMap[45]);
  var _staffPerkPromotionDetailModal2 = _$$_REQUIRE(_dependencyMap[46]);
  var _loadingV = _$$_REQUIRE(_dependencyMap[47]);
  var _viewError = _$$_REQUIRE(_dependencyMap[48]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[49]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[50]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_8541394932749_init_data = {
    code: "function staffPerkPromotionDetailModalTsx1(event){const{scrollY}=this.__closure;scrollY.value=event.contentOffset.y;}"
  };
  var _worklet_17523438827513_init_data = {
    code: "function staffPerkPromotionDetailModalTsx2(){const{interpolate,scrollY,heightBannerModal,positionTitle}=this.__closure;const opacity=interpolate(scrollY.value,[0,heightBannerModal.value+positionTitle.value-30,heightBannerModal.value+positionTitle.value],[0,0,1]);return{opacity:opacity};}"
  };
  var StaffPerkPromotionDetailModal = function StaffPerkPromotionDetailModal(_ref) {
    var _navigationRef$curren;
    var navigationRef = _ref.navigationRef;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    var CRCardIcon = memberIconInfo == null ? undefined : memberIconInfo.crCardIcon;
    var navigationRoute = navigationRef == null || (_navigationRef$curren = navigationRef.current) == null || _navigationRef$curren.getCurrentRoute == null ? undefined : _navigationRef$curren.getCurrentRoute();
    var isShopDineV2 = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOPDINE_V2);
    var modalRef = (0, _react.useRef)(null);
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var heightBannerModal = (0, _reactNativeReanimated.useSharedValue)(0);
    var positionTitle = (0, _reactNativeReanimated.useSharedValue)(0);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      newNavigation = _useState2[0],
      setNewNavigation = _useState2[1];
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isNoInternet = _useState4[0],
      setIsNoInternet = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isShowWithoutImage = _useState6[0],
      setIsShowWithoutImage = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      showHeaderAnimated = _useState8[0],
      setShowHeaderAnimated = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      modalVisible = _useState0[0],
      setModalVisible = _useState0[1];
    var isModalOpenRef = (0, _react.useRef)(false);
    var _useState1 = (0, _react.useState)(null),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      itemSelected = _useState10[0],
      setItemSelected = _useState10[1];
    var _useState11 = (0, _react.useState)(null),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      action = _useState12[0],
      setAction = _useState12[1];
    var staffPerkPromotionDetailRequest = (0, _reactRedux.useSelector)(_staffPerkRedux.StaffPerkSelectors.staffPerkPromotionDetailRequest);
    var staffPerkPromotionDetailPayload = (0, _reactRedux.useSelector)(_staffPerkRedux.StaffPerkSelectors.staffPerkPromotionDetailPayload);
    var staffPerkPromotionDetailError = (0, _reactRedux.useSelector)(_staffPerkRedux.StaffPerkSelectors.staffPerkPromotionDetailError);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var tierIconsAEM = (0, _lodash.get)(dataCommonAEM, "data.pageLanding.explore.tierIcons");
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var changiRewardCardInsidePromo = (0, _react.useRef)(null);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("STAFF_PERK_PROMOTION_DETAIL_MODAL"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useCPay = (0, _changipay.useCPay)(),
      openChangiPay = _useCPay.openChangiPay;
    var isTenantIdIsAnInteger = /^(?!0)\d+$/.test(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantId);
    var shouldHidePromoAvaibility = !isTenantIdIsAnInteger && (0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiUrl);
    var promoAvaibilityStyle = shouldHidePromoAvaibility && {
      marginTop: 24,
      borderBottomColor: "transparent"
    };
    var iShopChangiAvailabilityStyle = [_staffPerkPromotionDetailModal.styles.physicalTenantContentStyle, {
      paddingBottom: 16,
      borderBottomWidth: 0
    }];
    var mappingTierCode = function mappingTierCode(tierCode, arrTier) {
      if (tierCode === _changiRewardsMemberCard.Tier.StaffGold) {
        return arrTier == null ? undefined : arrTier.find(function (tierIconElement) {
          return tierIconElement.tier === _changiRewardsMemberCard.Tier.Gold;
        });
      } else if (tierCode === _changiRewardsMemberCard.Tier.StaffMember) {
        return arrTier == null ? undefined : arrTier.find(function (tierIconElement) {
          return tierIconElement.tier === _changiRewardsMemberCard.Tier.Member;
        });
      } else if (tierCode === _changiRewardsMemberCard.Tier.StaffPlatinum) {
        return arrTier == null ? undefined : arrTier.find(function (tierIconElement) {
          return tierIconElement.tier === _changiRewardsMemberCard.Tier.Platinum;
        });
      } else if (tierCode === _changiRewardsMemberCard.Tier.StaffMonarch) {
        return arrTier == null ? undefined : arrTier.find(function (tierIconElement) {
          return tierIconElement.tier === _changiRewardsMemberCard.Tier.Monarch;
        });
      }
      return arrTier == null ? undefined : arrTier.find(function (tierIconElement) {
        return tierIconElement.tier === tierCode;
      });
    };
    var transformTierIcon = function transformTierIcon(rewardsDataParams, isLoggedInParams, tierIconsAEMParams) {
      var _rewardsDataParams$re;
      var tier = isLoggedInParams ? rewardsDataParams == null || (_rewardsDataParams$re = rewardsDataParams.reward) == null || (_rewardsDataParams$re = _rewardsDataParams$re.currentTierInfo) == null ? undefined : _rewardsDataParams$re.replace(" ", "") : "Non-logged-in";
      var tierAEM = mappingTierCode(tier, tierIconsAEMParams);
      return (0, _utils.mappingUrlAem)(tierAEM == null ? undefined : tierAEM.icon);
    };
    var tierIcon = (0, _react.useMemo)(function () {
      return transformTierIcon(rewardsData, true, tierIconsAEM);
    }, [rewardsData, tierIconsAEM, true]);
    (0, _react.useEffect)(function () {
      _staffPerkPromotionDetailController.default.setModalRef(modalRef);
    }, []);
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var modalStyle = {
      marginHorizontal: 0,
      marginBottom: 0,
      marginTop: _reactNative2.Platform.select({
        ios: 60,
        android: (0, _utils.handleCondition)(inset == null ? undefined : inset.top, (inset == null ? undefined : inset.top) + 5, 25)
      }),
      overflow: "hidden",
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      backgroundColor: _theme.color.palette.whiteGrey
    };
    (0, _react.useImperativeHandle)(modalRef, function () {
      return {
        show: function show(nav, item, showWithoutImage) {
          setNewNavigation(nav);
          setItemSelected(item);
          setModalVisible(true);
          setIsShowWithoutImage(!!showWithoutImage);
          isModalOpenRef.current = true;
          dispatch(_staffPerkRedux.default.setStaffPerkPromotionDetailModalOpenning(true));
        },
        hide: function hide() {
          setModalVisible(false);
          isModalOpenRef.current = false;
          dispatch(_staffPerkRedux.default.setStaffPerkPromotionDetailModalOpenning(false));
        },
        setActionWhenCloseModal: function setActionWhenCloseModal(_nav, actionType) {
          setAction(actionType);
        }
      };
    }, []);
    var checkConnection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkConnection() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("StaffPerkPromotion_Ecard");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = newNavigation == null ? undefined : newNavigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("StaffPerkPromotion_Ecard", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, []);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(itemSelected)) {
        handleFetchData();
      }
    }, [itemSelected]);
    var handleFetchData = function handleFetchData() {
      checkConnection().then(function (res) {
        if (res) {
          var _itemSelected$item;
          if (isNoInternet) {
            setIsNoInternet(false);
          }
          var params = {
            promo_id: itemSelected == null || (_itemSelected$item = itemSelected.item) == null ? undefined : _itemSelected$item.id
          };
          dispatch(_staffPerkRedux.default.getStaffPerkPromotionDetailRequest(params));
        } else {
          setIsNoInternet(true);
        }
      });
    };
    var closeScreen = function closeScreen() {
      scrollY.value = 0;
      heightBannerModal.value = 0;
      positionTitle.value = 0;
      dispatch(_changiRewardsRedux.default.setChangiECardModalOpenning(false));
      _staffPerkPromotionDetailController.default.hideModal();
    };
    var resetState = function resetState() {
      setAction(null);
    };
    var TRANSITION_TIMING = 200;
    var openWebView = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var UID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyDetail, `${staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title} | ${staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkLabel} | ${UID}`));
        setAction(_staffPerkPromotionDetailModal2.ModalAction.OPEN_WEBVIEW);
        closeScreen();
      });
      return function openWebView() {
        return _ref3.apply(this, arguments);
      };
    }();
    var openTenantDetail = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var UID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyDetail, `${staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title} | PROMO AVAILABILITY | ${staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantName} | ${UID}`));
        if ((staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantType) === "dine") {
          setAction(_staffPerkPromotionDetailModal2.ModalAction.OPEN_DINE_DETAIL);
        } else if ((staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantType) === "shop") {
          setAction(_staffPerkPromotionDetailModal2.ModalAction.OPEN_SHOP_DETAIL);
        }
        closeScreen();
      });
      return function openTenantDetail() {
        return _ref4.apply(this, arguments);
      };
    }();
    var openIShopChangi = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        var UID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyDetail, `${staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title} | PROMO AVAILABILITY | iShopChangi | ${UID}`));
        setAction(_staffPerkPromotionDetailModal2.ModalAction.OPEN_ISHOPCHANGI);
        closeScreen();
      });
      return function openIShopChangi() {
        return _ref5.apply(this, arguments);
      };
    }();
    var handleTag = function handleTag() {
      var firstItem = (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        activeOpacity: 1,
        children: (0, _jsxRuntime.jsx)(_icons.StaffPerkTag, {})
      }, `tagPromo-`);
      var content = [firstItem];
      if (!(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tags)) {
        staffPerkPromotionDetailPayload == null || staffPerkPromotionDetailPayload.tags.forEach(function (e, index) {
          if ((e == null ? undefined : e.toLowerCase()) !== "staff perks") {
            content.push((0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: _staffPerkPromotionDetailModal.styles.tagPromo,
              activeOpacity: 1,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: e,
                style: _staffPerkPromotionDetailModal.styles.textTagPromo
              })
            }, `tagPromo-${index}`));
          }
        });
      }
      return content;
    };
    var processChangiPay = function processChangiPay() {
      if (_reactNative2.Platform.OS === "ios") {
        setTimeout(function () {
          openChangiPay();
        }, 200);
      } else {
        openChangiPay();
      }
    };
    var handleNavigateCSMIShopchangi = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (url) {
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var target = (0, _deeplinkParameter.getISCInputParamsDeepLink)(url);
        var payload = {
          stateCode: _constants.StateCode.ISHOPCHANGI_PAGELINK,
          input: Object.assign({}, target, {
            ecid: ecid
          })
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          if (response != null && response.redirectUri) {
            newNavigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            newNavigation.navigate(_constants.NavigationConstants.webview, {
              uri: url
            });
          }
        } catch (error) {
          newNavigation.navigate(_constants.NavigationConstants.webview, {
            uri: url
          });
        }
      });
      return function handleNavigateCSMIShopchangi(_x) {
        return _ref6.apply(this, arguments);
      };
    }();
    var callbackWhenClose = function callbackWhenClose() {
      switch (action) {
        case _staffPerkPromotionDetailModal2.ModalAction.OPEN_WEBVIEW:
          var isIscDomain = (0, _deeplinkParameter.getIsISCLinkDomain)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkUrl);
          if ((staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkUrl) === _staffPerkPromotionDetailModal2.CTALinkUrl.MiffyBirthdayBash) {
            handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.missionpass, {
              aaTag: (0, _utils.joinTexts)([_navigationHelper.NavigationAATag.offerDetails, staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title]),
              isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
              pageSource: _navigationHelper.NavigationPageSource.staffPerkPromotionDetailModal,
              taskCode: _staffPerkPromotionDetailModal2.TaskCode.StaffPerks,
              utmCampaign: "miffybirthdaybash25"
            });
          } else if ((staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkUrl) === _staffPerkPromotionDetailModal2.CTALinkUrl.SpaceAppxplorer) {
            handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.missionpass, {
              aaTag: (0, _utils.joinTexts)([_navigationHelper.NavigationAATag.offerDetails, staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title]),
              isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
              pageSource: _navigationHelper.NavigationPageSource.staffPerkPromotionDetailModal,
              taskCode: _staffPerkPromotionDetailModal2.TaskCode.StaffPerks
            });
          } else if (isIscDomain) {
            handleNavigateCSMIShopchangi(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkUrl);
          } else {
            newNavigation.navigate(_constants.NavigationConstants.webview, {
              uri: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkUrl
            });
          }
          break;
        case _staffPerkPromotionDetailModal2.ModalAction.OPEN_DINE_DETAIL:
          newNavigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
            tenantId: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantId,
            tenantName: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantName
          });
          break;
        case _staffPerkPromotionDetailModal2.ModalAction.OPEN_SHOP_DETAIL:
          newNavigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
            tenantId: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantId,
            tenantName: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantName
          });
          break;
        case _staffPerkPromotionDetailModal2.ModalAction.OPEN_ISHOPCHANGI:
          var isIscDomainiShopChangiUrl = (0, _deeplinkParameter.getIsISCLinkDomain)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiUrl);
          if (isIscDomainiShopChangiUrl) {
            handleNavigateCSMIShopchangi(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiUrl);
          } else {
            newNavigation.navigate(_constants.NavigationConstants.webview, {
              uri: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiUrl
            });
          }
          break;
        case _staffPerkPromotionDetailModal2.ModalAction.OPEN_CHANGIPAY:
          processChangiPay();
          break;
      }
    };
    var headerModal = function headerModal() {
      if (!(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.imageUrl) && !isShowWithoutImage) {
        return (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
          source: {
            uri: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.imageUrl
          },
          imageStyle: _staffPerkPromotionDetailModal.styles.backgroundImage,
          resizeMode: "stretch"
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: isShopDineV2 ? _staffPerkPromotionDetailModal.styles.headerModalWithoutImageStyleV2 : _staffPerkPromotionDetailModal.styles.headerModalWithoutImageStyle
      });
    };
    var openCRCard = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* () {
        var UID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyDetail, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyDetail, `${staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title} | ECard | ${UID}`));
        changiRewardCardInsidePromo.current.showCard(newNavigation);
      });
      return function openCRCard() {
        return _ref7.apply(this, arguments);
      };
    }();
    var scrollHandler = (0, _reactNativeReanimated.useAnimatedScrollHandler)({
      onScroll: function () {
        var staffPerkPromotionDetailModalTsx1 = function staffPerkPromotionDetailModalTsx1(event) {
          scrollY.value = event.contentOffset.y;
        };
        staffPerkPromotionDetailModalTsx1.__closure = {
          scrollY: scrollY
        };
        staffPerkPromotionDetailModalTsx1.__workletHash = 8541394932749;
        staffPerkPromotionDetailModalTsx1.__initData = _worklet_8541394932749_init_data;
        return staffPerkPromotionDetailModalTsx1;
      }()
    });
    var animatedHeaderStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var staffPerkPromotionDetailModalTsx2 = function staffPerkPromotionDetailModalTsx2() {
        var opacity = (0, _reactNativeReanimated.interpolate)(scrollY.value, [0, heightBannerModal.value + positionTitle.value - 30, heightBannerModal.value + positionTitle.value], [0, 0, 1]);
        return {
          opacity: opacity
        };
      };
      staffPerkPromotionDetailModalTsx2.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        scrollY: scrollY,
        heightBannerModal: heightBannerModal,
        positionTitle: positionTitle
      };
      staffPerkPromotionDetailModalTsx2.__workletHash = 17523438827513;
      staffPerkPromotionDetailModalTsx2.__initData = _worklet_17523438827513_init_data;
      return staffPerkPromotionDetailModalTsx2;
    }());
    var handleReload = function handleReload() {
      checkConnection().then(function (res) {
        if (res) {
          var _itemSelected$item2;
          if (isNoInternet) {
            setIsNoInternet(false);
          }
          var params = {
            promo_id: itemSelected == null || (_itemSelected$item2 = itemSelected.item) == null ? undefined : _itemSelected$item2.id
          };
          dispatch(_staffPerkRedux.default.getStaffPerkPromotionDetailRequest(params));
        } else {
          setIsNoInternet(true);
        }
      });
    };
    (0, _react.useEffect)(function () {
      var timeout = null;
      if (!staffPerkPromotionDetailRequest) {
        timeout = setTimeout(function () {
          return setShowHeaderAnimated(true);
        }, 500);
      } else {
        setShowHeaderAnimated(false);
      }
      return function () {
        if (timeout) clearTimeout(timeout);
      };
    }, [staffPerkPromotionDetailRequest]);
    var renderHeaderAnimated = function renderHeaderAnimated() {
      return showHeaderAnimated ? (0, _jsxRuntime.jsx)(_headerAnimated.HeaderAnimated, {
        animatedHeaderStyle: animatedHeaderStyle,
        title: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title
      }) : null;
    };
    var renderContentModal = function renderContentModal() {
      if (staffPerkPromotionDetailRequest && isShopDineV2) {
        return (0, _jsxRuntime.jsx)(_loadingV.LoadingV2, {
          navigationRoute: navigationRoute,
          onClose: closeScreen
        });
      } else if (isNoInternet || staffPerkPromotionDetailError) {
        return (0, _jsxRuntime.jsx)(_viewError.ViewError, {
          isNoInternet: isNoInternet,
          handlePressReload: handleReload,
          onCloseModal: closeScreen
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.ScrollView, {
            style: {
              flex: 1
            },
            showsVerticalScrollIndicator: false,
            onScroll: scrollHandler,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
              barStyle: "light-content"
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _staffPerkPromotionDetailModal.styles.contentModalStyle,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                onLayout: function onLayout(event) {
                  var layout = event.nativeEvent.layout;
                  heightBannerModal.value = layout.height;
                },
                children: headerModal()
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: isShopDineV2 ? _staffPerkPromotionDetailModal.styles.contentPromoV2 : _staffPerkPromotionDetailModal.styles.contentPromo,
                children: [!isShopDineV2 && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
                    horizontal: true,
                    showsHorizontalScrollIndicator: false,
                    children: handleTag()
                  })
                }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                  activeOpacity: 1,
                  onLayout: function onLayout(event) {
                    var layout = event.nativeEvent.layout;
                    positionTitle.value = layout.y + 16;
                  },
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    text: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.title,
                    style: isShopDineV2 ? _staffPerkPromotionDetailModal.styles.offerTitleV2 : _staffPerkPromotionDetailModal.styles.offerTitle
                  }), (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
                    source: {
                      html: (0, _htmlContent.formatHtmlContent)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.description)
                    },
                    tagsStyles: isShopDineV2 ? _staffPerkPromotionDetailModal.tagStyle2V2 : _staffPerkPromotionDetailModal.tagStyle2,
                    contentWidth: _staffPerkPromotionDetailModal.width,
                    systemFonts: _staffPerkPromotionDetailModal.systemFonts
                  })]
                }), !(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkLabel) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _staffPerkPromotionDetailModal.styles.openWebViewSection,
                  children: [isShopDineV2 ? (0, _jsxRuntime.jsx)(_icons.StaffPerkOpenWebViewV2, {}) : (0, _jsxRuntime.jsx)(_icons.StaffPerkOpenWebView, {}), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                    onPress: openWebView,
                    children: (0, _jsxRuntime.jsx)(_text.Text, {
                      text: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.linkLabel,
                      style: isShopDineV2 ? _staffPerkPromotionDetailModal.styles.textLinkWebViewV2 : _staffPerkPromotionDetailModal.styles.textLinkWebView
                    })
                  })]
                }), !shouldHidePromoAvaibility && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  activeOpacity: 1,
                  style: isShopDineV2 ? _staffPerkPromotionDetailModal.styles.physicalTenantStyleV2 : _staffPerkPromotionDetailModal.styles.physicalTenantStyle
                }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: promoAvaibilityStyle,
                  children: [(0, _utils.handleCondition)(shouldHidePromoAvaibility, null, (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                    activeOpacity: 1,
                    children: (0, _jsxRuntime.jsx)(_text.Text, {
                      text: isShopDineV2 ? "Promo Availability" : "PROMO AVAILABILITY",
                      style: isShopDineV2 ? _staffPerkPromotionDetailModal.styles.promoAvailabilityTextV2 : _staffPerkPromotionDetailModal.styles.promoAvailabilityText
                    })
                  })), isTenantIdIsAnInteger && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                    onPress: openTenantDetail,
                    activeOpacity: 0.5,
                    children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                      style: [_staffPerkPromotionDetailModal.styles.physicalTenantContentStyle, {
                        borderBottomWidth: !(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiUrl) ? 1 : 0
                      }],
                      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
                        source: {
                          uri: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantIcon
                        },
                        style: _staffPerkPromotionDetailModal.styles.tenantIconStyle,
                        resizeMode: "stretch"
                      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                        style: _staffPerkPromotionDetailModal.styles.promoRightContentStyle,
                        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                          style: _staffPerkPromotionDetailModal.styles.headerPromoAvailableStyle,
                          children: [(0, _jsxRuntime.jsx)(_text.Text, {
                            text: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantName,
                            style: _staffPerkPromotionDetailModal.styles.tenantNameTextStyle
                          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                            style: _staffPerkPromotionDetailModal.styles.caretRightStyle,
                            children: (0, _jsxRuntime.jsx)(_icons.CaretRight, {})
                          })]
                        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                          style: _staffPerkPromotionDetailModal.styles.wrapLocationComponentStyle,
                          children: (0, _jsxRuntime.jsx)(_text.Text, {
                            text: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tenantOutlets,
                            style: _staffPerkPromotionDetailModal.styles.caption1RegOutletStyle
                          })
                        })]
                      })]
                    })
                  }), !(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiUrl) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                    onPress: openIShopChangi,
                    activeOpacity: 0.5,
                    children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                      style: iShopChangiAvailabilityStyle,
                      children: [(0, _jsxRuntime.jsx)(_icons.IShopChangiPromoDetail, {
                        width: 40,
                        height: 40,
                        style: _staffPerkPromotionDetailModal.styles.tenantIconStyle
                      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                        style: _staffPerkPromotionDetailModal.styles.promoRightContentStyle,
                        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                          style: _staffPerkPromotionDetailModal.styles.headerPromoAvailableStyle,
                          children: [(0, _jsxRuntime.jsx)(_text.Text, {
                            text: "iShopChangi",
                            style: _staffPerkPromotionDetailModal.styles.tenantNameTextStyle
                          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                            style: _staffPerkPromotionDetailModal.styles.caretRightStyle,
                            children: (0, _jsxRuntime.jsx)(_icons.CaretRight, {})
                          })]
                        }), !(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiAvailability) && (0, _jsxRuntime.jsx)(_text.Text, {
                          text: staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.iShopChangiAvailability,
                          style: _staffPerkPromotionDetailModal.styles.caption1RegLocationStyle
                        })]
                      })]
                    })
                  })]
                }), !(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.howToUse) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: (0, _utils.handleCondition)(!(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tnc), _staffPerkPromotionDetailModal.styles.marginContentHowToUse, _staffPerkPromotionDetailModal.styles.marginContentHowToUseWithOutBreakline),
                  children: (0, _jsxRuntime.jsx)(_collapsible.default, {
                    customStyleTitle: isShopDineV2 && _staffPerkPromotionDetailModal.styles.collapsibleTitleStyle,
                    title: "HOW TO USE",
                    content: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                      activeOpacity: 1,
                      children: (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
                        source: {
                          html: (0, _htmlContent.formatHtmlContent)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.howToUse)
                        },
                        tagsStyles: isShopDineV2 ? _staffPerkPromotionDetailModal.tagStyle2V2 : _staffPerkPromotionDetailModal.tagStyle2,
                        contentWidth: _staffPerkPromotionDetailModal.width,
                        systemFonts: _staffPerkPromotionDetailModal.systemFonts
                      })
                    })
                  })
                }), !(0, _lodash.isEmpty)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tnc) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: _staffPerkPromotionDetailModal.styles.marginContentTerm,
                  children: (0, _jsxRuntime.jsx)(_collapsible.default, {
                    customStyleTitle: isShopDineV2 && _staffPerkPromotionDetailModal.styles.collapsibleTitleStyle,
                    title: "TERMS & CONDITIONS",
                    content: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                      activeOpacity: 1,
                      children: (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
                        source: {
                          html: (0, _htmlContent.formatHtmlContent)(staffPerkPromotionDetailPayload == null ? undefined : staffPerkPromotionDetailPayload.tnc)
                        },
                        tagsStyles: isShopDineV2 ? _staffPerkPromotionDetailModal.tagStyle2V2 : _staffPerkPromotionDetailModal.tagStyle2,
                        contentWidth: _staffPerkPromotionDetailModal.width,
                        systemFonts: _staffPerkPromotionDetailModal.systemFonts
                      })
                    })
                  })
                })]
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
            visible: staffPerkPromotionDetailRequest && !isShopDineV2,
            isTransparent: true
          }), isLoggedIn && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _staffPerkPromotionDetailModal.styles.staffPerkScanBtnStyle,
            onPress: openCRCard,
            activeOpacity: 0.5,
            children: isShopDineV2 ? (0, _jsxRuntime.jsx)(CRCardIcon, {
              width: 52,
              height: 52
            }) : (0, _jsxRuntime.jsx)(_reactNative2.ImageBackground, {
              source: _backgrounds.StaffPerkScan,
              style: _staffPerkPromotionDetailModal.styles.imageBackgroundStaffPerkScan,
              children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                source: {
                  uri: tierIcon
                },
                style: _staffPerkPromotionDetailModal.styles.imageTierHeaderStyle,
                resizeMode: "contain"
              })
            })
          }), (0, _jsxRuntime.jsx)(_changiRewardCardInsidePromo.default, {
            ref: changiRewardCardInsidePromo
          }), renderHeaderAnimated(), !staffPerkPromotionDetailRequest && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _staffPerkPromotionDetailModal.styles.crossPurple,
            onPress: closeScreen,
            children: (0, _jsxRuntime.jsx)(_icons.CrossPurpleWithBgr, {})
          })]
        });
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      isVisible: modalVisible,
      style: modalStyle,
      backdropColor: _theme.color.palette.black,
      backdropOpacity: 0.5,
      swipeDirection: "down",
      animationInTiming: TRANSITION_TIMING,
      animationOutTiming: TRANSITION_TIMING,
      backdropTransitionInTiming: TRANSITION_TIMING,
      backdropTransitionOutTiming: TRANSITION_TIMING,
      onSwipeComplete: closeScreen,
      propagateSwipe: true,
      onBackdropPress: closeScreen,
      onModalHide: callbackWhenClose,
      onModalShow: resetState,
      children: renderContentModal()
    });
  };
  var _default = exports.default = (0, _react.forwardRef)(StaffPerkPromotionDetailModal);
