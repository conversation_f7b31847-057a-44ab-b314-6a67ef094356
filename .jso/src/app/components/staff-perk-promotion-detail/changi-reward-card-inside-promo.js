  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _changiEcardModal = _$$_REQUIRE(_dependencyMap[9]);
  var _clipboard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[13]);
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _getConfigurationPermission = _$$_REQUIRE(_dependencyMap[16]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[18]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[19]);
  var _reactNativeDeviceBrightness = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _utils = _$$_REQUIRE(_dependencyMap[21]);
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[23]);
  var _text = _$$_REQUIRE(_dependencyMap[24]);
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[25]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[26]);
  var _staffPerkPromotionDetailController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _crForMonarchType = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _crForEconomyType = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[30]);
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[31]));
  var _staffPerkPromotionDetailModal = _$$_REQUIRE(_dependencyMap[32]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[33]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[34]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var presetCard = {
    Member: {
      linearGradientColor: ["#46268C", "#6950A1", "#B28DC1"],
      cornerColor: "#6D4FA5",
      dotConnerColor: "#563694",
      dotColor: ["#6D4FA5", "#AF8AC0"],
      boxShadow: "rgba(50, 25, 107, 0.4)",
      isStaff: false
    },
    Gold: {
      linearGradientColor: ["#916D28", "#947C4E", "#B29E78"],
      cornerColor: "#9C7C3D",
      dotConnerColor: "#936E27",
      dotColor: ["#9C7C3D", "#B29E78"],
      boxShadow: "rgba(121, 93, 38, 0.4)",
      isStaff: false
    },
    Platinum: {
      linearGradientColor: ["#29343C", "#2B4E63", "#456374"],
      cornerColor: "#57656D",
      dotConnerColor: "#29343C",
      dotColor: ["#57656D", "#55636B"],
      boxShadow: "rgba(42, 61, 73, 0.4)",
      isStaff: false
    },
    StaffMember: {
      linearGradientColor: ["#46268C", "#6950A1", "#B28DC1"],
      cornerColor: "#6D4FA5",
      dotConnerColor: "#563694",
      dotColor: ["#6D4FA5", "#AF8AC0"],
      boxShadow: "rgba(50, 25, 107, 0.4)",
      isStaff: true
    },
    StaffGold: {
      linearGradientColor: ["#916D28", "#947C4E", "#B29E78"],
      cornerColor: "#9C7C3D",
      dotConnerColor: "#936E27",
      dotColor: ["#9C7C3D", "#B29E78"],
      boxShadow: "rgba(121, 93, 38, 0.4)",
      isStaff: true
    },
    StaffPlatinum: {
      linearGradientColor: ["#29343C", "#2B4E63", "#456374"],
      cornerColor: "#57656D",
      dotConnerColor: "#29343C",
      dotColor: ["#57656D", "#55636B"],
      boxShadow: "rgba(42, 61, 73, 0.4)",
      isStaff: true
    }
  };
  var MAX_BRIGHTNESS = 1;
  var ChangiRewardCardInsidePromo = function ChangiRewardCardInsidePromo(_, ref) {
    var _useSelector, _rewardDetails$tierCo;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      newNavigation = _useState2[0],
      setNewNavigation = _useState2[1];
    var rewardDetails = (_useSelector = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData)) == null ? undefined : _useSelector.reward;
    var toastRef = (0, _react.useRef)(null);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var aemGroupTwoLoading = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.aemGroupTwoLoading);
    var horizontalContentCardDetailPayload = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.horizontalContentCardDetailPayload);
    var horizontalContentCardDetailMonarchPayload = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.horizontalContentCardDetailMonarchPayload);
    var contentBannerCard = {
      data: {
        list: horizontalContentCardDetailPayload
      }
    };
    var contentMonarchTierBannerCard = {
      data: {
        list: horizontalContentCardDetailMonarchPayload
      }
    };
    var isLoadingReward = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsFetching);
    var rewardsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsError);
    var tierCode = rewardDetails == null || (_rewardDetails$tierCo = rewardDetails.tierCode) == null ? undefined : _rewardDetails$tierCo.replace(" ", "");
    var selectedPresetTier = presetCard[tierCode] || presetCard.Member;
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoInternet = _useState6[0],
      setIsNoInternet = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isOpenChangiPay = _useState8[0],
      setIsOpenChangPay = _useState8[1];
    var brightnessCurrentLevel = (0, _react.useRef)(0);
    var isMonarchTier = (0, _utils.handleCondition)(tierCode === _changiRewardsMemberCard.Tier.Monarch || tierCode === _changiRewardsMemberCard.Tier.StaffMonarch, true, false);
    var _useGetConfigurationP = (0, _getConfigurationPermission.useGetConfigurationPermissionHelper)(),
      loadingGetConfig = _useGetConfigurationP.loadingGetConfig,
      getConfigApp = _useGetConfigurationP.getConfigApp,
      notifyDisableChangiPay = _useGetConfigurationP.notifyDisableChangiPay,
      notifyDisableChangiRewards = _useGetConfigurationP.notifyDisableChangiRewards;
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      modalVisible = _useState0[0],
      setModalVisible = _useState0[1];
    var isModalOpenRef = (0, _react.useRef)(false);
    var appState = (0, _react.useRef)(_reactNative.AppState.currentState);
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var modalStyle = {
      marginHorizontal: 0,
      marginBottom: 0,
      marginTop: _reactNative.Platform.select({
        ios: 60,
        android: (0, _utils.handleCondition)(inset == null ? undefined : inset.top, (inset == null ? undefined : inset.top) + 5, 25)
      }),
      overflow: "hidden",
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10
    };
    var showCard = function showCard(navigation) {
      setModalVisible(true);
      isModalOpenRef.current = true;
      setNewNavigation(navigation);
    };
    var closeCard = function closeCard() {
      setModalVisible(false);
      isModalOpenRef.current = false;
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        showCard: showCard
      };
    });
    (0, _react.useEffect)(function () {
      if (_reactNative.Platform.OS === "ios") {
        var subscription = /*#__PURE__*/function () {
          var _ref = (0, _asyncToGenerator2.default)(function* (nextAppState) {
            if ((0, _utils.ifAllTrue)([appState.current.match(/active/), nextAppState === "inactive", isModalOpenRef.current])) {
              _reactNativeDeviceBrightness.default.setBrightnessLevel(brightnessCurrentLevel.current);
            }
            if ((0, _utils.ifAllTrue)([appState.current.match(/inactive|background/), nextAppState === "active", isModalOpenRef.current])) {
              getBrightness();
            }
            appState.current = nextAppState;
          });
          return function subscription(_x) {
            return _ref.apply(this, arguments);
          };
        }();
        var listener = _reactNative.AppState.addEventListener("change", subscription);
        return function () {
          //   AppState.removeEventListener("change", subscription)
          listener.remove();
        };
      }
    }, []);
    var getBrightness = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var tBrightness = yield _reactNativeDeviceBrightness.default.getBrightnessLevel();
          brightnessCurrentLevel.current = tBrightness;
          _reactNativeDeviceBrightness.default.setBrightnessLevel(MAX_BRIGHTNESS);
        } catch (err) {}
      });
      return function getBrightness() {
        return _ref2.apply(this, arguments);
      };
    }();
    var getConfigECard = function getConfigECard() {
      getConfigApp({
        configKey: _constants.AppConfigPermissionTypes.changiappEcardEnabled,
        callbackSuccess: function callbackSuccess() {
          return setLoading(false);
        },
        callbackFailure: function callbackFailure() {
          checkConnection().then(function (res) {
            if (res) {
              setLoading(false);
              closeScreen();
              notifyDisableChangiRewards();
            } else {
              setIsNoInternet(true);
            }
          });
        }
      });
    };
    var getInitialConfig = function getInitialConfig() {
      getBrightness();
      getConfigECard();
    };
    var checkConnection = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkConnection() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_Ecard");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = newNavigation == null ? undefined : newNavigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_Ecard", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, []);
    (0, _react.useEffect)(function () {
      handleFetchData();
    }, [isLoggedIn, profilePayload]);
    var handleFetchData = function handleFetchData() {
      checkConnection().then(function (res) {
        if (res) {
          if (isNoInternet) {
            setIsNoInternet(false);
          }
          if ((0, _utils.ifAllTrue)([isLoggedIn, profilePayload])) {
            dispatch(_forYouRedux.default.rewardsRequest((profilePayload == null ? undefined : profilePayload.cardNo) || ""));
          }
        } else {
          setIsNoInternet(true);
        }
      });
    };
    var onPressCopy = function onPressCopy() {
      _clipboard.default.setString(rewardDetails == null ? undefined : rewardDetails.cardNo);
      onCopyQrCode();
    };
    var onCopyQrCode = function onCopyQrCode() {
      toastRef.current.show();
    };
    var scanToPay = function scanToPay() {
      checkConnection().then(function (res) {
        if (res) {
          if (isNoInternet) {
            setIsNoInternet(false);
          }
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeWallet, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeWallet, "1"));
          getConfigApp({
            configKey: _constants.AppConfigPermissionTypes.changiappWalletEnabled,
            callbackSuccess: function callbackSuccess() {
              _staffPerkPromotionDetailController.default.setActionWhenCloseModal(newNavigation, _staffPerkPromotionDetailModal.ModalAction.OPEN_CHANGIPAY);
              _reactNativeDeviceBrightness.default.setBrightnessLevel(brightnessCurrentLevel.current);
              _staffPerkPromotionDetailController.default.hideModal();
            },
            callbackFailure: function callbackFailure() {
              return notifyDisableChangiPay();
            }
          });
        } else {
          setIsNoInternet(true);
        }
      });
    };
    var closeScreen = function closeScreen() {
      _reactNativeDeviceBrightness.default.setBrightnessLevel(brightnessCurrentLevel.current);
      closeCard();
    };
    var onReloadPage = function onReloadPage() {
      getConfigECard();
      handleFetchData();
    };
    var TRANSITION_TIMING = 400;
    return (0, _jsxRuntime.jsxs)(_reactNativeModal.default, {
      isVisible: modalVisible,
      onModalWillHide: function onModalWillHide() {
        closeCard();
      },
      style: modalStyle,
      backdropColor: _theme.color.palette.black,
      backdropOpacity: 0.5,
      swipeDirection: "down",
      animationInTiming: TRANSITION_TIMING,
      animationOutTiming: TRANSITION_TIMING,
      backdropTransitionInTiming: TRANSITION_TIMING,
      backdropTransitionOutTiming: TRANSITION_TIMING,
      onSwipeComplete: closeScreen,
      propagateSwipe: true,
      onModalWillShow: getInitialConfig,
      onModalHide: function onModalHide() {
        _reactNativeDeviceBrightness.default.setBrightnessLevel(brightnessCurrentLevel.current);
        if (isOpenChangiPay) {
          _staffPerkPromotionDetailController.default.hideModal();
          setIsOpenChangPay(false);
        }
      },
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        barStyle: "light-content"
      }), (0, _utils.handleCondition)(isMonarchTier, (0, _jsxRuntime.jsx)(_crForMonarchType.default, {
        selectedPresetTier: selectedPresetTier,
        closeScreen: closeScreen,
        profilePayload: profilePayload,
        rewardDetails: rewardDetails,
        onPressCopy: onPressCopy,
        scanToPay: scanToPay,
        newNavigation: newNavigation,
        contentBannerCard: contentMonarchTierBannerCard
      }), (0, _jsxRuntime.jsx)(_crForEconomyType.default, {
        selectedPresetTier: selectedPresetTier,
        closeScreen: closeScreen,
        profilePayload: profilePayload,
        rewardDetails: rewardDetails,
        onPressCopy: onPressCopy,
        scanToPay: scanToPay,
        newNavigation: newNavigation,
        contentBannerCard: contentBannerCard
      })), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: loadingGetConfig
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: aemGroupTwoLoading || isLoadingReward || loadingGetConfig || loading,
        isTransparent: true
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        headerBackgroundColor: "transparent",
        visible: isNoInternet,
        testID: `ChangiECards__ErrorOverlayNoConnection`,
        onReload: onReloadPage,
        onBack: closeScreen,
        hideScreenHeader: false,
        storyMode: true
      }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        storyMode: true,
        header: true,
        hideScreenHeader: false,
        visible: rewardsError,
        onReload: onReloadPage,
        onBack: closeScreen,
        testID: `ChangiECards__ErrorOverlay`,
        accessibilityLabel: `ChangiECards__ErrorOverlay`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
        overlayStyle: _changiEcardModal.styles.overlayStyle
      }), (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastRef,
        style: feedBackToastStyle,
        position: "custom",
        textStyle: toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("changiRewardsEcard.cardNoCopied")
      })]
    });
  };
  var feedBackToastStyle = {
    bottom: 40
  };
  var toastTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.whiteGrey,
    width: "80%"
  });
  var _default = exports.default = (0, _react.forwardRef)(ChangiRewardCardInsidePromo);
