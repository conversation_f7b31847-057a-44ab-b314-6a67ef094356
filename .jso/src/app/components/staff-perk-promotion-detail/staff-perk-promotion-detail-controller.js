  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _StaffPerkPromotionDetailController;
  var StaffPerkPromotionDetailController = exports.default = /*#__PURE__*/(0, _createClass2.default)(function StaffPerkPromotionDetailController() {
    (0, _classCallCheck2.default)(this, StaffPerkPromotionDetailController);
  });
  _StaffPerkPromotionDetailController = StaffPerkPromotionDetailController;
  StaffPerkPromotionDetailController.setAppRef = function (ref) {
    _StaffPerkPromotionDetailController.appRef = ref;
  };
  StaffPerkPromotionDetailController.setModalRef = function (ref) {
    _StaffPerkPromotionDetailController.modalRef = ref;
  };
  StaffPerkPromotionDetailController.showModal = function (nav, item, showWithoutImage) {
    var _StaffPerkPromotionDe;
    (_StaffPerkPromotionDe = _StaffPerkPromotionDetailController.modalRef) == null || (_StaffPerkPromotionDe = _StaffPerkPromotionDe.current) == null || _StaffPerkPromotionDe.show(nav, item, showWithoutImage);
  };
  StaffPerkPromotionDetailController.setActionWhenCloseModal = function (nav, action) {
    var _StaffPerkPromotionDe2;
    (_StaffPerkPromotionDe2 = _StaffPerkPromotionDetailController.modalRef) == null || (_StaffPerkPromotionDe2 = _StaffPerkPromotionDe2.current) == null || _StaffPerkPromotionDe2.setActionWhenCloseModal(nav, action);
  };
  StaffPerkPromotionDetailController.hideModal = function () {
    var _StaffPerkPromotionDe3;
    (_StaffPerkPromotionDe3 = _StaffPerkPromotionDetailController.modalRef) == null || (_StaffPerkPromotionDe3 = _StaffPerkPromotionDe3.current) == null || _StaffPerkPromotionDe3.hide();
  };
