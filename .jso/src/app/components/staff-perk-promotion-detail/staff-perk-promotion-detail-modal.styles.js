  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.width = exports.tagStyleForAvailability = exports.tagStyle2V2 = exports.tagStyle2 = exports.tagStyle = exports.systemFonts = exports.styles = exports.height = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeRenderHtml = _$$_REQUIRE(_dependencyMap[5]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = exports.width = _Dimensions$get.width,
    height = exports.height = _Dimensions$get.height;
  var rightContentWidth = width - 100;
  var tagStyle = exports.tagStyle = {
    ul: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      paddingLeft: 11,
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center"
    }),
    ol: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center"
    }),
    li: Object.assign({}, _text.presets.bodyTextRegular, {
      lineHeight: 20,
      fontSize: 16,
      padding: 0,
      margin: 0,
      marginBottom: 5,
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center"
    }),
    p: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      marginBottom: 10,
      color: _theme.color.palette.darkestGrey,
      textAlignVertical: "center"
    })
  };
  var tagStyle2 = exports.tagStyle2 = {
    ul: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 15,
      lineHeight: 20,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    ol: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 15,
      lineHeight: 20,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    li: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 15,
      lineHeight: 20,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    p: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 15,
      lineHeight: 20,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    })
  };
  var tagStyle2V2 = exports.tagStyle2V2 = {
    ul: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    ol: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    li: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    p: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      padding: 0,
      marginTop: 16,
      textAlignVertical: "center"
    })
  };
  var tagStyleForAvailability = exports.tagStyleForAvailability = {
    ul: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      marginBottom: 4
    }),
    ol: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      marginBottom: 4
    }),
    li: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      marginBottom: 4
    }),
    p: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18,
      margin: 0,
      marginBottom: 4
    })
  };
  var styles = exports.styles = _reactNative.StyleSheet.create({
    collapsibleTitleStyle: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 22,
      textAlignVertical: "center",
      letterSpacing: 0.06
    },
    contentModalStyle: {
      flex: 1,
      paddingBottom: 150
    },
    backgroundImage: {
      height: 250,
      width: width
    },
    caption1LocationStyle: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4,
      textAlign: "left"
    }),
    caption1RegOutletStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18
    }),
    caption1RegLocationStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      lineHeight: 18,
      marginBottom: 4
    }),
    caretRightStyle: {
      alignItems: "center",
      height: 24,
      justifyContent: "center"
    },
    contentPromo: {
      padding: 24
    },
    contentPromoV2: {
      paddingHorizontal: 24,
      paddingBottom: 24
    },
    crossPurple: {
      position: "absolute",
      right: 14,
      top: 18
    },
    headerModalWithoutImageStyle: {
      height: 40
    },
    headerModalWithoutImageStyleV2: {
      height: 50
    },
    headerPromoAvailableStyle: {
      alignItems: "flex-start",
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 4,
      width: rightContentWidth
    },
    imageBackgroundStaffPerkScan: {
      alignItems: "center",
      height: 44,
      justifyContent: "center",
      width: 44
    },
    imageTierHeaderStyle: {
      height: 20,
      width: 20
    },
    marginContentHowToUse: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderColor: _theme.color.transparent,
      borderWidth: 1,
      marginTop: 24
    },
    marginContentHowToUseWithOutBreakline: {
      marginTop: 24
    },
    marginContentTerm: {
      marginTop: 16
    },
    offerDescription: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 15,
      lineHeight: 20,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    offerTitle: Object.assign({}, _text.presets.h1, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 28,
      lineHeight: 36,
      marginTop: 16,
      textAlignVertical: "center"
    }),
    offerTitleV2: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 24,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 32,
      marginTop: 16,
      textAlignVertical: "center"
    },
    openWebViewSection: {
      alignItems: "center",
      flexDirection: "row",
      marginTop: 17
    },
    physicalTenantContentStyle: {
      alignItems: "flex-start",
      flexDirection: "row",
      marginTop: 16,
      width: "100%",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    physicalTenantContentStyleV2: {
      alignItems: "flex-start",
      flexDirection: "row",
      marginTop: 12,
      width: "100%",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    physicalTenantStyle: {
      height: 50,
      width: "100%"
    },
    physicalTenantStyleV2: {
      height: 94,
      width: "100%"
    },
    promoAvailabilityText: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 18,
      letterSpacing: 0.06,
      lineHeight: 22
    }),
    promoAvailabilityTextV2: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16
    },
    promoRightContentStyle: {
      width: rightContentWidth
    },
    staffPerkScanBtnStyle: Object.assign({
      borderRadius: 22,
      bottom: 40,
      height: 44,
      position: "absolute",
      right: 12,
      width: 44
    }, _theme.shadow.secondaryShadow, {
      backgroundColor: _theme.color.palette.whiteGrey,
      justifyContent: 'center',
      alignItems: 'center'
    }),
    tagPromo: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 99,
      justifyContent: "center",
      marginLeft: 8,
      paddingHorizontal: 12,
      paddingVertical: 6
    },
    tenantIconStyle: {
      height: 40,
      width: 40,
      marginRight: 12
    },
    tenantNameTextStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      lineHeight: 20,
      textAlignVertical: "center",
      width: "90%"
    }),
    textLinkWebView: Object.assign({}, _text.presets.textLink, {
      color: _theme.color.palette.lightPurple,
      marginLeft: 8
    }),
    textLinkWebViewV2: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.lightPurple,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 20,
      marginLeft: 8
    },
    textTagPromo: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.lightPurple,
      letterSpacing: 0.04
    }),
    wrapHandleTenantAvailabilityTextStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      textAlignVertical: "center"
    },
    wrapLocationComponentStyle: {
      marginBottom: 16
    }
  });
  var systemFonts = exports.systemFonts = [].concat((0, _toConsumableArray2.default)(_reactNativeRenderHtml.defaultSystemFonts), ["Lato-Regular", "Lato-Bold"]);
