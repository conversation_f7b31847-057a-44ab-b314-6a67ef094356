  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.WeatherFlightCard = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _weatherFlightCard = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _envParams = _$$_REQUIRE(_dependencyMap[13]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[14]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _queries = _$$_REQUIRE(_dependencyMap[16]);
  var _flyHelper = _$$_REQUIRE(_dependencyMap[17]);
  var _listWeatherPopup = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _weatherFlightCardLoading = _$$_REQUIRE(_dependencyMap[19]);
  var _weatherFlightCardError = _$$_REQUIRE(_dependencyMap[20]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[21]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  // Dedicated test ID prefix for professional usage
  var TEST_ID_PREFIX = "weatherFlightCard_";
  var WeatherFlightCard = exports.WeatherFlightCard = function WeatherFlightCard(_ref) {
    var _flightDetailsData$ai5, _flightDetailsData$ai6, _flightDetailsData$vi3, _weatherData$dailyFor3, _destinationsFocusing, _weatherData$isDayTim;
    var section = _ref.section,
      flightDetailsData = _ref.flightDetailsData,
      isFlightSaved = _ref.isFlightSaved,
      onSaveFlight = _ref.onSaveFlight;
    var title = section.title;
    var _ref2 = flightDetailsData || {},
      viaAirportDetails = _ref2.viaAirportDetails;
    var _useState = (0, _react.useState)(Boolean(isFlightSaved)),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isExpanded = _useState2[0],
      setIsExpanded = _useState2[1];
    var _useState3 = (0, _react.useState)(),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      destinationsFocusing = _useState4[0],
      setDestinationsFocusing = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isLoadingWeatherData = _useState6[0],
      setIsLoadingWeatherData = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isErrorWeatherData = _useState8[0],
      setIsErrorWeatherData = _useState8[1];
    var _useState9 = (0, _react.useState)({}),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      weatherDestinations = _useState0[0],
      setWeatherDestinations = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      visibleWeatherPopup = _useState10[0],
      setVisibleWeatherPopup = _useState10[1];
    // the destination is SIN if the flight destination is ARR
    // otherwise, it is the airport code of the flight destination
    var _useState11 = (0, _react.useState)({
        name: "",
        code: ""
      }),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      airportDestination = _useState12[0],
      setAirportDestination = _useState12[1];
    (0, _react.useEffect)(function () {
      setIsExpanded(Boolean(isFlightSaved));
    }, [isFlightSaved]);
    var handleGetWeather = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (input) {
        setIsErrorWeatherData(false);
        setIsLoadingWeatherData(true);
        try {
          var _env, _env2, _response$data;
          var response = yield (0, _request.default)({
            url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.getWeatherQuery, {
              input: {
                airport: input.airport
              }
            }),
            parameters: {},
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
            }
          });
          var result = response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getWeather;
          if ((response == null ? undefined : response.statusCode) === 200 && (result == null ? undefined : result.status) !== "ERROR") {
            setWeatherDestinations(function (prev) {
              return Object.assign({}, prev, (0, _defineProperty2.default)({}, input.airport, result == null ? undefined : result.data));
            });
          } else {
            setIsErrorWeatherData(true);
          }
        } catch (error) {
          setIsErrorWeatherData(true);
        } finally {
          setIsLoadingWeatherData(false);
        }
      });
      return function (_x) {
        return _ref3.apply(this, arguments);
      };
    }(), []);
    (0, _react.useEffect)(function () {
      var _flightDetailsData$ai, _flightDetailsData$ai2, _flightDetailsData$vi;
      if (flightDetailsData != null && (_flightDetailsData$ai = flightDetailsData.airportDetails) != null && _flightDetailsData$ai.name && flightDetailsData != null && (_flightDetailsData$ai2 = flightDetailsData.airportDetails) != null && _flightDetailsData$ai2.code) {
        var _flightDetailsData$ai3, _flightDetailsData$ai4;
        var airportDestinationCode = (flightDetailsData == null ? undefined : flightDetailsData.direction) === "ARR" ? "SIN" : flightDetailsData == null || (_flightDetailsData$ai3 = flightDetailsData.airportDetails) == null ? undefined : _flightDetailsData$ai3.code;
        var airportDestinationName = (flightDetailsData == null ? undefined : flightDetailsData.direction) === "ARR" ? "Singapore" : flightDetailsData == null || (_flightDetailsData$ai4 = flightDetailsData.airportDetails) == null ? undefined : _flightDetailsData$ai4.name;
        setAirportDestination({
          name: airportDestinationName,
          code: airportDestinationCode
        });
        setDestinationsFocusing({
          name: airportDestinationName,
          code: airportDestinationCode
        });
        handleGetWeather({
          airport: airportDestinationCode
        });
      }
      if (flightDetailsData != null && (_flightDetailsData$vi = flightDetailsData.viaAirportDetails) != null && _flightDetailsData$vi.code) {
        var _flightDetailsData$vi2;
        handleGetWeather({
          airport: flightDetailsData == null || (_flightDetailsData$vi2 = flightDetailsData.viaAirportDetails) == null ? undefined : _flightDetailsData$vi2.code
        });
      }
    }, [flightDetailsData == null || (_flightDetailsData$ai5 = flightDetailsData.airportDetails) == null ? undefined : _flightDetailsData$ai5.name, flightDetailsData == null || (_flightDetailsData$ai6 = flightDetailsData.airportDetails) == null ? undefined : _flightDetailsData$ai6.code, flightDetailsData == null || (_flightDetailsData$vi3 = flightDetailsData.viaAirportDetails) == null ? undefined : _flightDetailsData$vi3.code]);
    var weatherData = (0, _react.useMemo)(function () {
      if (destinationsFocusing) {
        return weatherDestinations[destinationsFocusing.code];
      }
      return null;
    }, [destinationsFocusing, weatherDestinations]);
    var collapTitles = (0, _react.useMemo)(function () {
      var _airportDestination$n;
      if (!flightDetailsData) {
        return "";
      }
      var result = (_airportDestination$n = airportDestination == null ? undefined : airportDestination.name) != null ? _airportDestination$n : "";
      if (viaAirportDetails) {
        result += `${result ? " | " : ""}${viaAirportDetails.name}`;
      }
      return result;
    }, [airportDestination, viaAirportDetails]);
    var onPressExpandCollapse = function onPressExpandCollapse() {
      if (isFlightSaved) {
        setIsExpanded(function (prev) {
          return !prev;
        });
      } else {
        // If the flight is not saved, save the flight.
        onSaveFlight("Weather");
      }
    };
    var onPressSeeAll = function onPressSeeAll() {
      setVisibleWeatherPopup(true);
    };
    var renderCollapseWeather = function renderCollapseWeather() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _weatherFlightCard.styles.containerCollapseTiles,
        children: [(0, _jsxRuntime.jsx)(_icons.CircleArrowUpward, {}), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _weatherFlightCard.styles.containerCollapseTilesTitle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: collapTitles,
            style: _weatherFlightCard.styles.textCollapseTiles,
            testID: `${TEST_ID_PREFIX}collapseTitleText`,
            accessibilityLabel: `${TEST_ID_PREFIX}collapseTitleText`
          })
        })]
      });
    };
    var renderWeatherDestinationTab = function renderWeatherDestinationTab() {
      if (!viaAirportDetails || !destinationsFocusing) {
        return null;
      }
      var destinations = [{
        name: airportDestination.name,
        code: airportDestination.code
      }, {
        name: viaAirportDetails.name,
        code: viaAirportDetails.code
      }];
      var onPressTab = function onPressTab(item) {
        setDestinationsFocusing(item);
      };
      return (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: _weatherFlightCard.styles.containerTab,
        testID: `${TEST_ID_PREFIX}destinationScrollView`,
        accessibilityLabel: `${TEST_ID_PREFIX}destinationScrollView`,
        children: destinations.map(function (item, index) {
          var isSelected = destinationsFocusing.name === item.name;
          return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: [_weatherFlightCard.styles.btnTab, isSelected && _weatherFlightCard.styles.btnTabFocused],
            onPress: function onPress() {
              return onPressTab(item);
            },
            testID: `${TEST_ID_PREFIX}destinationTab_${index}`,
            accessibilityLabel: `${TEST_ID_PREFIX}destinationTab_${index}`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Bold",
              text: item.name,
              style: isSelected ? _weatherFlightCard.styles.labelTabFocused : _weatherFlightCard.styles.labelTab,
              testID: `${TEST_ID_PREFIX}destinationText_${index}`,
              accessibilityLabel: `${TEST_ID_PREFIX}destinationText_${index}`
            })
          }, index);
        })
      });
    };
    var renderWeatherContent = function renderWeatherContent() {
      var _weatherData$dailyFor, _weatherData$dailyFor2;
      if (!(weatherData != null && (_weatherData$dailyFor = weatherData.dailyForecasts) != null && _weatherData$dailyFor.length) || (weatherData == null || (_weatherData$dailyFor2 = weatherData.dailyForecasts) == null ? undefined : _weatherData$dailyFor2.length) < 3) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _weatherFlightCard.styles.containerWeatherContent,
          testID: `${TEST_ID_PREFIX}weatherContent`,
          accessibilityLabel: `${TEST_ID_PREFIX}weatherContent`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            tx: "flightDetailV2.weatherFlightCard.unavailable",
            style: _weatherFlightCard.styles.textAlmostBackColor,
            testID: `${TEST_ID_PREFIX}unavailableLabel`,
            accessibilityLabel: `${TEST_ID_PREFIX}unavailableLabel`
          })
        });
      }
      var dailyForecasts = weatherData.dailyForecasts,
        isDayTime = weatherData.isDayTime,
        currentTemperature = weatherData.currentTemperature;
      var today = dailyForecasts[0];
      var tomorrow = dailyForecasts[1];
      var theNextTwoDays = dailyForecasts[2];
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _weatherFlightCard.styles.containerWeatherContent,
        testID: `${TEST_ID_PREFIX}weatherContent`,
        accessibilityLabel: `${TEST_ID_PREFIX}weatherContent`,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _weatherFlightCard.styles.containerToday,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Regular",
              tx: "flightDetailV2.weatherFlightCard.today",
              style: _weatherFlightCard.styles.textDarkestGrey,
              testID: `${TEST_ID_PREFIX}todayLabel`,
              accessibilityLabel: `${TEST_ID_PREFIX}todayLabel`
            }), (0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: isDayTime ? today.dayIconUrl : today.nightIconUrl
              },
              style: _weatherFlightCard.styles.imgToday,
              resizeMode: "stretch"
            })]
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h3Regular",
            text: `${(0, _flyHelper.roundTemperature)(currentTemperature)}`,
            style: _weatherFlightCard.styles.todayDegreeLabel,
            testID: `${TEST_ID_PREFIX}currentTemperature`,
            accessibilityLabel: `${TEST_ID_PREFIX}currentTemperature`
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _weatherFlightCard.styles.containerStatusToday,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Regular",
              text: `H: ${(0, _flyHelper.roundTemperature)(today.maximumTemperature)}    L: ${(0, _flyHelper.roundTemperature)(today.minimumTemperature)}`,
              style: _weatherFlightCard.styles.textDarkestGrey,
              testID: `${TEST_ID_PREFIX}todayHighLow`,
              accessibilityLabel: `${TEST_ID_PREFIX}todayHighLow`
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "bodyTextBlack",
              text: isDayTime ? today.dayIconPhrase : today.nightIconPhrase,
              style: _weatherFlightCard.styles.textDarkestGrey,
              testID: `${TEST_ID_PREFIX}todayIconPhrase`,
              accessibilityLabel: `${TEST_ID_PREFIX}todayIconPhrase`
            })]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _weatherFlightCard.styles.containerNextDay,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _weatherFlightCard.styles.containerDegreeItemTmr,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Regular",
              tx: "flightDetailV2.weatherFlightCard.tomorrow",
              style: _weatherFlightCard.styles.textDarkestGrey,
              testID: `${TEST_ID_PREFIX}tomorrowLabel`,
              accessibilityLabel: `${TEST_ID_PREFIX}tomorrowLabel`
            }), (0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: tomorrow.dayIconUrl
              },
              style: _weatherFlightCard.styles.imgTmr,
              resizeMode: "stretch"
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _weatherFlightCard.styles.containerDegreeTmr,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "subTitleBold",
                text: (0, _flyHelper.roundTemperature)(tomorrow.maximumTemperature),
                style: _weatherFlightCard.styles.textDarkestGrey,
                testID: `${TEST_ID_PREFIX}tomorrowMaxTemp`,
                accessibilityLabel: `${TEST_ID_PREFIX}tomorrowMaxTemp`
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption2Bold",
                text: ` / ${(0, _flyHelper.roundTemperature)(tomorrow.minimumTemperature)}`,
                style: _weatherFlightCard.styles.textDarkestGrey,
                testID: `${TEST_ID_PREFIX}tomorrowMinTemp`,
                accessibilityLabel: `${TEST_ID_PREFIX}tomorrowMinTemp`
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption2Regular",
              text: tomorrow.dayIconPhrase,
              style: _weatherFlightCard.styles.weatherLabel,
              testID: `${TEST_ID_PREFIX}tomorrowIconPhrase`,
              accessibilityLabel: `${TEST_ID_PREFIX}tomorrowIconPhrase`
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _weatherFlightCard.styles.containerDegreeItem,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Regular",
              text: (0, _moment.default)(theNextTwoDays.date).format("ddd, D MMM"),
              style: _weatherFlightCard.styles.textDarkestGrey,
              testID: `${TEST_ID_PREFIX}nextDayDate`,
              accessibilityLabel: `${TEST_ID_PREFIX}nextDayDate`
            }), (0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: theNextTwoDays.dayIconUrl
              },
              style: _weatherFlightCard.styles.imgTmr,
              resizeMode: "stretch"
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _weatherFlightCard.styles.containerDegreeTmr,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "subTitleBold",
                text: (0, _flyHelper.roundTemperature)(theNextTwoDays.maximumTemperature),
                style: _weatherFlightCard.styles.textDarkestGrey,
                testID: `${TEST_ID_PREFIX}nextDayMaxTemp`,
                accessibilityLabel: `${TEST_ID_PREFIX}nextDayMaxTemp`
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption2Bold",
                text: ` / ${(0, _flyHelper.roundTemperature)(theNextTwoDays.minimumTemperature)}`,
                style: _weatherFlightCard.styles.textDarkestGrey,
                testID: `${TEST_ID_PREFIX}nextDayMinTemp`,
                accessibilityLabel: `${TEST_ID_PREFIX}nextDayMinTemp`
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption2Regular",
              text: theNextTwoDays.dayIconPhrase,
              style: _weatherFlightCard.styles.weatherLabel,
              testID: `${TEST_ID_PREFIX}nextDayIconPhrase`,
              accessibilityLabel: `${TEST_ID_PREFIX}nextDayIconPhrase`
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _weatherFlightCard.styles.containerBtn,
            onPress: onPressSeeAll,
            testID: `${TEST_ID_PREFIX}seeAllButton`,
            accessibilityLabel: `${TEST_ID_PREFIX}seeAllButton`,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _weatherFlightCard.styles.containerLabel,
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "caption1Bold",
                  tx: "flightDetailV2.weatherFlightCard.seeMore",
                  style: _weatherFlightCard.styles.btnSee5DaysLabel,
                  testID: `${TEST_ID_PREFIX}seeMoreText`,
                  accessibilityLabel: `${TEST_ID_PREFIX}seeMoreText`
                })
              }), (0, _jsxRuntime.jsx)(_icons.RightArrowCircle, {
                width: 20,
                height: 20,
                fill: _theme.color.palette.lightPurple
              })]
            })
          })]
        })]
      });
    };
    var renderWeatherCard = function renderWeatherCard() {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [renderWeatherDestinationTab(), renderWeatherContent()]
      });
    };
    var renderExpandCollapseWeather = function renderExpandCollapseWeather() {
      if (isExpanded) {
        return renderWeatherCard();
      }
      return renderCollapseWeather();
    };
    var renderExpandCollapseIcon = function renderExpandCollapseIcon() {
      if (isExpanded) {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _weatherFlightCard.styles.containerExpandCollapseIcon,
          onPress: onPressExpandCollapse,
          testID: `${TEST_ID_PREFIX}expandCollapseIcon`,
          accessibilityLabel: `${TEST_ID_PREFIX}expandCollapseIcon`,
          children: (0, _jsxRuntime.jsx)(_icons.AccordionUp, {})
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _weatherFlightCard.styles.containerExpandCollapseIcon,
        onPress: onPressExpandCollapse,
        testID: `${TEST_ID_PREFIX}expandCollapseIcon`,
        accessibilityLabel: `${TEST_ID_PREFIX}expandCollapseIcon`,
        children: (0, _jsxRuntime.jsx)(_icons.AccordionDown, {})
      });
    };
    var retryGetWeather = function retryGetWeather() {
      var _flightDetailsData$vi4;
      if (airportDestination != null && airportDestination.code) {
        handleGetWeather({
          airport: airportDestination == null ? undefined : airportDestination.code
        });
      }
      if (flightDetailsData != null && (_flightDetailsData$vi4 = flightDetailsData.viaAirportDetails) != null && _flightDetailsData$vi4.code) {
        var _flightDetailsData$vi5;
        handleGetWeather({
          airport: flightDetailsData == null || (_flightDetailsData$vi5 = flightDetailsData.viaAirportDetails) == null ? undefined : _flightDetailsData$vi5.code
        });
      }
    };
    if (isLoadingWeatherData) {
      return (0, _jsxRuntime.jsx)(_weatherFlightCardLoading.WeatherFlightCardLoading, {});
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [title && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _weatherFlightCard.styles.titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          preset: "caption1Bold",
          numberOfLines: 1,
          style: _weatherFlightCard.styles.textAlmostBackColor,
          testID: `${TEST_ID_PREFIX}titleText`,
          accessibilityLabel: `${TEST_ID_PREFIX}titleText`
        })
      }), isErrorWeatherData ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _weatherFlightCard.styles.viewError,
        children: (0, _jsxRuntime.jsx)(_weatherFlightCardError.WeatherFlightCardError, {
          onRetry: retryGetWeather
        })
      }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [renderExpandCollapseWeather(), renderExpandCollapseIcon()]
      }), (0, _jsxRuntime.jsx)(_listWeatherPopup.default, {
        visible: visibleWeatherPopup,
        dailyForecasts: (_weatherData$dailyFor3 = weatherData == null ? undefined : weatherData.dailyForecasts) != null ? _weatherData$dailyFor3 : [],
        destinationName: (_destinationsFocusing = destinationsFocusing == null ? undefined : destinationsFocusing.name) != null ? _destinationsFocusing : "",
        onClosed: function onClosed() {
          return setVisibleWeatherPopup(false);
        },
        onBackPressed: function onBackPressed() {
          return setVisibleWeatherPopup(false);
        },
        onModalHide: function onModalHide() {
          return setVisibleWeatherPopup(false);
        },
        isDayTime: (_weatherData$isDayTim = weatherData == null ? undefined : weatherData.isDayTime) != null ? _weatherData$isDayTim : false
      })]
    });
  };
