  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.WeatherFlightCardLoading = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _exploreStaffPerkItem = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var WeatherFlightCardLoading = exports.WeatherFlightCardLoading = _react.default.memo(function () {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.viewContainer,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
        shimmerStyle: styles.componentTop
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
        shimmerStyle: styles.componentBottom
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    viewContainer: {
      paddingHorizontal: 16
    },
    componentTop: {
      width: 200,
      height: 24,
      borderRadius: 4
    },
    componentBottom: {
      width: "100%",
      height: 56,
      marginTop: 16,
      borderRadius: 4
    }
  });
