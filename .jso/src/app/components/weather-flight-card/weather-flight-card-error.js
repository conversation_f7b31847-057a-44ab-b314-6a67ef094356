  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.WeatherFlightCardError = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var testID = "FlightDetail";
  var WeatherFlightCardError = exports.WeatherFlightCardError = _react.default.memo(function (props) {
    var onRetry = props.onRetry;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: "flightDetailV2.flightInfo.weatherError",
        style: styles.txtContent,
        testID: `${testID}__title_weather_error`,
        accessibilityLabel: `${testID}__title_weather_error`
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.buttonRetry,
        onPress: onRetry,
        testID: `${testID}__ButtonRetry`,
        accessibilityLabel: `${testID}__ButtonRetry`,
        children: [(0, _jsxRuntime.jsx)(_icons.Refresh, {
          width: 18,
          height: 18
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.flightInfo.retry",
          style: styles.txtButton,
          testID: `${testID}__Text_ButtonRetry`,
          accessibilityLabel: `${testID}__Text_ButtonRetry`
        })]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      paddingHorizontal: 16,
      flexDirection: 'row',
      alignItems: 'center'
    },
    txtContent: Object.assign({}, _text.presets.caption1Regular, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.darkestGrey
    }),
    buttonRetry: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 4
    },
    txtButton: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.lightPurple,
      marginLeft: 2
    })
  });
