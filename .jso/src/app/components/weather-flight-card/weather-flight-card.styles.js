  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    titleContainer: {
      flex: 1,
      paddingTop: 1,
      paddingBottom: 4,
      paddingHorizontal: 16
    },
    textDarkestGrey: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "left"
    },
    textAlmostBackColor: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    containerExpandCollapseIcon: {
      alignSelf: "center",
      width: 100,
      alignItems: "center",
      paddingTop: 16
    },
    containerCollapseTiles: {
      flexDirection: "row",
      paddingHorizontal: 16,
      paddingTop: 16,
      alignItems: 'center'
    },
    containerCollapseTilesTitle: {
      flexDirection: "row",
      flex: 1,
      marginLeft: 8
    },
    textCollapseTiles: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "left"
    },
    containerTab: {
      flexDirection: "row",
      marginTop: 16,
      paddingHorizontal: 16
    },
    btnTab: {
      paddingVertical: 6,
      paddingHorizontal: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: _theme.color.palette.lightPurple,
      marginRight: 8
    },
    btnTabFocused: {
      backgroundColor: _theme.color.palette.lightPurple
    },
    labelTab: {
      color: _theme.color.palette.lightPurple
    },
    labelTabFocused: {
      color: _theme.color.palette.whiteGrey
    },
    containerWeatherContent: {
      marginTop: 16,
      paddingHorizontal: 16
    },
    containerToday: {
      flexDirection: 'row',
      alignItems: 'flex-end'
    },
    containerNextDay: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 24
    },
    containerDegreeItem: {
      flex: 1,
      marginRight: 4
    },
    containerDegreeItemTmr: {
      flex: 1,
      marginRight: 16
    },
    imgToday: {
      width: 67,
      height: 40,
      marginTop: 4
    },
    todayDegreeLabel: {
      color: _theme.color.palette.darkestGrey,
      marginLeft: 4,
      marginRight: 12
    },
    imgTmr: {
      width: 53,
      height: 32,
      marginTop: 4
    },
    containerDegreeTmr: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingTop: 4
    },
    weatherLabel: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "left",
      flex: 1
    },
    containerBtn: {
      flex: 1,
      flexDirection: 'row'
    },
    containerLabel: {
      alignItems: 'center'
    },
    btnSee5DaysLabel: {
      color: _theme.color.palette.lightPurple,
      marginBottom: 4,
      textAlign: 'left'
    },
    viewError: {
      paddingTop: 16,
      width: '100%',
      alignItems: 'center'
    },
    viewPadding: {
      height: 16
    },
    viewIcon: {
      marginTop: 10
    },
    containerStatusToday: {
      flex: 1
    }
  });
