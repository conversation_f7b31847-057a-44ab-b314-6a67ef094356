  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.VARIANT_MESSAGE_WAY_FINDING = exports.BottomSheetUnableLoadLocation = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _bottomSheetConfirm = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "BottomSheetUnableLoadLocation__";
  var iconBottomSheet = {
    width: 70,
    height: 70
  };
  var VARIANT_MESSAGE_WAY_FINDING = exports.VARIANT_MESSAGE_WAY_FINDING = /*#__PURE__*/function (VARIANT_MESSAGE_WAY_FINDING) {
    VARIANT_MESSAGE_WAY_FINDING["UNAVAILABLESERVICE"] = "UNAVAILABLESERVICE";
    VARIANT_MESSAGE_WAY_FINDING["NOTFOUND"] = "NOTFOUND";
    return VARIANT_MESSAGE_WAY_FINDING;
  }({});
  var ComponentBottomSheet = function ComponentBottomSheet(_, ref) {
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isShow = _useState2[0],
      showModal = _useState2[1];
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr16 = !(0, _lodash.isEmpty)(messageCommon) && (messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR16";
    }));
    var VARIANT_MESSAGE = {
      NOTFOUND: {
        title: (ehr16 == null ? undefined : ehr16.header) || (0, _i18n.translate)("unableLoadDirectionLocation.title"),
        message: (ehr16 == null ? undefined : ehr16.subHeader) || (0, _i18n.translate)("unableLoadDirectionLocation.description"),
        firstButton: (ehr16 == null ? undefined : ehr16.buttonLabel) || (0, _i18n.translate)("unableLoadDirectionLocation.button")
      }
    };
    var variantSelected = VARIANT_MESSAGE[VARIANT_MESSAGE_WAY_FINDING.NOTFOUND];
    var show = function show() {
      showModal(true);
    };
    var handleAccept = function handleAccept() {
      showModal(false);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        show: show
      };
    });
    var iconComponent = (0, _jsxRuntime.jsx)(_baseImage.default, {
      source: {
        uri: (0, _mediaHelper.handleImageUrl)(ehr16 == null ? undefined : ehr16.icon)
      },
      style: iconBottomSheet
    });
    return (0, _jsxRuntime.jsx)(_bottomSheetConfirm.BottomSheetConfirm, {
      visible: isShow,
      hideOnConfirm: true,
      title: variantSelected.title,
      message: variantSelected.message,
      confirmButtonText: variantSelected.firstButton,
      height: 274,
      onHide: handleAccept,
      onConfirm: handleAccept,
      testID: `${COMPONENT_NAME}BottomSheetUnableLoadLocation`,
      isClose: true,
      hasCancel: false,
      icon: (0, _utils.handleCondition)(!(0, _lodash.isEmpty)(ehr16 == null ? undefined : ehr16.icon), iconComponent, (0, _jsxRuntime.jsx)(_icons.Info, {}))
    });
  };
  var BottomSheetUnableLoadLocation = exports.BottomSheetUnableLoadLocation = (0, _react.forwardRef)(ComponentBottomSheet);
