  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.tierTextContainer = exports.tierText = exports.tierLabel = exports.tierIcon = exports.tierContainer = exports.qrStyle = exports.numberTextContainer = exports.numberText = exports.numberLabel = exports.nameStyle = exports.loadingTitle = exports.loadingTierText = exports.loadingTierImg = exports.loadingTierContainer = exports.loadingQR = exports.loadingNumber = exports.loadingCardContainer = exports.copyStyle = exports.container = exports.cardContainer = exports.bannerRewardsCatalogStyles = exports.backgroundImageContainer = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var container = exports.container = {
    paddingTop: 50,
    paddingHorizontal: 24,
    minHeight: 344
  };
  var backgroundImageContainer = exports.backgroundImageContainer = {
    width: "100%",
    height: 300,
    position: "absolute",
    borderBottomLeftRadius: 40
  };
  var cardContainer = exports.cardContainer = Object.assign({}, _theme.shadow.primaryShadow, {
    padding: 24,
    alignItems: "center",
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16
  });
  var nameStyle = exports.nameStyle = Object.assign({}, _text.presets.subTitleBold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var qrStyle = exports.qrStyle = {
    marginTop: 16,
    minHeight: 140
  };
  var tierContainer = exports.tierContainer = {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 19
  };
  var tierIcon = exports.tierIcon = {
    width: 40,
    height: 40,
    resizeMode: "contain"
  };
  var tierTextContainer = exports.tierTextContainer = Object.assign({}, _text.presets.bodyTextRegular, {
    marginStart: 4,
    color: _theme.color.palette.almostBlackGrey
  });
  var tierLabel = exports.tierLabel = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.almostBlackGrey
  });
  var tierText = exports.tierText = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var numberTextContainer = exports.numberTextContainer = {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 18
  };
  var numberLabel = exports.numberLabel = Object.assign({}, _text.presets.caption1Regular, {
    color: _theme.color.palette.almostBlackGrey
  });
  var numberText = exports.numberText = Object.assign({}, _text.presets.caption1Bold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var copyStyle = exports.copyStyle = {
    marginStart: 8
  };
  var loadingCardContainer = exports.loadingCardContainer = Object.assign({}, cardContainer, {
    paddingTop: 29,
    paddingBottom: 31.5,
    alignItems: "center",
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16,
    marginBottom: 20
  });
  var loadingTitle = exports.loadingTitle = {
    width: 63,
    height: 13,
    borderRadius: 4
  };
  var loadingQR = exports.loadingQR = {
    width: 132,
    height: 132,
    marginTop: 23
  };
  var loadingTierContainer = exports.loadingTierContainer = {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 18
  };
  var loadingTierImg = exports.loadingTierImg = {
    width: 36,
    height: 36,
    borderRadius: 40
  };
  var loadingTierText = exports.loadingTierText = {
    width: 83,
    height: 13,
    borderRadius: 4,
    marginStart: 8
  };
  var loadingNumber = exports.loadingNumber = {
    width: 206,
    height: 13,
    marginTop: 21.5,
    borderRadius: 4
  };
  var bannerRewardsCatalogStyles = exports.bannerRewardsCatalogStyles = _reactNative.StyleSheet.create({
    bannerContainer: Object.assign({}, _theme.shadow.primaryShadow, {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      flexDirection: "row",
      marginBottom: 16,
      marginTop: 24,
      padding: 8,
      width: "100%"
    }),
    description: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontStyle: "normal",
      fontWeight: "400",
      lineHeight: 18
    }),
    image: {
      borderRadius: 12,
      height: 70,
      width: 70
    },
    leftContent: {
      width: "27%"
    },
    rightContent: {
      width: "73%"
    },
    title: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      lineHeight: 20,
      marginBottom: 4
    })
  });
