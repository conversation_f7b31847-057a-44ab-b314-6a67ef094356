  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeCollapsible = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CollapsibleComponent = function CollapsibleComponent(_ref) {
    var title = _ref.title,
      content = _ref.content,
      _ref$defaultCollapse = _ref.defaultCollapse,
      defaultCollapse = _ref$defaultCollapse === undefined ? true : _ref$defaultCollapse,
      customStyleTitle = _ref.customStyleTitle;
    var _useState = (0, _react.useState)(defaultCollapse),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      collapsed = _useState2[0],
      setCollapse = _useState2[1];
    var animatedController = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var arrowAngle = animatedController.interpolate({
      inputRange: [0, 1],
      outputRange: ["0rad", `${Math.PI}rad`]
    });
    (0, _react.useEffect)(function () {
      if (collapsed) {
        _reactNative2.Animated.timing(animatedController, {
          duration: 300,
          toValue: 0,
          easing: _reactNative2.Easing.bezier(0.4, 0.0, 0.2, 1),
          useNativeDriver: false
        }).start();
      } else {
        _reactNative2.Animated.timing(animatedController, {
          duration: 300,
          toValue: 1,
          easing: _reactNative2.Easing.bezier(0.4, 0.0, 0.2, 1),
          useNativeDriver: false
        }).start();
      }
    }, [collapsed]);
    var toogleCollapse = function toogleCollapse() {
      setCollapse(!collapsed);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.wrapItemCollapse,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: function onPress() {
          return toogleCollapse();
        },
        activeOpacity: 0.5,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.titleContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            text: title,
            style: [styles.title, customStyleTitle]
          }), (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
            style: {
              transform: [{
                rotateZ: arrowAngle
              }]
            },
            children: (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
              color: _theme.color.palette.lightPurple
            })
          })]
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeCollapsible.default, {
        collapsed: collapsed,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.contentCollapse,
          children: content
        })
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    contentCollapse: {},
    title: {
      width: "90%"
    },
    titleContainer: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 24
    },
    wrapItemCollapse: {
      paddingVertical: 5
    }
  });
  var _default = exports.default = CollapsibleComponent;
