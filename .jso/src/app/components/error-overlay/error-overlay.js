  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorOverlayVariant = exports.ErrorOverlay = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _errorOverlayHeader = _$$_REQUIRE(_dependencyMap[8]);
  var _errorOverlayVariant = _$$_REQUIRE(_dependencyMap[9]);
  var _errorOverlayVariant2 = _$$_REQUIRE(_dependencyMap[10]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _errorOverlayVariantSection = _$$_REQUIRE(_dependencyMap[14]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[15]);
  var _errorCode = _$$_REQUIRE(_dependencyMap[16]);
  var _utils = _$$_REQUIRE(_dependencyMap[17]);
  var _errorOverlayBottomSheet = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  var _excluded = ["variant", "visible", "hideScreenHeader", "header", "headerBackgroundColor", "headerText", "headerTx", "storyMode", "onBack", "testID", "accessibilityLabel", "overlayStyle", "extendCode", "customBackButton", "ignoreShowNoInternet"]; // page level
  // unsuccessfull page
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ErrorOverlayVariant = exports.ErrorOverlayVariant = /*#__PURE__*/function (ErrorOverlayVariant) {
    ErrorOverlayVariant[ErrorOverlayVariant["VARIANT1"] = 0] = "VARIANT1";
    ErrorOverlayVariant[ErrorOverlayVariant["VARIANT2"] = 1] = "VARIANT2";
    ErrorOverlayVariant[ErrorOverlayVariant["VARIANT3"] = 2] = "VARIANT3";
    ErrorOverlayVariant[ErrorOverlayVariant["VARIANTSECTION"] = 3] = "VARIANTSECTION";
    ErrorOverlayVariant[ErrorOverlayVariant["BOTTOM_SHEET"] = 4] = "BOTTOM_SHEET";
    return ErrorOverlayVariant;
  }({});
  var ErrorOverlay = exports.ErrorOverlay = function ErrorOverlay(props) {
    var variant = props.variant,
      visible = props.visible,
      _props$hideScreenHead = props.hideScreenHeader,
      hideScreenHeader = _props$hideScreenHead === undefined ? true : _props$hideScreenHead,
      header = props.header,
      headerBackgroundColor = props.headerBackgroundColor,
      headerText = props.headerText,
      headerTx = props.headerTx,
      storyMode = props.storyMode,
      onBack = props.onBack,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ErrorOverlay" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ErrorOverlay" : _props$accessibilityL,
      overlayStyle = props.overlayStyle,
      extendCode = props.extendCode,
      customBackButton = props.customBackButton,
      _props$ignoreShowNoIn = props.ignoreShowNoInternet,
      ignoreShowNoInternet = _props$ignoreShowNoIn === undefined ? false : _props$ignoreShowNoIn,
      rest = (0, _objectWithoutProperties2.default)(props, _excluded);
    var navigation = storyMode ? null : (0, _native.useNavigation)();
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNetworkConnected = _useState2[0],
      setNetworkConnected = _useState2[1];
    var previousValues = (0, _react.useRef)(null);
    (0, _react.useLayoutEffect)(function () {
      var _previousValues$curre;
      if (hideScreenHeader && !!(previousValues != null && (_previousValues$curre = previousValues.current) != null && _previousValues$curre.visible) !== !!visible) {
        navigation == null || navigation.setOptions({
          headerShown: !visible
        });
      }
      return function () {
        previousValues.current = Object.assign({}, (previousValues == null ? undefined : previousValues.current) || {}, {
          visible: visible
        });
      };
    }, [visible, hideScreenHeader]);
    (0, _react.useEffect)(function () {
      var checkConnection = /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          setNetworkConnected(isConnected);
        });
        return function checkConnection() {
          return _ref.apply(this, arguments);
        };
      }();
      checkConnection();
    }, [visible]);
    var Component = function Component() {
      var errorPage = errorData != null && errorData.length ? errorData.find(function (el) {
        return el.code === (extendCode || _errorCode.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL);
      }) : null;
      var errorUnsuccessfulPage = errorData != null && errorData.length ? errorData.find(function (el) {
        return el.code === (extendCode || _errorCode.ERROR_HANDLING_CODE.ERROR_UNSUCCESSFUL_PAGE);
      }) : null;
      var errorSection = errorData != null && errorData.length ? errorData.find(function (el) {
        return el.code === (extendCode || _errorCode.ERROR_HANDLING_CODE.ERROR_SECTION_LEVEL);
      }) : null;
      if (variant === ErrorOverlayVariant.VARIANT1) {
        var _errorPage$navigation, _errorPage$navigation2, _errorPage$navigation3, _errorPage$navigation4;
        var agrument = (0, _utils.simpleCondition)({
          condition: errorPage,
          ifValue: Object.assign({}, rest, {
            title: errorPage == null ? undefined : errorPage.header,
            message: errorPage == null ? undefined : errorPage.subHeader,
            reloadText: errorPage == null ? undefined : errorPage.buttonLabel,
            icon: errorPage == null ? undefined : errorPage.icon,
            sub: errorPage == null ? undefined : errorPage.buttonLabel2,
            navigationType: errorPage == null || (_errorPage$navigation = errorPage.navigationFirst) == null ? undefined : _errorPage$navigation.type,
            navigationValue: errorPage == null || (_errorPage$navigation2 = errorPage.navigationFirst) == null ? undefined : _errorPage$navigation2.value,
            subNavigationType: errorPage == null || (_errorPage$navigation3 = errorPage.navigationSecond) == null ? undefined : _errorPage$navigation3.type,
            subNavigationValue: errorPage == null || (_errorPage$navigation4 = errorPage.navigationSecond) == null ? undefined : _errorPage$navigation4.value
          }),
          elseValue: rest
        });
        return (0, _jsxRuntime.jsx)(_errorOverlayVariant.ErrorOverlayVariant1, Object.assign({}, agrument, {
          testID: `${testID}__ErrorOverlayVariant1`,
          accessibilityLabel: `${accessibilityLabel}__ErrorOverlayVariant1`
        }));
      } else if (variant === ErrorOverlayVariant.VARIANT2) {
        var _agrument = (0, _utils.simpleCondition)({
          condition: errorUnsuccessfulPage,
          ifValue: Object.assign({}, rest, {
            title: errorUnsuccessfulPage == null ? undefined : errorUnsuccessfulPage.header,
            message: errorUnsuccessfulPage == null ? undefined : errorUnsuccessfulPage.subHeader,
            reloadText: errorUnsuccessfulPage == null ? undefined : errorUnsuccessfulPage.buttonLabel,
            icon: errorUnsuccessfulPage == null ? undefined : errorUnsuccessfulPage.icon
          }),
          elseValue: rest
        });
        return (0, _jsxRuntime.jsx)(_errorOverlayVariant2.ErrorOverlayVariant2, Object.assign({}, _agrument, {
          testID: `${testID}__ErrorOverlayVariant2`,
          accessibilityLabel: `${accessibilityLabel}__ErrorOverlayVariant2`
        }));
      } else if (variant === ErrorOverlayVariant.VARIANTSECTION) {
        var _errorSection$navigat, _errorSection$navigat2;
        var _agrument2 = (0, _utils.simpleCondition)({
          condition: errorSection,
          ifValue: Object.assign({}, rest, {
            title: errorSection == null ? undefined : errorSection.header,
            message: errorSection == null ? undefined : errorSection.subHeader,
            reloadText: errorSection == null ? undefined : errorSection.buttonLabel,
            navigationType: errorSection == null || (_errorSection$navigat = errorSection.navigationFirst) == null ? undefined : _errorSection$navigat.type,
            navigationValue: errorSection == null || (_errorSection$navigat2 = errorSection.navigationFirst) == null ? undefined : _errorSection$navigat2.value
          }),
          elseValue: rest
        });
        return (0, _jsxRuntime.jsx)(_errorOverlayVariantSection.ErrorOverlayVariantSection, Object.assign({}, _agrument2, {
          testID: `${testID}__ErrorOverlayVariantSection`,
          accessibilityLabel: `${accessibilityLabel}__ErrorOverlayVariantSection`
        }));
      } else if (variant === ErrorOverlayVariant.BOTTOM_SHEET) {
        var argument = (0, _utils.simpleCondition)({
          condition: errorPage,
          ifValue: Object.assign({}, rest, {
            icon: errorPage == null ? undefined : errorPage.icon
          }),
          elseValue: rest
        });
        return (0, _jsxRuntime.jsx)(_errorOverlayBottomSheet.ErrorOverlayBottomSheet, Object.assign({}, argument, {
          testID: `${testID}__ErrorOverlayVariantBottomSheet`,
          accessibilityLabel: `${accessibilityLabel}__ErrorOverlayVariantBottomSheet`
        }));
      } else {
        var _errorPage$navigation5, _errorPage$navigation6, _errorPage$navigation7, _errorPage$navigation8;
        var _agrument3 = (0, _utils.simpleCondition)({
          condition: errorPage,
          ifValue: Object.assign({}, rest, {
            title: errorPage == null ? undefined : errorPage.header,
            message: errorPage == null ? undefined : errorPage.subHeader,
            reloadText: errorPage == null ? undefined : errorPage.buttonLabel,
            icon: errorPage == null ? undefined : errorPage.icon,
            sub: errorPage == null ? undefined : errorPage.buttonLabel2,
            navigationType: errorPage == null || (_errorPage$navigation5 = errorPage.navigationFirst) == null ? undefined : _errorPage$navigation5.type,
            navigationValue: errorPage == null || (_errorPage$navigation6 = errorPage.navigationFirst) == null ? undefined : _errorPage$navigation6.value,
            subNavigationType: errorPage == null || (_errorPage$navigation7 = errorPage.navigationSecond) == null ? undefined : _errorPage$navigation7.type,
            subNavigationValue: errorPage == null || (_errorPage$navigation8 = errorPage.navigationSecond) == null ? undefined : _errorPage$navigation8.value
          }),
          elseValue: rest
        });
        return (0, _jsxRuntime.jsx)(_errorOverlayVariant.ErrorOverlayVariant1, Object.assign({}, _agrument3, {
          testID: `${testID}__ErrorOverlayVariantDefault`,
          accessibilityLabel: `${accessibilityLabel}__ErrorOverlayVariantDefault`
        }));
      }
    };
    var containerOverlayStyle = (0, _utils.simpleCondition)({
      condition: storyMode,
      ifValue: styles.storyModeStyle,
      elseValue: styles.overlayStyle
    });
    return visible ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: overlayStyle || containerOverlayStyle,
      children: [header && (0, _jsxRuntime.jsx)(_errorOverlayHeader.ErrorOverlayHeader, {
        title: headerText,
        titleTx: headerTx,
        headerBackgroundColor: headerBackgroundColor,
        onBack: onBack,
        customBackButton: customBackButton,
        testID: `${testID}__ErrorOverlayHeader`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlayHeader`
      }), (0, _utils.handleCondition)(isNetworkConnected || ignoreShowNoInternet, (0, _jsxRuntime.jsx)(Component, {}), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, Object.assign({}, rest, {
        testID: `${testID}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlayNoConnection`,
        hideScreenHeader: hideScreenHeader
      })))]
    }) : null;
  };
