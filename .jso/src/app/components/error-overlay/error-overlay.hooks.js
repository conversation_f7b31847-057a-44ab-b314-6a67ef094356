  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useConfigIcon = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _envParams = _$$_REQUIRE(_dependencyMap[4]);
  var useConfigIcon = exports.useConfigIcon = function useConfigIcon(params) {
    var icon = params.icon;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      iconUri = _useState2[0],
      setIconUri = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isLoadingIcon = _useState4[0],
      setLoadingIcon = _useState4[1];
    var _useState5 = (0, _react.useState)({
        width: 0,
        height: 0,
        resizeMode: "contain"
      }),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      iconStyle = _useState6[0],
      setIconStyle = _useState6[1];
    (0, _react.useEffect)(function () {
      var isMounted = true;
      if (icon === "") {
        if (isMounted) {
          setLoadingIcon(true);
        }
      } else if (icon) {
        var _env, _env2;
        setIconUri(`${(_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL}${icon}`);
        _reactNative.Image.getSize(`${(_env2 = (0, _envParams.env)()) == null ? undefined : _env2.AEM_URL}${icon}`, function (width, height) {
          var maxWidth = 191;
          var newWidth = width;
          var newHeight = height;
          var rate = width / height;
          if (width > maxWidth) {
            newWidth = maxWidth;
            newHeight = maxWidth / rate;
          }
          if (isMounted) {
            setIconStyle({
              width: newWidth,
              height: newHeight,
              resizeMode: "contain"
            });
          }
        });
        if (isMounted) {
          setLoadingIcon(false);
        }
      } else {
        if (isMounted) {
          setLoadingIcon(false);
        }
      }
      return function () {
        isMounted = false;
      };
    }, [icon]);
    return {
      iconStyle: iconStyle,
      iconUri: iconUri,
      isLoadingIcon: isLoadingIcon
    };
  };
