  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.StaffPrivileges = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[2]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var StaffPrivileges = exports.StaffPrivileges = function StaffPrivileges(props) {
    return (0, _jsxRuntime.jsxs)(_reactNativeSvg.default, Object.assign({
      width: 347,
      height: 96,
      viewBox: "0 0 347 96",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, props, {
      children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        d: "M2 94H345V21.2C345 14.4794 345 11.1191 343.692 8.55211C342.542 6.29417 340.706 4.4584 338.448 3.30792C335.881 2 332.521 2 325.8 2H21.2C14.4794 2 11.1191 2 8.55211 3.30792C6.29417 4.4584 4.4584 6.29417 3.30792 8.55211C2 11.1191 2 14.4794 2 21.2V94Z",
        fill: "url(#paint0_linear_23303_9522)"
      }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        d: "M2 95H1V94V21.2V21.1539C0.999996 17.8326 0.999994 15.2787 1.16681 13.237C1.33535 11.1741 1.67915 9.54606 2.41692 8.09812C3.66327 5.65202 5.65202 3.66327 8.09812 2.41692C9.54606 1.67915 11.1741 1.33536 13.237 1.16681C15.2787 0.999992 17.8325 1 21.1539 1H21.2H325.8H325.846C329.167 1 331.721 0.999992 333.763 1.16681C335.826 1.33536 337.454 1.67915 338.902 2.41692C341.348 3.66327 343.337 5.65202 344.583 8.09812C345.321 9.54606 345.665 11.1741 345.833 13.237C346 15.2789 346 17.833 346 21.1547V21.2V94V95H345H2Z",
        stroke: "url(#paint1_linear_23303_9522)",
        strokeOpacity: 0.8,
        strokeWidth: 2
      }), (0, _jsxRuntime.jsxs)(_reactNativeSvg.Defs, {
        children: [(0, _jsxRuntime.jsxs)(_reactNativeSvg.LinearGradient, {
          id: "paint0_linear_23303_9522",
          x1: 345,
          y1: 90.8883,
          x2: 219.139,
          y2: -120.83,
          gradientUnits: "userSpaceOnUse",
          children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            stopColor: "#682EB1"
          }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            offset: 1,
            stopColor: "#E14194"
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNativeSvg.LinearGradient, {
          id: "paint1_linear_23303_9522",
          x1: 68.5,
          y1: -22.6829,
          x2: 183.443,
          y2: 52.7042,
          gradientUnits: "userSpaceOnUse",
          children: [(0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            stopColor: "white",
            stopOpacity: 0.2
          }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            offset: 0.515,
            stopColor: "white"
          }), (0, _jsxRuntime.jsx)(_reactNativeSvg.Stop, {
            offset: 1,
            stopColor: "white",
            stopOpacity: 0
          })]
        })]
      })]
    }));
  };
