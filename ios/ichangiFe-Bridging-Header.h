//
//  ichangiFe-Bridging-Header.h
//  ichangiFe
//
//  Created by <PERSON><PERSON><PERSON> on 29/08/2022.
//

#ifndef ichangiFe_Bridging_Header_h
#define ichangiFe_Bridging_Header_h

//@import Foundation;
//@import UIKit;
//@import CoreLocation;
//@import AVFoundation;

#import <React/RCTBridge.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import "React/RCTUIManager.h"
#import <React/RCTComponent.h>
#import <React/RCTViewManager.h>
#import <React/RCTDevLoadingView.h>

#import "React/RCTEventDispatcher.h"
#import "React/RCTUtils.h"
#import "React/RCTConvert.h"
#import "React/RCTEventEmitter.h"

#import "BrazeReactBridge.h"
#import "BrazeReactUtils.h"
//#import "BrazeReactDelegate.h"

#import "ACPCore.h"
#import "ACPUserProfile.h"
#import "ACPIdentity.h"
#import "ACPLifecycle.h"
#import "ACPSignal.h"
#import "ACPMobileServices.h"
#import "ACPAnalytics.h"
#import "ACPCampaign.h"
#import "ACPTarget.h"

#import "RNEventEmitter.h"

//#import <LiquidPayWidget/LiquidPayWidget.h>
//@interface RCTCalendarModule : NSObject <RCTBridgeModule>

@interface ClearWebviewCache : NSObject <RCTBridgeModule>
@end

#endif /* ichangiFe_Bridging_Header_h */
